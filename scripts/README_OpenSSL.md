# Сборка OpenSSL для Asta Android

Этот документ описывает процесс сборки OpenSSL 3.2.1 с флагом `-fPIC` для Android приложения Asta.

## Требования

### Системные требования
- macOS или Linux
- Android NDK (рекомендуется версия 25 или новее)
- curl для скачивания исходников
- make и стандартные утилиты сборки

### Переменные окружения
Перед сборкой необходимо установить переменную окружения `ANDROID_NDK_ROOT`:

```bash
# Для macOS (пример)
export ANDROID_NDK_ROOT=/Users/<USER>/Library/Android/sdk/ndk/25.2.9519653

# Для Linux (пример)
export ANDROID_NDK_ROOT=/home/<USER>/Android/Sdk/ndk/25.2.9519653

# Добавьте в ~/.bashrc или ~/.zshrc для постоянного использования
echo 'export ANDROID_NDK_ROOT=/path/to/your/ndk' >> ~/.bashrc
```

## Использование

### 1. Настройка переменных окружения

Сначала настройте переменные окружения:

```bash
./scripts/setup_env.sh
```

Или установите вручную:
```bash
export ANDROID_NDK_ROOT=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
```

### 2. Проверка текущих библиотек

Проверьте текущие библиотеки OpenSSL:

```bash
./scripts/check_openssl_libs.sh
```

Этот скрипт покажет:
- Размер библиотек
- Архитектуру
- Информацию о символах
- Проверку флага -fPIC

### 3. Сборка новых библиотек

Для сборки OpenSSL с флагом `-fPIC`:

```bash
./scripts/build_openssl_simple.sh
```

Скрипт выполнит следующие действия:
1. Проверит переменные окружения
2. Скачает OpenSSL 3.2.1
3. Сконфигурирует сборку для Android aarch64
4. Соберет библиотеки с флагом `-fPIC`
5. Скопирует `libssl.a` и `libcrypto.a` в проект

### 4. Проверка результата

После сборки снова запустите проверку:

```bash
./scripts/check_openssl_libs.sh
```

## Конфигурация сборки

### Параметры OpenSSL
Скрипт использует следующие параметры конфигурации:
- `android-arm64` - целевая платформа
- `no-shared` - только статические библиотеки
- `no-tests` - без тестов
- `no-ui-console` - без консольного UI
- `no-asm` - без ассемблерных оптимизаций
- `-fPIC` - позиционно-независимый код

### Флаги компиляции
- `CFLAGS="-fPIC -O3"`
- `CXXFLAGS="-fPIC -O3"`
- `API=21` - минимальный уровень Android API

## Интеграция в проект

Библиотеки автоматически копируются в:
```
engine/engine-impl/src/main/jniLibs/aarch64/
├── libssl.a
└── libcrypto.a
```

В `CMakeLists.txt` они уже подключены:
```cmake
SET(WHITEMCLIENT_DEPS_LIBS
    # ... другие библиотеки ...
    ssl
    crypto
    # ... другие библиотеки ...
)
```

## Устранение проблем

### Ошибка "ANDROID_NDK_ROOT не установлен"
Убедитесь, что переменная окружения установлена правильно:
```bash
echo $ANDROID_NDK_ROOT
ls -la $ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/
```

### Ошибка "Тулчейн не найден"
Проверьте структуру NDK:
```bash
ls -la $ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/
```

Должна быть директория `darwin-x86_64` (macOS) или `linux-x86_64` (Linux).

### Ошибки сборки
1. Убедитесь, что у вас достаточно места на диске
2. Проверьте права доступа к директориям
3. Убедитесь, что все зависимости установлены

### Проверка флага -fPIC
Если нужно убедиться, что библиотеки собраны с `-fPIC`:
```bash
# Проверка через readelf (если доступен)
readelf -d engine/engine-impl/src/main/jniLibs/aarch64/libssl.a

# Проверка через objdump (если доступен)
objdump -R engine/engine-impl/src/main/jniLibs/aarch64/libssl.a
```

## Дополнительная информация

### Версии
- OpenSSL: 3.2.1
- Android API: 21 (минимум)
- Архитектура: aarch64 (arm64-v8a)

### Размер библиотек
Ожидаемые размеры после сборки:
- `libssl.a`: ~1-2 MB
- `libcrypto.a`: ~4-6 MB

### Безопасность
OpenSSL 3.2.1 включает последние исправления безопасности. Регулярно обновляйте версию для получения новых исправлений.
