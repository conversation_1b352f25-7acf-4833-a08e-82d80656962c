# Сборка Boost для Asta Android

Этот документ описывает процесс сборки Boost 1.82.0 с флагом `-fPIC` для Android приложения Asta.

## Требования

### Системные требования
- macOS или Linux
- Android NDK (рекомендуется версия 25 или новее)
- curl для скачивания исходников
- make и стандартные утилиты сборки

### Переменные окружения
Перед сборкой необходимо установить переменную окружения `ANDROID_NDK_ROOT`:

```bash
# Настройка через скрипт
./scripts/setup_env.sh

# Или вручную
export ANDROID_NDK_ROOT=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
```

## Использование

### 1. Проверка текущих библиотек

Сначала проверьте текущие библиотеки Boost:

```bash
./scripts/check_boost_libs.sh
```

Этот скрипт покажет:
- Размер библиотек
- Архитектуру
- Информацию о символах
- Предполагаемую версию Boost
- Проверку флага -fPIC

### 2. Сборка новых библиотек

Для сборки Boost с флагом `-fPIC`:

```bash
./scripts/build_boost_simple.sh
```

Скрипт выполнит следующие действия:
1. Проверит переменные окружения
2. Скачает Boost 1.82.0
3. Сконфигурирует сборку для Android aarch64
4. Соберет библиотеки с флагом `-fPIC`
5. Скопирует библиотеки в проект

### 3. Проверка результата

После сборки снова запустите проверку:

```bash
./scripts/check_boost_libs.sh
```

## Конфигурация сборки

### Параметры Boost
Скрипт использует следующие параметры конфигурации:
- `toolset=clang-android` - использование Android Clang
- `target-os=android` - целевая ОС
- `architecture=arm` - архитектура ARM
- `address-model=64` - 64-битная модель
- `link=static` - только статические библиотеки
- `runtime-link=static` - статическая линковка runtime
- `threading=multi` - поддержка многопоточности
- `variant=release` - релизная сборка

### Флаги компиляции
- `cxxflags="-fPIC -O3 -D__ANDROID_API__=21 -DANDROID"`
- `linkflags="-static"`

### Собираемые библиотеки
Скрипт собирает следующие библиотеки (из CMakeLists.txt):
- boost_locale
- boost_thread
- boost_atomic
- boost_timer
- boost_container
- boost_program_options
- boost_date_time
- boost_random
- boost_exception
- boost_filesystem
- boost_iostreams
- boost_regex
- boost_system

## Интеграция в проект

Библиотеки автоматически копируются в:
```
engine/engine-impl/src/main/jniLibs/aarch64/
├── libboost_locale.a
├── libboost_thread.a
├── libboost_atomic.a
├── libboost_timer.a
├── libboost_container.a
├── libboost_program_options.a
├── libboost_date_time.a
├── libboost_random.a
├── libboost_exception.a
├── libboost_filesystem.a
├── libboost_iostreams.a
├── libboost_regex.a
└── libboost_system.a
```

В `CMakeLists.txt` они уже подключены:
```cmake
SET(WHITEMCLIENT_DEPS_LIBS
    boost_locale
    boost_thread
    boost_atomic
    boost_timer
    boost_container
    boost_program_options
    boost_date_time
    boost_random
    boost_exception
    boost_filesystem
    boost_iostreams
    boost_regex
    boost_system
    # ... другие библиотеки ...
)
```

## Устранение проблем

### Ошибка "ANDROID_NDK_ROOT не установлен"
```bash
./scripts/setup_env.sh
```

### Ошибки сборки
1. Убедитесь, что у вас достаточно места на диске (Boost требует ~2GB)
2. Проверьте права доступа к директориям
3. Убедитесь, что все зависимости установлены

### Проверка флага -fPIC
```bash
# Проверка через objdump (если доступен)
objdump -R engine/engine-impl/src/main/jniLibs/aarch64/libboost_system.a

# Проверка через readelf (если доступен)
readelf -d engine/engine-impl/src/main/jniLibs/aarch64/libboost_system.a
```

### Долгая сборка
Сборка Boost может занять 20-60 минут в зависимости от мощности системы. Это нормально.

## Дополнительная информация

### Версии
- Boost: 1.82.0
- Android API: 21 (минимум)
- Архитектура: aarch64 (arm64-v8a)

### Ожидаемые размеры библиотек
После сборки ожидаемые размеры:
- `libboost_locale.a`: ~1.2MB
- `libboost_program_options.a`: ~1.1MB
- `libboost_regex.a`: ~700KB
- `libboost_filesystem.a`: ~320KB
- `libboost_thread.a`: ~250KB
- Остальные: 10KB-200KB каждая

### Совместимость
Boost 1.82.0 совместим с:
- Android API 21+
- NDK 25+
- C++17 и выше

### Оптимизация
Библиотеки собираются с оптимизацией `-O3` для максимальной производительности.

## Альтернативные версии

Если нужна другая версия Boost, измените в скрипте:
```bash
BOOST_VERSION="1.81.0"  # Например, для версии 1.81.0
BOOST_VERSION_UNDERSCORE="1_81_0"
```

Поддерживаемые версии: 1.75.0 - 1.82.0
