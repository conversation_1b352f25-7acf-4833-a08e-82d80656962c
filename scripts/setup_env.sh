#!/bin/bash

# Скрипт для настройки переменных окружения для сборки OpenSSL
# Автор: Asta Android Team

# Цвета для вывода
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Определяем путь к Android NDK
ANDROID_NDK_PATH="/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393"

echo_info "Настройка переменных окружения для сборки OpenSSL"
echo_info "Android NDK: $ANDROID_NDK_PATH"

# Экспортируем переменную для текущей сессии
export ANDROID_NDK_ROOT="$ANDROID_NDK_PATH"

echo_info "Переменная ANDROID_NDK_ROOT установлена для текущей сессии"

# Определяем shell
if [[ "$SHELL" == *"zsh"* ]]; then
    PROFILE_FILE="$HOME/.zshrc"
    SHELL_NAME="zsh"
elif [[ "$SHELL" == *"bash"* ]]; then
    PROFILE_FILE="$HOME/.bash_profile"
    SHELL_NAME="bash"
else
    PROFILE_FILE="$HOME/.profile"
    SHELL_NAME="shell"
fi

echo_info "Обнаружен $SHELL_NAME, файл профиля: $PROFILE_FILE"

# Проверяем, есть ли уже переменная в профиле
if grep -q "ANDROID_NDK_ROOT" "$PROFILE_FILE" 2>/dev/null; then
    echo_warn "Переменная ANDROID_NDK_ROOT уже есть в $PROFILE_FILE"
    echo_info "Текущие настройки:"
    grep "ANDROID_NDK_ROOT" "$PROFILE_FILE"
else
    echo_info "Добавляем переменную ANDROID_NDK_ROOT в $PROFILE_FILE"
    echo "" >> "$PROFILE_FILE"
    echo "# Android NDK для сборки OpenSSL" >> "$PROFILE_FILE"
    echo "export ANDROID_NDK_ROOT=\"$ANDROID_NDK_PATH\"" >> "$PROFILE_FILE"
    echo_info "Переменная добавлена в $PROFILE_FILE"
fi

echo ""
echo_info "Готово! Теперь вы можете:"
echo_info "1. Перезапустить терминал, или"
echo_info "2. Выполнить: source $PROFILE_FILE"
echo_info "3. Запустить сборку: ./scripts/build_openssl_simple.sh"

echo ""
echo_info "Для проверки текущих настроек: echo \$ANDROID_NDK_ROOT"
