#!/bin/bash

# Упрощенный скрипт для сборки Boost 1.82.0 для Android aarch64 с флагом -fPIC
# Автор: Asta Android Team

set -e

# Конфигурация
BOOST_VERSION="1.82.0"
BOOST_VERSION_UNDERSCORE="1_82_0"
ANDROID_API_LEVEL="21"

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Список библиотек для сборки (из CMakeLists.txt)
BOOST_LIBS=(
    "locale"
    "thread"
    "atomic"
    "timer"
    "container"
    "program_options"
    "date_time"
    "random"
    "exception"
    "filesystem"
    "iostreams"
    "regex"
    "system"
)

# Проверка переменных окружения
check_environment() {
    echo_info "Проверка переменных окружения..."
    
    if [ -z "$ANDROID_NDK_ROOT" ]; then
        echo_error "ANDROID_NDK_ROOT не установлен!"
        echo_info "Запустите: ./scripts/setup_env.sh"
        exit 1
    fi
    
    if [ ! -d "$ANDROID_NDK_ROOT" ]; then
        echo_error "Директория ANDROID_NDK_ROOT не существует: $ANDROID_NDK_ROOT"
        exit 1
    fi
    
    echo_info "ANDROID_NDK_ROOT: $ANDROID_NDK_ROOT"
}

# Настройка переменных для сборки
setup_build_environment() {
    echo_info "Настройка окружения для сборки..."
    
    # Определяем платформу хоста
    if [[ "$OSTYPE" == "darwin"* ]]; then
        HOST_TAG="darwin-x86_64"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        HOST_TAG="linux-x86_64"
    else
        echo_error "Неподдерживаемая платформа: $OSTYPE"
        exit 1
    fi
    
    # Путь к тулчейну
    export TOOLCHAIN="$ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/$HOST_TAG"
    
    if [ ! -d "$TOOLCHAIN" ]; then
        echo_error "Не найден тулчейн: $TOOLCHAIN"
        exit 1
    fi
    
    # Настройка переменных
    export PATH="$TOOLCHAIN/bin:$PATH"
    export TARGET="aarch64-linux-android"
    export API="$ANDROID_API_LEVEL"
    export AR="$TOOLCHAIN/bin/llvm-ar"
    export CC="$TOOLCHAIN/bin/${TARGET}${API}-clang"
    export CXX="$TOOLCHAIN/bin/${TARGET}${API}-clang++"
    export RANLIB="$TOOLCHAIN/bin/llvm-ranlib"
    export STRIP="$TOOLCHAIN/bin/llvm-strip"
    
    # Проверяем компилятор
    if [ ! -f "$CC" ]; then
        echo_error "Компилятор не найден: $CC"
        exit 1
    fi
    
    echo_info "Компилятор: $CC"
    echo_info "API уровень: $API"
}

# Скачивание Boost
download_boost() {
    echo_info "Скачивание Boost $BOOST_VERSION..."
    
    BOOST_DIR="boost_$BOOST_VERSION_UNDERSCORE"
    BOOST_TAR="$BOOST_DIR.tar.gz"
    BOOST_URL="https://boostorg.jfrog.io/artifactory/main/release/$BOOST_VERSION/source/$BOOST_TAR"
    
    if [ ! -f "$BOOST_TAR" ]; then
        echo_info "Скачиваем $BOOST_URL"
        curl -L -o "$BOOST_TAR" "$BOOST_URL"
    else
        echo_info "Архив уже скачан: $BOOST_TAR"
    fi
    
    if [ -d "$BOOST_DIR" ]; then
        echo_warn "Удаляем существующую директорию $BOOST_DIR"
        rm -rf "$BOOST_DIR"
    fi
    
    echo_info "Распаковываем архив..."
    tar -xzf "$BOOST_TAR"
    
    cd "$BOOST_DIR"
}

# Создание project-config.jam
create_project_config() {
    echo_info "Создание project-config.jam для Android..."
    
    cat > project-config.jam << EOF
import option ;
import feature ;

# Compiler configuration
using clang : android
:
$CXX
:
<archiver>$AR
<ranlib>$RANLIB
<compileflags>-fPIC
<compileflags>-O3
<compileflags>-D__ANDROID_API__=$API
<compileflags>-DANDROID
<compileflags>-ffunction-sections
<compileflags>-funwind-tables
<compileflags>-fstack-protector-strong
<compileflags>-no-canonical-prefixes
<compileflags>-Wa,--noexecstack
<compileflags>-Wformat
<compileflags>-Werror=format-security
;

# Build configuration
option.set keep-going : false ;
EOF

    echo_info "project-config.jam создан"
}

# Сборка Boost
build_boost() {
    echo_info "Конфигурирование Boost для Android aarch64..."
    
    # Создаем project-config.jam
    create_project_config
    
    # Запускаем bootstrap
    echo_info "Запуск bootstrap..."
    ./bootstrap.sh --with-toolset=clang
    
    if [ $? -ne 0 ]; then
        echo_error "Ошибка bootstrap"
        exit 1
    fi
    
    # Создаем список библиотек для сборки
    LIBS_LIST=""
    for lib in "${BOOST_LIBS[@]}"; do
        LIBS_LIST="$LIBS_LIST --with-$lib"
    done
    
    echo_info "Сборка Boost библиотек: ${BOOST_LIBS[*]}"
    
    # Собираем Boost
    ./b2 \
        toolset=clang-android \
        target-os=android \
        architecture=arm \
        address-model=64 \
        binary-format=elf \
        abi=aapcs \
        $LIBS_LIST \
        link=static \
        runtime-link=static \
        threading=multi \
        variant=release \
        cxxflags="-fPIC -O3 -D__ANDROID_API__=$API -DANDROID" \
        linkflags="-static" \
        --stagedir="$(pwd)/stage" \
        -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4) \
        stage
    
    if [ $? -ne 0 ]; then
        echo_error "Ошибка сборки Boost"
        exit 1
    fi
    
    echo_info "Boost собран успешно!"
}

# Копирование библиотек
copy_libraries() {
    echo_info "Копирование библиотек в проект..."
    
    STAGE_DIR="$(pwd)/stage/lib"
    PROJECT_LIBS_DIR="../engine/engine-impl/src/main/jniLibs/aarch64"
    
    # Создаем директорию если не существует
    mkdir -p "$PROJECT_LIBS_DIR"
    
    echo_info "Доступные библиотеки в stage/lib:"
    ls -la "$STAGE_DIR"/libboost_*.a | head -10
    
    # Копируем каждую библиотеку
    COPIED_COUNT=0
    for lib in "${BOOST_LIBS[@]}"; do
        LIB_FILE="libboost_${lib}.a"
        STAGE_LIB="$STAGE_DIR/$LIB_FILE"
        PROJECT_LIB="$PROJECT_LIBS_DIR/$LIB_FILE"
        
        if [ -f "$STAGE_LIB" ]; then
            # Создаем резервную копию если библиотека существует
            if [ -f "$PROJECT_LIB" ]; then
                echo_info "Создаем резервную копию $LIB_FILE"
                cp "$PROJECT_LIB" "$PROJECT_LIB.backup.$(date +%Y%m%d_%H%M%S)"
            fi
            
            # Копируем новую библиотеку
            cp "$STAGE_LIB" "$PROJECT_LIB"
            echo_info "✅ Скопирована: $LIB_FILE ($(ls -lh "$PROJECT_LIB" | awk '{print $5}'))"
            COPIED_COUNT=$((COPIED_COUNT + 1))
        else
            echo_warn "❌ Библиотека не найдена: $STAGE_LIB"
        fi
    done
    
    echo_info "Скопировано библиотек: $COPIED_COUNT из ${#BOOST_LIBS[@]}"
    
    if [ $COPIED_COUNT -eq 0 ]; then
        echo_error "Ни одна библиотека не была скопирована!"
        exit 1
    fi
}

# Основная функция
main() {
    echo_info "Начинаем сборку Boost $BOOST_VERSION для Android aarch64"
    
    # Создаем рабочую директорию
    BUILD_DIR="build_boost"
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    check_environment
    setup_build_environment
    download_boost
    build_boost
    copy_libraries
    
    echo_info "Сборка Boost завершена успешно!"
    echo_info "Библиотеки Boost готовы к использованию с флагом -fPIC."
    echo_info "Запустите ./scripts/check_boost_libs.sh для проверки результата."
}

# Запуск скрипта
main "$@"
