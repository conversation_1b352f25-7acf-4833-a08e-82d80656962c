#!/bin/bash

# Скрипт для сборки OpenSSL 3.2.1 для Android aarch64 с флагом -fPIC
# Автор: Asta Android Team

set -e

# Конфигурация
OPENSSL_VERSION="3.2.1"
ANDROID_API_LEVEL="21"
ANDROID_ARCH="aarch64"
TARGET_ARCH="android-arm64"

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Проверка переменных окружения
check_environment() {
    echo_info "Проверка переменных окружения..."
    
    if [ -z "$ANDROID_NDK_ROOT" ]; then
        echo_error "ANDROID_NDK_ROOT не установлен!"
        echo_info "Установите переменную окружения ANDROID_NDK_ROOT, например:"
        echo_info "export ANDROID_NDK_ROOT=/path/to/android-ndk"
        exit 1
    fi
    
    if [ ! -d "$ANDROID_NDK_ROOT" ]; then
        echo_error "Директория ANDROID_NDK_ROOT не существует: $ANDROID_NDK_ROOT"
        exit 1
    fi
    
    echo_info "ANDROID_NDK_ROOT: $ANDROID_NDK_ROOT"
}

# Настройка переменных для сборки
setup_build_environment() {
    echo_info "Настройка окружения для сборки..."
    
    # Путь к тулчейну
    export TOOLCHAIN="$ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/darwin-x86_64"
    
    # Проверяем существование тулчейна для разных платформ
    if [ ! -d "$TOOLCHAIN" ]; then
        export TOOLCHAIN="$ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64"
    fi
    
    if [ ! -d "$TOOLCHAIN" ]; then
        echo_error "Не найден тулчейн в $ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/"
        exit 1
    fi
    
    # Настройка компилятора
    export TARGET="${ANDROID_ARCH}-linux-android"
    export API="$ANDROID_API_LEVEL"
    export AR="$TOOLCHAIN/bin/llvm-ar"
    export CC="$TOOLCHAIN/bin/${TARGET}${API}-clang"
    export AS="$CC"
    export CXX="$TOOLCHAIN/bin/${TARGET}${API}-clang++"
    export LD="$TOOLCHAIN/bin/ld"
    export RANLIB="$TOOLCHAIN/bin/llvm-ranlib"
    export STRIP="$TOOLCHAIN/bin/llvm-strip"
    
    # Флаги компиляции с -fPIC
    export CFLAGS="-fPIC -O3"
    export CXXFLAGS="-fPIC -O3"
    
    echo_info "Тулчейн: $TOOLCHAIN"
    echo_info "Компилятор: $CC"
    echo_info "CFLAGS: $CFLAGS"
}

# Скачивание OpenSSL
download_openssl() {
    echo_info "Скачивание OpenSSL $OPENSSL_VERSION..."
    
    OPENSSL_DIR="openssl-$OPENSSL_VERSION"
    OPENSSL_TAR="$OPENSSL_DIR.tar.gz"
    OPENSSL_URL="https://www.openssl.org/source/$OPENSSL_TAR"
    
    if [ ! -f "$OPENSSL_TAR" ]; then
        echo_info "Скачиваем $OPENSSL_URL"
        curl -L -o "$OPENSSL_TAR" "$OPENSSL_URL"
    else
        echo_info "Архив уже скачан: $OPENSSL_TAR"
    fi
    
    if [ -d "$OPENSSL_DIR" ]; then
        echo_warn "Удаляем существующую директорию $OPENSSL_DIR"
        rm -rf "$OPENSSL_DIR"
    fi
    
    echo_info "Распаковываем архив..."
    tar -xzf "$OPENSSL_TAR"
    
    cd "$OPENSSL_DIR"
}

# Сборка OpenSSL
build_openssl() {
    echo_info "Конфигурирование OpenSSL для $TARGET_ARCH..."
    
    # Конфигурация для Android aarch64
    ./Configure $TARGET_ARCH \
        -D__ANDROID_API__=$API \
        --prefix="$(pwd)/install" \
        --openssldir="$(pwd)/install/ssl" \
        no-shared \
        no-tests \
        no-ui-console \
        no-asm \
        -fPIC
    
    echo_info "Сборка OpenSSL..."
    make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
    
    echo_info "Установка OpenSSL..."
    make install_sw
}

# Копирование библиотек
copy_libraries() {
    echo_info "Копирование библиотек в проект..."
    
    INSTALL_DIR="$(pwd)/install"
    PROJECT_LIBS_DIR="../engine/engine-impl/src/main/jniLibs/aarch64"
    
    # Создаем директорию если не существует
    mkdir -p "$PROJECT_LIBS_DIR"
    
    # Копируем библиотеки
    cp "$INSTALL_DIR/lib/libssl.a" "$PROJECT_LIBS_DIR/"
    cp "$INSTALL_DIR/lib/libcrypto.a" "$PROJECT_LIBS_DIR/"
    
    echo_info "Библиотеки скопированы:"
    echo_info "  libssl.a -> $PROJECT_LIBS_DIR/libssl.a"
    echo_info "  libcrypto.a -> $PROJECT_LIBS_DIR/libcrypto.a"
    
    # Проверяем размеры файлов
    echo_info "Размеры библиотек:"
    ls -lh "$PROJECT_LIBS_DIR/libssl.a" "$PROJECT_LIBS_DIR/libcrypto.a"
}

# Основная функция
main() {
    echo_info "Начинаем сборку OpenSSL $OPENSSL_VERSION для Android $ANDROID_ARCH"
    
    # Создаем рабочую директорию
    BUILD_DIR="build_openssl"
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    check_environment
    setup_build_environment
    download_openssl
    build_openssl
    copy_libraries
    
    echo_info "Сборка OpenSSL завершена успешно!"
    echo_info "Библиотеки libssl.a и libcrypto.a готовы к использованию."
}

# Запуск скрипта
main "$@"
