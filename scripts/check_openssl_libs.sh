#!/bin/bash

# Скрипт для проверки библиотек OpenSSL в проекте
# Автор: Asta Android Team

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_detail() {
    echo -e "${BLUE}[DETAIL]${NC} $1"
}

# Путь к библиотекам
LIBS_DIR="engine/engine-impl/src/main/jniLibs/aarch64"

check_library() {
    local lib_path="$1"
    local lib_name=$(basename "$lib_path")
    
    echo_info "Проверка библиотеки: $lib_name"
    
    if [ ! -f "$lib_path" ]; then
        echo_error "Библиотека не найдена: $lib_path"
        return 1
    fi
    
    # Размер файла
    local size=$(ls -lh "$lib_path" | awk '{print $5}')
    echo_detail "Размер: $size"
    
    # Информация о файле
    echo_detail "Тип файла:"
    file "$lib_path" | sed 's/^/    /'
    
    # Проверка архитектуры
    echo_detail "Архитектура:"
    if command -v objdump >/dev/null 2>&1; then
        objdump -f "$lib_path" 2>/dev/null | grep "architecture" | sed 's/^/    /' || echo "    Не удалось определить архитектуру"
    elif command -v readelf >/dev/null 2>&1; then
        readelf -h "$lib_path" 2>/dev/null | grep "Machine:" | sed 's/^/    /' || echo "    Не удалось определить архитектуру"
    else
        echo "    Утилиты objdump/readelf не найдены"
    fi
    
    # Проверка символов (первые 10)
    echo_detail "Символы (первые 10):"
    if command -v nm >/dev/null 2>&1; then
        nm "$lib_path" 2>/dev/null | head -10 | sed 's/^/    /' || echo "    Не удалось получить символы"
    elif command -v objdump >/dev/null 2>&1; then
        objdump -t "$lib_path" 2>/dev/null | head -15 | tail -10 | sed 's/^/    /' || echo "    Не удалось получить символы"
    else
        echo "    Утилиты nm/objdump не найдены"
    fi
    
    echo ""
}

check_pic_flag() {
    local lib_path="$1"
    local lib_name=$(basename "$lib_path")
    
    echo_info "Проверка флага -fPIC для $lib_name"
    
    if [ ! -f "$lib_path" ]; then
        echo_error "Библиотека не найдена: $lib_path"
        return 1
    fi
    
    # Проверяем наличие позиционно-независимого кода
    if command -v readelf >/dev/null 2>&1; then
        echo_detail "Проверка через readelf:"
        if readelf -d "$lib_path" 2>/dev/null | grep -q "TEXTREL\|DT_TEXTREL"; then
            echo_warn "    Обнаружены текстовые релокации (возможно, не -fPIC)"
        else
            echo_detail "    Текстовые релокации не найдены (хорошо для -fPIC)"
        fi
        
        # Проверяем тип файла
        local file_type=$(readelf -h "$lib_path" 2>/dev/null | grep "Type:" | awk '{print $2}')
        echo_detail "    Тип файла: $file_type"
        
    elif command -v objdump >/dev/null 2>&1; then
        echo_detail "Проверка через objdump:"
        if objdump -R "$lib_path" 2>/dev/null | grep -q "R_.*_RELATIVE"; then
            echo_detail "    Найдены относительные релокации (хорошо для -fPIC)"
        else
            echo_warn "    Относительные релокации не найдены"
        fi
    else
        echo_warn "    Утилиты readelf/objdump не найдены для проверки -fPIC"
    fi
    
    echo ""
}

main() {
    echo_info "Проверка библиотек OpenSSL в проекте Asta Android"
    echo_info "Директория библиотек: $LIBS_DIR"
    echo ""
    
    # Проверяем существование директории
    if [ ! -d "$LIBS_DIR" ]; then
        echo_error "Директория с библиотеками не найдена: $LIBS_DIR"
        exit 1
    fi
    
    # Проверяем libssl.a
    SSL_LIB="$LIBS_DIR/libssl.a"
    check_library "$SSL_LIB"
    check_pic_flag "$SSL_LIB"
    
    # Проверяем libcrypto.a
    CRYPTO_LIB="$LIBS_DIR/libcrypto.a"
    check_library "$CRYPTO_LIB"
    check_pic_flag "$CRYPTO_LIB"
    
    # Общая информация
    echo_info "Общая информация:"
    echo_detail "Общий размер OpenSSL библиотек:"
    du -sh "$SSL_LIB" "$CRYPTO_LIB" | sed 's/^/    /'
    
    # Проверяем дату модификации
    echo_detail "Дата последней модификации:"
    ls -la "$SSL_LIB" "$CRYPTO_LIB" | awk '{print "    " $6 " " $7 " " $8 " " $9}'
    
    echo ""
    echo_info "Проверка завершена!"
    echo_info "Если нужно пересобрать OpenSSL с -fPIC, запустите: ./scripts/build_openssl.sh"
}

# Запуск скрипта
main "$@"
