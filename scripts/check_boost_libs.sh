#!/bin/bash

# Скрипт для проверки библиотек Boost в проекте
# Автор: Asta Android Team

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_detail() {
    echo -e "${BLUE}[DETAIL]${NC} $1"
}

# Путь к библиотекам
LIBS_DIR="engine/engine-impl/src/main/jniLibs/aarch64"

# Список Boost библиотек из CMakeLists.txt
BOOST_LIBS=(
    "boost_locale"
    "boost_thread"
    "boost_atomic"
    "boost_timer"
    "boost_container"
    "boost_program_options"
    "boost_date_time"
    "boost_random"
    "boost_exception"
    "boost_filesystem"
    "boost_iostreams"
    "boost_regex"
    "boost_system"
)

check_boost_library() {
    local lib_name="$1"
    local lib_path="$LIBS_DIR/lib${lib_name}.a"
    
    echo_info "Проверка библиотеки: $lib_name"
    
    if [ ! -f "$lib_path" ]; then
        echo_error "Библиотека не найдена: $lib_path"
        return 1
    fi
    
    # Размер файла
    local size=$(ls -lh "$lib_path" | awk '{print $5}')
    echo_detail "Размер: $size"
    
    # Информация о файле
    echo_detail "Тип файла:"
    file "$lib_path" | sed 's/^/    /'
    
    # Проверка архитектуры
    echo_detail "Архитектура:"
    if command -v objdump >/dev/null 2>&1; then
        objdump -f "$lib_path" 2>/dev/null | grep "architecture" | head -1 | sed 's/^/    /' || echo "    Не удалось определить архитектуру"
    else
        echo "    Утилита objdump не найдена"
    fi
    
    # Проверка символов (первые 5)
    echo_detail "Символы (первые 5):"
    if command -v nm >/dev/null 2>&1; then
        nm "$lib_path" 2>/dev/null | head -5 | sed 's/^/    /' || echo "    Не удалось получить символы"
    else
        echo "    Утилита nm не найдена"
    fi
    
    echo ""
}

detect_boost_version() {
    echo_info "Попытка определения версии Boost..."
    
    # Проверяем разные библиотеки на наличие версионной информации
    for lib in "${BOOST_LIBS[@]}"; do
        local lib_path="$LIBS_DIR/lib${lib}.a"
        if [ -f "$lib_path" ]; then
            echo_detail "Проверка версии в $lib..."
            
            # Ищем версионные символы
            if command -v strings >/dev/null 2>&1; then
                local version_info=$(strings "$lib_path" 2>/dev/null | grep -E "1\.[0-9]{2}\.[0-9]|boost.*[0-9]\.[0-9]" | head -3)
                if [ ! -z "$version_info" ]; then
                    echo_detail "Найдена версионная информация:"
                    echo "$version_info" | sed 's/^/    /'
                fi
            fi
            
            # Ищем в символах
            if command -v nm >/dev/null 2>&1; then
                local symbols=$(nm "$lib_path" 2>/dev/null | grep -i "version\|boost.*[0-9]" | head -3)
                if [ ! -z "$symbols" ]; then
                    echo_detail "Версионные символы:"
                    echo "$symbols" | sed 's/^/    /'
                fi
            fi
        fi
    done
    
    # Анализ размеров для предположения о версии
    echo_detail "Анализ размеров библиотек для определения версии:"
    local filesystem_size=$(stat -f%z "$LIBS_DIR/libboost_filesystem.a" 2>/dev/null || echo "0")
    local locale_size=$(stat -f%z "$LIBS_DIR/libboost_locale.a" 2>/dev/null || echo "0")
    
    echo_detail "libboost_filesystem.a: $(ls -lh "$LIBS_DIR/libboost_filesystem.a" | awk '{print $5}')"
    echo_detail "libboost_locale.a: $(ls -lh "$LIBS_DIR/libboost_locale.a" | awk '{print $5}')"
    
    # Предположения на основе размеров (приблизительно)
    if [ "$filesystem_size" -gt 300000 ] && [ "$locale_size" -gt 1000000 ]; then
        echo_warn "Предположительно Boost 1.82+ (на основе размеров библиотек)"
    elif [ "$filesystem_size" -gt 250000 ] && [ "$locale_size" -gt 800000 ]; then
        echo_warn "Предположительно Boost 1.75-1.81 (на основе размеров библиотек)"
    else
        echo_warn "Предположительно Boost 1.70-1.74 (на основе размеров библиотек)"
    fi
    
    echo_warn "Рекомендуется использовать Boost 1.82.0 для новой сборки"
}

check_pic_flag() {
    local lib_path="$1"
    local lib_name=$(basename "$lib_path")
    
    echo_info "Проверка флага -fPIC для $lib_name"
    
    if [ ! -f "$lib_path" ]; then
        echo_error "Библиотека не найдена: $lib_path"
        return 1
    fi
    
    # Проверяем наличие позиционно-независимого кода
    if command -v readelf >/dev/null 2>&1; then
        echo_detail "Проверка через readelf:"
        if readelf -d "$lib_path" 2>/dev/null | grep -q "TEXTREL\|DT_TEXTREL"; then
            echo_warn "    Обнаружены текстовые релокации (возможно, не -fPIC)"
        else
            echo_detail "    Текстовые релокации не найдены (хорошо для -fPIC)"
        fi
    elif command -v objdump >/dev/null 2>&1; then
        echo_detail "Проверка через objdump:"
        if objdump -R "$lib_path" 2>/dev/null | grep -q "R_.*_RELATIVE"; then
            echo_detail "    Найдены относительные релокации (хорошо для -fPIC)"
        else
            echo_warn "    Относительные релокации не найдены"
        fi
    else
        echo_warn "    Утилиты readelf/objdump не найдены для проверки -fPIC"
    fi
    
    echo ""
}

main() {
    echo_info "Проверка библиотек Boost в проекте Asta Android"
    echo_info "Директория библиотек: $LIBS_DIR"
    echo ""
    
    # Проверяем существование директории
    if [ ! -d "$LIBS_DIR" ]; then
        echo_error "Директория с библиотеками не найдена: $LIBS_DIR"
        exit 1
    fi
    
    # Определяем версию Boost
    detect_boost_version
    echo ""
    
    # Проверяем каждую библиотеку
    echo_info "Проверка отдельных библиотек:"
    for lib in "${BOOST_LIBS[@]}"; do
        check_boost_library "$lib"
    done
    
    # Проверяем флаг -fPIC для нескольких ключевых библиотек
    echo_info "Проверка флага -fPIC для ключевых библиотек:"
    check_pic_flag "$LIBS_DIR/libboost_system.a"
    check_pic_flag "$LIBS_DIR/libboost_filesystem.a"
    check_pic_flag "$LIBS_DIR/libboost_thread.a"
    
    # Общая информация
    echo_info "Общая информация:"
    echo_detail "Общий размер Boost библиотек:"
    du -sh $LIBS_DIR/libboost_*.a | awk '{sum+=$1} END {print "    Всего: " sum " (приблизительно)"}'
    
    # Проверяем дату модификации
    echo_detail "Дата последней модификации (первые 5 библиотек):"
    ls -la $LIBS_DIR/libboost_*.a | head -5 | awk '{print "    " $6 " " $7 " " $8 " " $9}'
    
    echo ""
    echo_info "Проверка завершена!"
    echo_info "Для пересборки Boost с -fPIC запустите: ./scripts/build_boost.sh"
}

# Запуск скрипта
main "$@"
