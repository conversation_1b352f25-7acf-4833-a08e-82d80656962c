#! /usr/bin/env perl
#
# Copyright 2017-2021 The OpenSSL Project Authors. All Rights Reserved.
# Copyright 2017 BaishanCloud. All rights reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


use strict;
use warnings;

use OpenSSL::Test::Simple;
use OpenSSL::Test;
use OpenSSL::Test::Utils qw(alldisabled available_protocols);

setup("test_ciphername");

my $no_anytls = alldisabled(available_protocols("tls"));

# If we have no protocols, then we also have no supported ciphers.
plan skip_all => "No SSL/TLS protocol is supported by this OpenSSL build."
    if $no_anytls;

simple_test("test_ciphername", "ciphername_test");
