#! /usr/bin/env perl
# Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


use OpenSSL::Test qw(:DEFAULT data_dir);
use OpenSSL::Test::Utils;

setup("test_pem_read_depr");

plan skip_all => "Test not supported in a no-deprecated build"
    if disabled("deprecated");

plan tests => 1;

ok(run(test(['pem_read_depr_test', data_dir()])), "pem_read_depr_test");
