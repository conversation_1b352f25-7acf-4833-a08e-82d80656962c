# -*- mode: perl; -*-
# Copyright 2016-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test DTLS CertStatus messages

use strict;
use warnings;

package ssltests;
use OpenSSL::Test::Utils;

our $fips_mode;

our @tests = ();

our @tests_standard = (
    {
        name => "certstatus-good",
        server => {
            "CipherString" => "DEFAULT:\@SECLEVEL=0",
            extra => {
                "CertStatus" => "GoodResponse"
            },
        },
        client => {
            "CipherString" => "DEFAULT:\@SECLEVEL=0",
        },
        test => {
            "Method" => "DTLS",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "certstatus-bad",
        server => {
            "CipherString" => "DEFAULT:\@SECLEVEL=0",
            extra => {
                "CertStatus" => "BadResponse",
            },
        },
        client => {
            "CipherString" => "DEFAULT:\@SECLEVEL=0",
        },
        test => {
            "Method" => "DTLS",
            "ExpectedResult" => "ClientFail"
        }
    }
);

our @tests_sctp = (
    {
        name => "certstatus-good",
        server => {
            "CipherString" => "DEFAULT:\@SECLEVEL=0",
            extra => {
                "CertStatus" => "GoodResponse",
            },
        },
        client => {
            "CipherString" => "DEFAULT:\@SECLEVEL=0",
        },
        test => {
            "Method" => "DTLS",
            "UseSCTP" => "Yes",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "certstatus-bad",
        server => {
            "CipherString" => "DEFAULT:\@SECLEVEL=0",
            extra => {
                "CertStatus" => "BadResponse",
            },
        },
        client => {
            "CipherString" => "DEFAULT:\@SECLEVEL=0",
        },
        test => {
            "Method" => "DTLS",
            "UseSCTP" => "Yes",
            "ExpectedResult" => "ClientFail"
        }
    },
);

if  (!$fips_mode || !disabled("dtls1_2")) {
    push @tests, @tests_standard;
    push @tests, @tests_sctp unless disabled("sctp") || disabled("sock");
}
