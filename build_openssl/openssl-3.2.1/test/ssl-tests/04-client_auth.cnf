# Generated with generate_ssl_tests.pl

num_tests = 40

test-0 = 0-server-auth-flex
test-1 = 1-client-auth-flex-request
test-2 = 2-client-auth-flex-require-fail
test-3 = 3-client-auth-flex-require
test-4 = 4-client-auth-flex-rsa-pss
test-5 = 5-client-auth-flex-rsa-pss-bad
test-6 = 6-client-auth-flex-require-non-empty-names
test-7 = 7-client-auth-flex-noroot
test-8 = 8-server-auth-TLSv1
test-9 = 9-client-auth-TLSv1-request
test-10 = 10-client-auth-TLSv1-require-fail
test-11 = 11-client-auth-TLSv1-require
test-12 = 12-client-auth-TLSv1-require-non-empty-names
test-13 = 13-client-auth-TLSv1-noroot
test-14 = 14-server-auth-TLSv1.1
test-15 = 15-client-auth-TLSv1.1-request
test-16 = 16-client-auth-TLSv1.1-require-fail
test-17 = 17-client-auth-TLSv1.1-require
test-18 = 18-client-auth-TLSv1.1-require-non-empty-names
test-19 = 19-client-auth-TLSv1.1-noroot
test-20 = 20-server-auth-TLSv1.2
test-21 = 21-client-auth-TLSv1.2-request
test-22 = 22-client-auth-TLSv1.2-require-fail
test-23 = 23-client-auth-TLSv1.2-require
test-24 = 24-client-auth-TLSv1.2-rsa-pss
test-25 = 25-client-auth-TLSv1.2-rsa-pss-bad
test-26 = 26-client-auth-TLSv1.2-require-non-empty-names
test-27 = 27-client-auth-TLSv1.2-noroot
test-28 = 28-server-auth-DTLSv1
test-29 = 29-client-auth-DTLSv1-request
test-30 = 30-client-auth-DTLSv1-require-fail
test-31 = 31-client-auth-DTLSv1-require
test-32 = 32-client-auth-DTLSv1-require-non-empty-names
test-33 = 33-client-auth-DTLSv1-noroot
test-34 = 34-server-auth-DTLSv1.2
test-35 = 35-client-auth-DTLSv1.2-request
test-36 = 36-client-auth-DTLSv1.2-require-fail
test-37 = 37-client-auth-DTLSv1.2-require
test-38 = 38-client-auth-DTLSv1.2-require-non-empty-names
test-39 = 39-client-auth-DTLSv1.2-noroot
# ===========================================================

[0-server-auth-flex]
ssl_conf = 0-server-auth-flex-ssl

[0-server-auth-flex-ssl]
server = 0-server-auth-flex-server
client = 0-server-auth-flex-client

[0-server-auth-flex-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-server-auth-flex-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success


# ===========================================================

[1-client-auth-flex-request]
ssl_conf = 1-client-auth-flex-request-ssl

[1-client-auth-flex-request-ssl]
server = 1-client-auth-flex-request-server
client = 1-client-auth-flex-request-client

[1-client-auth-flex-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[1-client-auth-flex-request-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success


# ===========================================================

[2-client-auth-flex-require-fail]
ssl_conf = 2-client-auth-flex-require-fail-ssl

[2-client-auth-flex-require-fail-ssl]
server = 2-client-auth-flex-require-fail-server
client = 2-client-auth-flex-require-fail-client

[2-client-auth-flex-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[2-client-auth-flex-require-fail-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateRequired


# ===========================================================

[3-client-auth-flex-require]
ssl_conf = 3-client-auth-flex-require-ssl

[3-client-auth-flex-require-ssl]
server = 3-client-auth-flex-require-server
client = 3-client-auth-flex-require-client

[3-client-auth-flex-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[3-client-auth-flex-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[4-client-auth-flex-rsa-pss]
ssl_conf = 4-client-auth-flex-rsa-pss-ssl

[4-client-auth-flex-rsa-pss-ssl]
server = 4-client-auth-flex-rsa-pss-server
client = 4-client-auth-flex-rsa-pss-client

[4-client-auth-flex-rsa-pss-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Require

[4-client-auth-flex-rsa-pss-client]
Certificate = ${ENV::TEST_CERTS_DIR}/client-pss-restrict-cert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = StrictCertCheck
PrivateKey = ${ENV::TEST_CERTS_DIR}/client-pss-restrict-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/rootcert.pem
ExpectedClientCertType = RSA-PSS
ExpectedResult = Success


# ===========================================================

[5-client-auth-flex-rsa-pss-bad]
ssl_conf = 5-client-auth-flex-rsa-pss-bad-ssl

[5-client-auth-flex-rsa-pss-bad-ssl]
server = 5-client-auth-flex-rsa-pss-bad-server
client = 5-client-auth-flex-rsa-pss-bad-client

[5-client-auth-flex-rsa-pss-bad-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/rootCA.pem
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootCA.pem
VerifyMode = Require

[5-client-auth-flex-rsa-pss-bad-client]
Certificate = ${ENV::TEST_CERTS_DIR}/client-pss-restrict-cert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = StrictCertCheck
PrivateKey = ${ENV::TEST_CERTS_DIR}/client-pss-restrict-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateRequired


# ===========================================================

[6-client-auth-flex-require-non-empty-names]
ssl_conf = 6-client-auth-flex-require-non-empty-names-ssl

[6-client-auth-flex-require-non-empty-names-ssl]
server = 6-client-auth-flex-require-non-empty-names-server
client = 6-client-auth-flex-require-non-empty-names-client

[6-client-auth-flex-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[6-client-auth-flex-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[7-client-auth-flex-noroot]
ssl_conf = 7-client-auth-flex-noroot-ssl

[7-client-auth-flex-noroot-ssl]
server = 7-client-auth-flex-noroot-server
client = 7-client-auth-flex-noroot-client

[7-client-auth-flex-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[7-client-auth-flex-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA


# ===========================================================

[8-server-auth-TLSv1]
ssl_conf = 8-server-auth-TLSv1-ssl

[8-server-auth-TLSv1-ssl]
server = 8-server-auth-TLSv1-server
client = 8-server-auth-TLSv1-client

[8-server-auth-TLSv1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-server-auth-TLSv1-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedResult = Success


# ===========================================================

[9-client-auth-TLSv1-request]
ssl_conf = 9-client-auth-TLSv1-request-ssl

[9-client-auth-TLSv1-request-ssl]
server = 9-client-auth-TLSv1-request-server
client = 9-client-auth-TLSv1-request-client

[9-client-auth-TLSv1-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[9-client-auth-TLSv1-request-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedResult = Success


# ===========================================================

[10-client-auth-TLSv1-require-fail]
ssl_conf = 10-client-auth-TLSv1-require-fail-ssl

[10-client-auth-TLSv1-require-fail-ssl]
server = 10-client-auth-TLSv1-require-fail-server
client = 10-client-auth-TLSv1-require-fail-client

[10-client-auth-TLSv1-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[10-client-auth-TLSv1-require-fail-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure


# ===========================================================

[11-client-auth-TLSv1-require]
ssl_conf = 11-client-auth-TLSv1-require-ssl

[11-client-auth-TLSv1-require-ssl]
server = 11-client-auth-TLSv1-require-server
client = 11-client-auth-TLSv1-require-client

[11-client-auth-TLSv1-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[11-client-auth-TLSv1-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[12-client-auth-TLSv1-require-non-empty-names]
ssl_conf = 12-client-auth-TLSv1-require-non-empty-names-ssl

[12-client-auth-TLSv1-require-non-empty-names-ssl]
server = 12-client-auth-TLSv1-require-non-empty-names-server
client = 12-client-auth-TLSv1-require-non-empty-names-client

[12-client-auth-TLSv1-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[12-client-auth-TLSv1-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[13-client-auth-TLSv1-noroot]
ssl_conf = 13-client-auth-TLSv1-noroot-ssl

[13-client-auth-TLSv1-noroot-ssl]
server = 13-client-auth-TLSv1-noroot-server
client = 13-client-auth-TLSv1-noroot-client

[13-client-auth-TLSv1-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[13-client-auth-TLSv1-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA


# ===========================================================

[14-server-auth-TLSv1.1]
ssl_conf = 14-server-auth-TLSv1.1-ssl

[14-server-auth-TLSv1.1-ssl]
server = 14-server-auth-TLSv1.1-server
client = 14-server-auth-TLSv1.1-client

[14-server-auth-TLSv1.1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-server-auth-TLSv1.1-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ExpectedResult = Success


# ===========================================================

[15-client-auth-TLSv1.1-request]
ssl_conf = 15-client-auth-TLSv1.1-request-ssl

[15-client-auth-TLSv1.1-request-ssl]
server = 15-client-auth-TLSv1.1-request-server
client = 15-client-auth-TLSv1.1-request-client

[15-client-auth-TLSv1.1-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[15-client-auth-TLSv1.1-request-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedResult = Success


# ===========================================================

[16-client-auth-TLSv1.1-require-fail]
ssl_conf = 16-client-auth-TLSv1.1-require-fail-ssl

[16-client-auth-TLSv1.1-require-fail-ssl]
server = 16-client-auth-TLSv1.1-require-fail-server
client = 16-client-auth-TLSv1.1-require-fail-client

[16-client-auth-TLSv1.1-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[16-client-auth-TLSv1.1-require-fail-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-16]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure


# ===========================================================

[17-client-auth-TLSv1.1-require]
ssl_conf = 17-client-auth-TLSv1.1-require-ssl

[17-client-auth-TLSv1.1-require-ssl]
server = 17-client-auth-TLSv1.1-require-server
client = 17-client-auth-TLSv1.1-require-client

[17-client-auth-TLSv1.1-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[17-client-auth-TLSv1.1-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[18-client-auth-TLSv1.1-require-non-empty-names]
ssl_conf = 18-client-auth-TLSv1.1-require-non-empty-names-ssl

[18-client-auth-TLSv1.1-require-non-empty-names-ssl]
server = 18-client-auth-TLSv1.1-require-non-empty-names-server
client = 18-client-auth-TLSv1.1-require-non-empty-names-client

[18-client-auth-TLSv1.1-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[18-client-auth-TLSv1.1-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-18]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[19-client-auth-TLSv1.1-noroot]
ssl_conf = 19-client-auth-TLSv1.1-noroot-ssl

[19-client-auth-TLSv1.1-noroot-ssl]
server = 19-client-auth-TLSv1.1-noroot-server
client = 19-client-auth-TLSv1.1-noroot-client

[19-client-auth-TLSv1.1-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[19-client-auth-TLSv1.1-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-19]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA


# ===========================================================

[20-server-auth-TLSv1.2]
ssl_conf = 20-server-auth-TLSv1.2-ssl

[20-server-auth-TLSv1.2-ssl]
server = 20-server-auth-TLSv1.2-server
client = 20-server-auth-TLSv1.2-client

[20-server-auth-TLSv1.2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[20-server-auth-TLSv1.2-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-20]
ExpectedResult = Success


# ===========================================================

[21-client-auth-TLSv1.2-request]
ssl_conf = 21-client-auth-TLSv1.2-request-ssl

[21-client-auth-TLSv1.2-request-ssl]
server = 21-client-auth-TLSv1.2-request-server
client = 21-client-auth-TLSv1.2-request-client

[21-client-auth-TLSv1.2-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[21-client-auth-TLSv1.2-request-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-21]
ExpectedResult = Success


# ===========================================================

[22-client-auth-TLSv1.2-require-fail]
ssl_conf = 22-client-auth-TLSv1.2-require-fail-ssl

[22-client-auth-TLSv1.2-require-fail-ssl]
server = 22-client-auth-TLSv1.2-require-fail-server
client = 22-client-auth-TLSv1.2-require-fail-client

[22-client-auth-TLSv1.2-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[22-client-auth-TLSv1.2-require-fail-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-22]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure


# ===========================================================

[23-client-auth-TLSv1.2-require]
ssl_conf = 23-client-auth-TLSv1.2-require-ssl

[23-client-auth-TLSv1.2-require-ssl]
server = 23-client-auth-TLSv1.2-require-server
client = 23-client-auth-TLSv1.2-require-client

[23-client-auth-TLSv1.2-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientSignatureAlgorithms = SHA256+RSA
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[23-client-auth-TLSv1.2-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-23]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedClientSignHash = SHA256
ExpectedClientSignType = RSA
ExpectedResult = Success


# ===========================================================

[24-client-auth-TLSv1.2-rsa-pss]
ssl_conf = 24-client-auth-TLSv1.2-rsa-pss-ssl

[24-client-auth-TLSv1.2-rsa-pss-ssl]
server = 24-client-auth-TLSv1.2-rsa-pss-server
client = 24-client-auth-TLSv1.2-rsa-pss-client

[24-client-auth-TLSv1.2-rsa-pss-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Require

[24-client-auth-TLSv1.2-rsa-pss-client]
Certificate = ${ENV::TEST_CERTS_DIR}/client-pss-restrict-cert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = StrictCertCheck
PrivateKey = ${ENV::TEST_CERTS_DIR}/client-pss-restrict-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-24]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/rootcert.pem
ExpectedClientCertType = RSA-PSS
ExpectedResult = Success


# ===========================================================

[25-client-auth-TLSv1.2-rsa-pss-bad]
ssl_conf = 25-client-auth-TLSv1.2-rsa-pss-bad-ssl

[25-client-auth-TLSv1.2-rsa-pss-bad-ssl]
server = 25-client-auth-TLSv1.2-rsa-pss-bad-server
client = 25-client-auth-TLSv1.2-rsa-pss-bad-client

[25-client-auth-TLSv1.2-rsa-pss-bad-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/rootCA.pem
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootCA.pem
VerifyMode = Require

[25-client-auth-TLSv1.2-rsa-pss-bad-client]
Certificate = ${ENV::TEST_CERTS_DIR}/client-pss-restrict-cert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = StrictCertCheck
PrivateKey = ${ENV::TEST_CERTS_DIR}/client-pss-restrict-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-25]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure


# ===========================================================

[26-client-auth-TLSv1.2-require-non-empty-names]
ssl_conf = 26-client-auth-TLSv1.2-require-non-empty-names-ssl

[26-client-auth-TLSv1.2-require-non-empty-names-ssl]
server = 26-client-auth-TLSv1.2-require-non-empty-names-server
client = 26-client-auth-TLSv1.2-require-non-empty-names-client

[26-client-auth-TLSv1.2-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ClientSignatureAlgorithms = SHA256+RSA
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[26-client-auth-TLSv1.2-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-26]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedClientSignHash = SHA256
ExpectedClientSignType = RSA
ExpectedResult = Success


# ===========================================================

[27-client-auth-TLSv1.2-noroot]
ssl_conf = 27-client-auth-TLSv1.2-noroot-ssl

[27-client-auth-TLSv1.2-noroot-ssl]
server = 27-client-auth-TLSv1.2-noroot-server
client = 27-client-auth-TLSv1.2-noroot-client

[27-client-auth-TLSv1.2-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[27-client-auth-TLSv1.2-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-27]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA


# ===========================================================

[28-server-auth-DTLSv1]
ssl_conf = 28-server-auth-DTLSv1-ssl

[28-server-auth-DTLSv1-ssl]
server = 28-server-auth-DTLSv1-server
client = 28-server-auth-DTLSv1-client

[28-server-auth-DTLSv1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[28-server-auth-DTLSv1-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-28]
ExpectedResult = Success
Method = DTLS


# ===========================================================

[29-client-auth-DTLSv1-request]
ssl_conf = 29-client-auth-DTLSv1-request-ssl

[29-client-auth-DTLSv1-request-ssl]
server = 29-client-auth-DTLSv1-request-server
client = 29-client-auth-DTLSv1-request-client

[29-client-auth-DTLSv1-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[29-client-auth-DTLSv1-request-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-29]
ExpectedResult = Success
Method = DTLS


# ===========================================================

[30-client-auth-DTLSv1-require-fail]
ssl_conf = 30-client-auth-DTLSv1-require-fail-ssl

[30-client-auth-DTLSv1-require-fail-ssl]
server = 30-client-auth-DTLSv1-require-fail-server
client = 30-client-auth-DTLSv1-require-fail-client

[30-client-auth-DTLSv1-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[30-client-auth-DTLSv1-require-fail-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-30]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure
Method = DTLS


# ===========================================================

[31-client-auth-DTLSv1-require]
ssl_conf = 31-client-auth-DTLSv1-require-ssl

[31-client-auth-DTLSv1-require-ssl]
server = 31-client-auth-DTLSv1-require-server
client = 31-client-auth-DTLSv1-require-client

[31-client-auth-DTLSv1-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[31-client-auth-DTLSv1-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-31]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success
Method = DTLS


# ===========================================================

[32-client-auth-DTLSv1-require-non-empty-names]
ssl_conf = 32-client-auth-DTLSv1-require-non-empty-names-ssl

[32-client-auth-DTLSv1-require-non-empty-names-ssl]
server = 32-client-auth-DTLSv1-require-non-empty-names-server
client = 32-client-auth-DTLSv1-require-non-empty-names-client

[32-client-auth-DTLSv1-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[32-client-auth-DTLSv1-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-32]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success
Method = DTLS


# ===========================================================

[33-client-auth-DTLSv1-noroot]
ssl_conf = 33-client-auth-DTLSv1-noroot-ssl

[33-client-auth-DTLSv1-noroot-ssl]
server = 33-client-auth-DTLSv1-noroot-server
client = 33-client-auth-DTLSv1-noroot-client

[33-client-auth-DTLSv1-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[33-client-auth-DTLSv1-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-33]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA
Method = DTLS


# ===========================================================

[34-server-auth-DTLSv1.2]
ssl_conf = 34-server-auth-DTLSv1.2-ssl

[34-server-auth-DTLSv1.2-ssl]
server = 34-server-auth-DTLSv1.2-server
client = 34-server-auth-DTLSv1.2-client

[34-server-auth-DTLSv1.2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[34-server-auth-DTLSv1.2-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-34]
ExpectedResult = Success
Method = DTLS


# ===========================================================

[35-client-auth-DTLSv1.2-request]
ssl_conf = 35-client-auth-DTLSv1.2-request-ssl

[35-client-auth-DTLSv1.2-request-ssl]
server = 35-client-auth-DTLSv1.2-request-server
client = 35-client-auth-DTLSv1.2-request-client

[35-client-auth-DTLSv1.2-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[35-client-auth-DTLSv1.2-request-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-35]
ExpectedResult = Success
Method = DTLS


# ===========================================================

[36-client-auth-DTLSv1.2-require-fail]
ssl_conf = 36-client-auth-DTLSv1.2-require-fail-ssl

[36-client-auth-DTLSv1.2-require-fail-ssl]
server = 36-client-auth-DTLSv1.2-require-fail-server
client = 36-client-auth-DTLSv1.2-require-fail-client

[36-client-auth-DTLSv1.2-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[36-client-auth-DTLSv1.2-require-fail-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-36]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure
Method = DTLS


# ===========================================================

[37-client-auth-DTLSv1.2-require]
ssl_conf = 37-client-auth-DTLSv1.2-require-ssl

[37-client-auth-DTLSv1.2-require-ssl]
server = 37-client-auth-DTLSv1.2-require-server
client = 37-client-auth-DTLSv1.2-require-client

[37-client-auth-DTLSv1.2-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[37-client-auth-DTLSv1.2-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-37]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success
Method = DTLS


# ===========================================================

[38-client-auth-DTLSv1.2-require-non-empty-names]
ssl_conf = 38-client-auth-DTLSv1.2-require-non-empty-names-ssl

[38-client-auth-DTLSv1.2-require-non-empty-names-ssl]
server = 38-client-auth-DTLSv1.2-require-non-empty-names-server
client = 38-client-auth-DTLSv1.2-require-non-empty-names-client

[38-client-auth-DTLSv1.2-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[38-client-auth-DTLSv1.2-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-38]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success
Method = DTLS


# ===========================================================

[39-client-auth-DTLSv1.2-noroot]
ssl_conf = 39-client-auth-DTLSv1.2-noroot-ssl

[39-client-auth-DTLSv1.2-noroot-ssl]
server = 39-client-auth-DTLSv1.2-noroot-server
client = 39-client-auth-DTLSv1.2-noroot-client

[39-client-auth-DTLSv1.2-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[39-client-auth-DTLSv1.2-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-39]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA
Method = DTLS


