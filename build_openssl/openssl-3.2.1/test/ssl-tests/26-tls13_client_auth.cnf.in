# -*- mode: perl; -*-
# Copyright 2018-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test TLSv1.3 certificate authentication
## Similar to 04-client_auth.cnf.in output, but specific for
## TLSv1.3 and post-handshake authentication

use strict;
use warnings;

package ssltests;
use OpenSSL::Test::Utils;

our @tests = (
    {
        name => "server-auth-TLSv1.3",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
        },
        test => {
            "ExpectedResult" => "Success",
        },
    },
    {
        name => "client-auth-TLSv1.3-request",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyMode" => "Request",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
        },
        test => {
            "ExpectedResult" => "Success",
        },
    },
    {
        name => "client-auth-TLSv1.3-require-fail",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "Require",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
        },
        test => {
            "ExpectedResult" => "ServerFail",
            "ExpectedServerAlert" => "CertificateRequired",
        },
    },
    {
        name => "client-auth-TLSv1.3-require",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "ClientSignatureAlgorithms" => "PSS+SHA256",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "Request",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey" => test_pem("ee-key.pem"),
        },
        test => {
            "ExpectedResult" => "Success",
            "ExpectedClientCertType" => "RSA",
            "ExpectedClientSignType" => "RSA-PSS",
            "ExpectedClientSignHash" => "SHA256",
            "ExpectedClientCANames" => "empty"
        },
    },
    {
        name => "client-auth-TLSv1.3-require-non-empty-names",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "ClientSignatureAlgorithms" => "PSS+SHA256",
            "ClientCAFile" => test_pem("root-cert.pem"),
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "Request",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey" => test_pem("ee-key.pem"),
        },
        test => {
            "ExpectedResult" => "Success",
            "ExpectedClientCertType" => "RSA",
            "ExpectedClientSignType" => "RSA-PSS",
            "ExpectedClientSignHash" => "SHA256",
            "ExpectedClientCANames" => test_pem("root-cert.pem"),
        },
    },
    {
        name => "client-auth-TLSv1.3-noroot",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyMode" => "Require",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey" => test_pem("ee-key.pem"),
        },
        test => {
            "ExpectedResult" => "ServerFail",
            "ExpectedServerAlert" => "UnknownCA",
        },
    },
    {
        name => "client-auth-TLSv1.3-request-post-handshake",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyMode" => "RequestPostHandshake",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
        },
        test => {
            "ExpectedResult" => "ServerFail",
            "HandshakeMode" => "PostHandshakeAuth",
        },
    },
    {
        name => "client-auth-TLSv1.3-require-fail-post-handshake",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "RequirePostHandshake",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
        },
        test => {
            "ExpectedResult" => "ServerFail",
            "HandshakeMode" => "PostHandshakeAuth",
        },
    },
    {
        name => "client-auth-TLSv1.3-require-post-handshake",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "ClientSignatureAlgorithms" => "PSS+SHA256",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "RequestPostHandshake",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey" => test_pem("ee-key.pem"),
            extra => {
                "EnablePHA" => "Yes",
            },
        },
        test => {
            "ExpectedResult" => "Success",
            "HandshakeMode" => "PostHandshakeAuth",
            "ExpectedClientCertType" => "RSA",
            "ExpectedClientSignType" => "RSA-PSS",
            "ExpectedClientSignHash" => "SHA256",
            "ExpectedClientCANames" => "empty"
        },
    },
    {
        name => "client-auth-TLSv1.3-require-non-empty-names-post-handshake",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "ClientSignatureAlgorithms" => "PSS+SHA256",
            "ClientCAFile" => test_pem("root-cert.pem"),
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "RequestPostHandshake",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey" => test_pem("ee-key.pem"),
            extra => {
                "EnablePHA" => "Yes",
            },
        },
        test => {
            "ExpectedResult" => "Success",
            "HandshakeMode" => "PostHandshakeAuth",
            "ExpectedClientCertType" => "RSA",
            "ExpectedClientSignType" => "RSA-PSS",
            "ExpectedClientSignHash" => "SHA256",
            "ExpectedClientCANames" => test_pem("root-cert.pem"),
        },
    },
    {
        name => "client-auth-TLSv1.3-noroot-post-handshake",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyMode" => "RequirePostHandshake",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey" => test_pem("ee-key.pem"),
            extra => {
                "EnablePHA" => "Yes",
            },
        },
        test => {
            "ExpectedResult" => "ServerFail",
            "HandshakeMode" => "PostHandshakeAuth",
            "ExpectedServerAlert" => "UnknownCA",
        },
    },
    {
        name => "client-auth-TLSv1.3-request-force-client-post-handshake",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyMode" => "RequestPostHandshake",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            extra => {
                "EnablePHA" => "Yes",
            },
        },
        test => {
            "ExpectedResult" => "Success",
            "HandshakeMode" => "PostHandshakeAuth",
        },
    },
    {
        name => "client-auth-TLSv1.3-request-force-server-post-handshake",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyMode" => "RequestPostHandshake",
            extra => {
                "ForcePHA" => "Yes",
            },
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
        },
        test => {
            "ExpectedResult" => "ClientFail",
            "HandshakeMode" => "PostHandshakeAuth",
        },
    },
    {
        name => "client-auth-TLSv1.3-request-force-both-post-handshake",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyMode" => "RequestPostHandshake",
            extra => {
                "ForcePHA" => "Yes",
            },
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            extra => {
                "EnablePHA" => "Yes",
            },
        },
        test => {
            "ExpectedResult" => "Success",
            "HandshakeMode" => "PostHandshakeAuth",
        },
    },
);
