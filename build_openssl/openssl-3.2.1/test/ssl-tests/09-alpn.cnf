# Generated with generate_ssl_tests.pl

num_tests = 16

test-0 = 0-alpn-simple
test-1 = 1-alpn-server-finds-match
test-2 = 2-alpn-server-honours-server-pref
test-3 = 3-alpn-alert-on-mismatch
test-4 = 4-alpn-no-server-support
test-5 = 5-alpn-no-client-support
test-6 = 6-alpn-with-sni-no-context-switch
test-7 = 7-alpn-with-sni-context-switch
test-8 = 8-alpn-selected-sni-server-supports-alpn
test-9 = 9-alpn-selected-sni-server-does-not-support-alpn
test-10 = 10-alpn-simple-resumption
test-11 = 11-alpn-server-switch-resumption
test-12 = 12-alpn-client-switch-resumption
test-13 = 13-alpn-alert-on-mismatch-resumption
test-14 = 14-alpn-no-server-support-resumption
test-15 = 15-alpn-no-client-support-resumption
# ===========================================================

[0-alpn-simple]
ssl_conf = 0-alpn-simple-ssl

[0-alpn-simple-ssl]
server = 0-alpn-simple-server
client = 0-alpn-simple-client

[0-alpn-simple-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-alpn-simple-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedALPNProtocol = foo
server = 0-alpn-simple-server-extra
client = 0-alpn-simple-client-extra

[0-alpn-simple-server-extra]
ALPNProtocols = foo

[0-alpn-simple-client-extra]
ALPNProtocols = foo


# ===========================================================

[1-alpn-server-finds-match]
ssl_conf = 1-alpn-server-finds-match-ssl

[1-alpn-server-finds-match-ssl]
server = 1-alpn-server-finds-match-server
client = 1-alpn-server-finds-match-client

[1-alpn-server-finds-match-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-alpn-server-finds-match-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedALPNProtocol = bar
server = 1-alpn-server-finds-match-server-extra
client = 1-alpn-server-finds-match-client-extra

[1-alpn-server-finds-match-server-extra]
ALPNProtocols = baz,bar

[1-alpn-server-finds-match-client-extra]
ALPNProtocols = foo,bar


# ===========================================================

[2-alpn-server-honours-server-pref]
ssl_conf = 2-alpn-server-honours-server-pref-ssl

[2-alpn-server-honours-server-pref-ssl]
server = 2-alpn-server-honours-server-pref-server
client = 2-alpn-server-honours-server-pref-client

[2-alpn-server-honours-server-pref-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-alpn-server-honours-server-pref-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedALPNProtocol = bar
server = 2-alpn-server-honours-server-pref-server-extra
client = 2-alpn-server-honours-server-pref-client-extra

[2-alpn-server-honours-server-pref-server-extra]
ALPNProtocols = bar,foo

[2-alpn-server-honours-server-pref-client-extra]
ALPNProtocols = foo,bar


# ===========================================================

[3-alpn-alert-on-mismatch]
ssl_conf = 3-alpn-alert-on-mismatch-ssl

[3-alpn-alert-on-mismatch-ssl]
server = 3-alpn-alert-on-mismatch-server
client = 3-alpn-alert-on-mismatch-client

[3-alpn-alert-on-mismatch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-alpn-alert-on-mismatch-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = ServerFail
ExpectedServerAlert = NoApplicationProtocol
server = 3-alpn-alert-on-mismatch-server-extra
client = 3-alpn-alert-on-mismatch-client-extra

[3-alpn-alert-on-mismatch-server-extra]
ALPNProtocols = baz

[3-alpn-alert-on-mismatch-client-extra]
ALPNProtocols = foo,bar


# ===========================================================

[4-alpn-no-server-support]
ssl_conf = 4-alpn-no-server-support-ssl

[4-alpn-no-server-support-ssl]
server = 4-alpn-no-server-support-server
client = 4-alpn-no-server-support-client

[4-alpn-no-server-support-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-alpn-no-server-support-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
client = 4-alpn-no-server-support-client-extra

[4-alpn-no-server-support-client-extra]
ALPNProtocols = foo


# ===========================================================

[5-alpn-no-client-support]
ssl_conf = 5-alpn-no-client-support-ssl

[5-alpn-no-client-support-ssl]
server = 5-alpn-no-client-support-server
client = 5-alpn-no-client-support-client

[5-alpn-no-client-support-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-alpn-no-client-support-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
server = 5-alpn-no-client-support-server-extra

[5-alpn-no-client-support-server-extra]
ALPNProtocols = foo


# ===========================================================

[6-alpn-with-sni-no-context-switch]
ssl_conf = 6-alpn-with-sni-no-context-switch-ssl

[6-alpn-with-sni-no-context-switch-ssl]
server = 6-alpn-with-sni-no-context-switch-server
client = 6-alpn-with-sni-no-context-switch-client
server2 = 6-alpn-with-sni-no-context-switch-server2

[6-alpn-with-sni-no-context-switch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-alpn-with-sni-no-context-switch-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-alpn-with-sni-no-context-switch-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedALPNProtocol = foo
ExpectedServerName = server1
server = 6-alpn-with-sni-no-context-switch-server-extra
server2 = 6-alpn-with-sni-no-context-switch-server2-extra
client = 6-alpn-with-sni-no-context-switch-client-extra

[6-alpn-with-sni-no-context-switch-server-extra]
ALPNProtocols = foo
ServerNameCallback = IgnoreMismatch

[6-alpn-with-sni-no-context-switch-server2-extra]
ALPNProtocols = bar

[6-alpn-with-sni-no-context-switch-client-extra]
ALPNProtocols = foo,bar
ServerName = server1


# ===========================================================

[7-alpn-with-sni-context-switch]
ssl_conf = 7-alpn-with-sni-context-switch-ssl

[7-alpn-with-sni-context-switch-ssl]
server = 7-alpn-with-sni-context-switch-server
client = 7-alpn-with-sni-context-switch-client
server2 = 7-alpn-with-sni-context-switch-server2

[7-alpn-with-sni-context-switch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-alpn-with-sni-context-switch-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-alpn-with-sni-context-switch-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedALPNProtocol = bar
ExpectedServerName = server2
server = 7-alpn-with-sni-context-switch-server-extra
server2 = 7-alpn-with-sni-context-switch-server2-extra
client = 7-alpn-with-sni-context-switch-client-extra

[7-alpn-with-sni-context-switch-server-extra]
ALPNProtocols = foo
ServerNameCallback = IgnoreMismatch

[7-alpn-with-sni-context-switch-server2-extra]
ALPNProtocols = bar

[7-alpn-with-sni-context-switch-client-extra]
ALPNProtocols = foo,bar
ServerName = server2


# ===========================================================

[8-alpn-selected-sni-server-supports-alpn]
ssl_conf = 8-alpn-selected-sni-server-supports-alpn-ssl

[8-alpn-selected-sni-server-supports-alpn-ssl]
server = 8-alpn-selected-sni-server-supports-alpn-server
client = 8-alpn-selected-sni-server-supports-alpn-client
server2 = 8-alpn-selected-sni-server-supports-alpn-server2

[8-alpn-selected-sni-server-supports-alpn-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-alpn-selected-sni-server-supports-alpn-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-alpn-selected-sni-server-supports-alpn-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedALPNProtocol = bar
ExpectedServerName = server2
server = 8-alpn-selected-sni-server-supports-alpn-server-extra
server2 = 8-alpn-selected-sni-server-supports-alpn-server2-extra
client = 8-alpn-selected-sni-server-supports-alpn-client-extra

[8-alpn-selected-sni-server-supports-alpn-server-extra]
ServerNameCallback = IgnoreMismatch

[8-alpn-selected-sni-server-supports-alpn-server2-extra]
ALPNProtocols = bar

[8-alpn-selected-sni-server-supports-alpn-client-extra]
ALPNProtocols = foo,bar
ServerName = server2


# ===========================================================

[9-alpn-selected-sni-server-does-not-support-alpn]
ssl_conf = 9-alpn-selected-sni-server-does-not-support-alpn-ssl

[9-alpn-selected-sni-server-does-not-support-alpn-ssl]
server = 9-alpn-selected-sni-server-does-not-support-alpn-server
client = 9-alpn-selected-sni-server-does-not-support-alpn-client
server2 = 9-alpn-selected-sni-server-does-not-support-alpn-server2

[9-alpn-selected-sni-server-does-not-support-alpn-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-alpn-selected-sni-server-does-not-support-alpn-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-alpn-selected-sni-server-does-not-support-alpn-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedServerName = server2
server = 9-alpn-selected-sni-server-does-not-support-alpn-server-extra
client = 9-alpn-selected-sni-server-does-not-support-alpn-client-extra

[9-alpn-selected-sni-server-does-not-support-alpn-server-extra]
ALPNProtocols = bar
ServerNameCallback = IgnoreMismatch

[9-alpn-selected-sni-server-does-not-support-alpn-client-extra]
ALPNProtocols = foo,bar
ServerName = server2


# ===========================================================

[10-alpn-simple-resumption]
ssl_conf = 10-alpn-simple-resumption-ssl

[10-alpn-simple-resumption-ssl]
server = 10-alpn-simple-resumption-server
client = 10-alpn-simple-resumption-client
resume-server = 10-alpn-simple-resumption-server
resume-client = 10-alpn-simple-resumption-client

[10-alpn-simple-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-alpn-simple-resumption-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedALPNProtocol = foo
HandshakeMode = Resume
ResumptionExpected = Yes
server = 10-alpn-simple-resumption-server-extra
resume-server = 10-alpn-simple-resumption-server-extra
client = 10-alpn-simple-resumption-client-extra
resume-client = 10-alpn-simple-resumption-client-extra

[10-alpn-simple-resumption-server-extra]
ALPNProtocols = foo

[10-alpn-simple-resumption-client-extra]
ALPNProtocols = foo


# ===========================================================

[11-alpn-server-switch-resumption]
ssl_conf = 11-alpn-server-switch-resumption-ssl

[11-alpn-server-switch-resumption-ssl]
server = 11-alpn-server-switch-resumption-server
client = 11-alpn-server-switch-resumption-client
resume-server = 11-alpn-server-switch-resumption-resume-server
resume-client = 11-alpn-server-switch-resumption-client

[11-alpn-server-switch-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-alpn-server-switch-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-alpn-server-switch-resumption-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedALPNProtocol = baz
HandshakeMode = Resume
ResumptionExpected = Yes
server = 11-alpn-server-switch-resumption-server-extra
resume-server = 11-alpn-server-switch-resumption-resume-server-extra
client = 11-alpn-server-switch-resumption-client-extra
resume-client = 11-alpn-server-switch-resumption-client-extra

[11-alpn-server-switch-resumption-server-extra]
ALPNProtocols = bar,foo

[11-alpn-server-switch-resumption-resume-server-extra]
ALPNProtocols = baz,foo

[11-alpn-server-switch-resumption-client-extra]
ALPNProtocols = foo,bar,baz


# ===========================================================

[12-alpn-client-switch-resumption]
ssl_conf = 12-alpn-client-switch-resumption-ssl

[12-alpn-client-switch-resumption-ssl]
server = 12-alpn-client-switch-resumption-server
client = 12-alpn-client-switch-resumption-client
resume-server = 12-alpn-client-switch-resumption-server
resume-client = 12-alpn-client-switch-resumption-resume-client

[12-alpn-client-switch-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-alpn-client-switch-resumption-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[12-alpn-client-switch-resumption-resume-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedALPNProtocol = bar
HandshakeMode = Resume
ResumptionExpected = Yes
server = 12-alpn-client-switch-resumption-server-extra
resume-server = 12-alpn-client-switch-resumption-server-extra
client = 12-alpn-client-switch-resumption-client-extra
resume-client = 12-alpn-client-switch-resumption-resume-client-extra

[12-alpn-client-switch-resumption-server-extra]
ALPNProtocols = foo,bar,baz

[12-alpn-client-switch-resumption-client-extra]
ALPNProtocols = foo,baz

[12-alpn-client-switch-resumption-resume-client-extra]
ALPNProtocols = bar,baz


# ===========================================================

[13-alpn-alert-on-mismatch-resumption]
ssl_conf = 13-alpn-alert-on-mismatch-resumption-ssl

[13-alpn-alert-on-mismatch-resumption-ssl]
server = 13-alpn-alert-on-mismatch-resumption-server
client = 13-alpn-alert-on-mismatch-resumption-client
resume-server = 13-alpn-alert-on-mismatch-resumption-resume-server
resume-client = 13-alpn-alert-on-mismatch-resumption-client

[13-alpn-alert-on-mismatch-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-alpn-alert-on-mismatch-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-alpn-alert-on-mismatch-resumption-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedResult = ServerFail
ExpectedServerAlert = NoApplicationProtocol
HandshakeMode = Resume
server = 13-alpn-alert-on-mismatch-resumption-server-extra
resume-server = 13-alpn-alert-on-mismatch-resumption-resume-server-extra
client = 13-alpn-alert-on-mismatch-resumption-client-extra
resume-client = 13-alpn-alert-on-mismatch-resumption-client-extra

[13-alpn-alert-on-mismatch-resumption-server-extra]
ALPNProtocols = bar

[13-alpn-alert-on-mismatch-resumption-resume-server-extra]
ALPNProtocols = baz

[13-alpn-alert-on-mismatch-resumption-client-extra]
ALPNProtocols = foo,bar


# ===========================================================

[14-alpn-no-server-support-resumption]
ssl_conf = 14-alpn-no-server-support-resumption-ssl

[14-alpn-no-server-support-resumption-ssl]
server = 14-alpn-no-server-support-resumption-server
client = 14-alpn-no-server-support-resumption-client
resume-server = 14-alpn-no-server-support-resumption-resume-server
resume-client = 14-alpn-no-server-support-resumption-client

[14-alpn-no-server-support-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-alpn-no-server-support-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-alpn-no-server-support-resumption-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
HandshakeMode = Resume
ResumptionExpected = Yes
server = 14-alpn-no-server-support-resumption-server-extra
client = 14-alpn-no-server-support-resumption-client-extra
resume-client = 14-alpn-no-server-support-resumption-client-extra

[14-alpn-no-server-support-resumption-server-extra]
ALPNProtocols = foo

[14-alpn-no-server-support-resumption-client-extra]
ALPNProtocols = foo


# ===========================================================

[15-alpn-no-client-support-resumption]
ssl_conf = 15-alpn-no-client-support-resumption-ssl

[15-alpn-no-client-support-resumption-ssl]
server = 15-alpn-no-client-support-resumption-server
client = 15-alpn-no-client-support-resumption-client
resume-server = 15-alpn-no-client-support-resumption-server
resume-client = 15-alpn-no-client-support-resumption-resume-client

[15-alpn-no-client-support-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-alpn-no-client-support-resumption-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[15-alpn-no-client-support-resumption-resume-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
HandshakeMode = Resume
ResumptionExpected = Yes
server = 15-alpn-no-client-support-resumption-server-extra
resume-server = 15-alpn-no-client-support-resumption-server-extra
client = 15-alpn-no-client-support-resumption-client-extra

[15-alpn-no-client-support-resumption-server-extra]
ALPNProtocols = foo

[15-alpn-no-client-support-resumption-client-extra]
ALPNProtocols = foo


