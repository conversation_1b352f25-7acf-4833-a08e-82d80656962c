# Generated with generate_ssl_tests.pl

num_tests = 9

test-0 = 0-renegotiate-client-no-resume
test-1 = 1-renegotiate-client-resume
test-2 = 2-renegotiate-server-resume
test-3 = 3-renegotiate-client-auth-require
test-4 = 4-renegotiate-client-auth-once
test-5 = 5-renegotiate-aead-to-non-aead
test-6 = 6-renegotiate-non-aead-to-aead
test-7 = 7-renegotiate-non-aead-to-non-aead
test-8 = 8-renegotiate-aead-to-aead
# ===========================================================

[0-renegotiate-client-no-resume]
ssl_conf = 0-renegotiate-client-no-resume-ssl

[0-renegotiate-client-no-resume-ssl]
server = 0-renegotiate-client-no-resume-server
client = 0-renegotiate-client-no-resume-client

[0-renegotiate-client-no-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-renegotiate-client-no-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = DTLS
ResumptionExpected = No
UseSCTP = No


# ===========================================================

[1-renegotiate-client-resume]
ssl_conf = 1-renegotiate-client-resume-ssl

[1-renegotiate-client-resume-ssl]
server = 1-renegotiate-client-resume-server
client = 1-renegotiate-client-resume-client

[1-renegotiate-client-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-renegotiate-client-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = DTLS
ResumptionExpected = Yes
UseSCTP = No


# ===========================================================

[2-renegotiate-server-resume]
ssl_conf = 2-renegotiate-server-resume-ssl

[2-renegotiate-server-resume-ssl]
server = 2-renegotiate-server-resume-server
client = 2-renegotiate-server-resume-client

[2-renegotiate-server-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-renegotiate-server-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = Success
HandshakeMode = RenegotiateServer
Method = DTLS
ResumptionExpected = No
UseSCTP = No


# ===========================================================

[3-renegotiate-client-auth-require]
ssl_conf = 3-renegotiate-client-auth-require-ssl

[3-renegotiate-client-auth-require-ssl]
server = 3-renegotiate-client-auth-require-server
client = 3-renegotiate-client-auth-require-client

[3-renegotiate-client-auth-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[3-renegotiate-client-auth-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success
HandshakeMode = RenegotiateServer
Method = DTLS
ResumptionExpected = No
UseSCTP = No


# ===========================================================

[4-renegotiate-client-auth-once]
ssl_conf = 4-renegotiate-client-auth-once-ssl

[4-renegotiate-client-auth-once-ssl]
server = 4-renegotiate-client-auth-once-server
client = 4-renegotiate-client-auth-once-client

[4-renegotiate-client-auth-once-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Once

[4-renegotiate-client-auth-once-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success
HandshakeMode = RenegotiateServer
Method = DTLS
ResumptionExpected = No
UseSCTP = No


# ===========================================================

[5-renegotiate-aead-to-non-aead]
ssl_conf = 5-renegotiate-aead-to-non-aead-ssl

[5-renegotiate-aead-to-non-aead-ssl]
server = 5-renegotiate-aead-to-non-aead-server
client = 5-renegotiate-aead-to-non-aead-client

[5-renegotiate-aead-to-non-aead-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-renegotiate-aead-to-non-aead-client]
CipherString = AES128-GCM-SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = DTLS
ResumptionExpected = No
UseSCTP = No
client = 5-renegotiate-aead-to-non-aead-client-extra

[5-renegotiate-aead-to-non-aead-client-extra]
RenegotiateCiphers = AES128-SHA


# ===========================================================

[6-renegotiate-non-aead-to-aead]
ssl_conf = 6-renegotiate-non-aead-to-aead-ssl

[6-renegotiate-non-aead-to-aead-ssl]
server = 6-renegotiate-non-aead-to-aead-server
client = 6-renegotiate-non-aead-to-aead-client

[6-renegotiate-non-aead-to-aead-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-renegotiate-non-aead-to-aead-client]
CipherString = AES128-SHA
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = DTLS
ResumptionExpected = No
UseSCTP = No
client = 6-renegotiate-non-aead-to-aead-client-extra

[6-renegotiate-non-aead-to-aead-client-extra]
RenegotiateCiphers = AES128-GCM-SHA256


# ===========================================================

[7-renegotiate-non-aead-to-non-aead]
ssl_conf = 7-renegotiate-non-aead-to-non-aead-ssl

[7-renegotiate-non-aead-to-non-aead-ssl]
server = 7-renegotiate-non-aead-to-non-aead-server
client = 7-renegotiate-non-aead-to-non-aead-client

[7-renegotiate-non-aead-to-non-aead-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-renegotiate-non-aead-to-non-aead-client]
CipherString = AES128-SHA
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = DTLS
ResumptionExpected = No
UseSCTP = No
client = 7-renegotiate-non-aead-to-non-aead-client-extra

[7-renegotiate-non-aead-to-non-aead-client-extra]
RenegotiateCiphers = AES256-SHA


# ===========================================================

[8-renegotiate-aead-to-aead]
ssl_conf = 8-renegotiate-aead-to-aead-ssl

[8-renegotiate-aead-to-aead-ssl]
server = 8-renegotiate-aead-to-aead-server
client = 8-renegotiate-aead-to-aead-client

[8-renegotiate-aead-to-aead-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-renegotiate-aead-to-aead-client]
CipherString = AES128-GCM-SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = DTLS
ResumptionExpected = No
UseSCTP = No
client = 8-renegotiate-aead-to-aead-client-extra

[8-renegotiate-aead-to-aead-client-extra]
RenegotiateCiphers = AES256-GCM-SHA384


