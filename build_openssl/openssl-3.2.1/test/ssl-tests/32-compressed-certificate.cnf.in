# -*- mode: perl; -*-
# Copyright 2016-2016 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## SSL test configurations

package ssltests;

use OpenSSL::Test::Utils;

our @tests = ();

our @tests = (
    {
        name => "no-compressed-certificates",
        server => {
            "Options" => "-TxCertificateCompression,-RxCertificateCompression",
        },
        client => {
            "Options" => "-TxCertificateCompression,-RxCertificateCompression",
                "MinProtocol" => "TLSv1.3",
        },
        test   => {
            "ExpectedResult" => "Success",
            "CompressCertificates" => "Yes",
        },
    },
    {
        name => "server-compressed-certificates",
        server => {
            "Options" => "TxCertificateCompression,RxCertificateCompression",
        },
        client => {
            "Options" => "-TxCertificateCompression,-RxCertificateCompression",
            "MinProtocol" => "TLSv1.3",
        },
        test   => {
            "ExpectedResult" => "Success",
            "CompressCertificates" => "Yes",
        },
    },
    {
        name => "client-compressed-certificates",
        server => {
            "Options" => "-TxCertificateCompression,-RxCertificateCompression",
        },
        client => {
            "Options" => "TxCertificateCompression,RxCertificateCompression",
            "MinProtocol" => "TLSv1.3",
        },
        test   => {
            "ExpectedResult" => "Success",
            "CompressCertificates" => "Yes",
        },
    },
    {
        name => "both-compressed-certificates",
        server => {
            "Options" => "TxCertificateCompression,RxCertificateCompression",
        },
        client => {
            "Options" => "TxCertificateCompression,RxCertificateCompression",
            "MinProtocol" => "TLSv1.3",
        },
        test   => {
            "ExpectedResult" => "Success",
            "CompressCertificates" => "Yes",
        },
    },
    {
        name => "no-compressed-certificates-mtls",
        server => {
            "Options" => "-TxCertificateCompression,-RxCertificateCompression",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "Request",
        },
        client => {
            "Options" => "-TxCertificateCompression,-RxCertificateCompression",
            "MinProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey"  => test_pem("ee-key.pem"),
        },
        test   => {
            "ExpectedResult" => "Success",
            "CompressCertificates" => "Yes",
        },
    },
    {
        name => "server-compressed-certificates-mtls",
        server => {
            "Options" => "TxCertificateCompression,RxCertificateCompression",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "Request",
        },
        client => {
            "Options" => "-TxCertificateCompression,-RxCertificateCompression",
            "MinProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey"  => test_pem("ee-key.pem"),
        },
        test   => {
            "ExpectedResult" => "Success",
            "CompressCertificates" => "Yes",
        },
    },
    {
        name => "client-compressed-certificates-mtls",
        server => {
            "Options" => "-TxCertificateCompression,-RxCertificateCompression",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "Request",
        },
        client => {
            "Options" => "TxCertificateCompression,RxCertificateCompression",
            "MinProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey"  => test_pem("ee-key.pem"),
        },
        test   => {
            "ExpectedResult" => "Success",
            "CompressCertificates" => "Yes",
        },
    },
    {
        name => "both-compressed-certificates-mtls",
        server => {
            "Options" => "TxCertificateCompression,RxCertificateCompression",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "Request",
        },
        client => {
            "Options" => "TxCertificateCompression,RxCertificateCompression",
            "MinProtocol" => "TLSv1.3",
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey"  => test_pem("ee-key.pem"),
        },
        test   => {
            "ExpectedResult" => "Success",
            "CompressCertificates" => "Yes",
        },
    },
);
