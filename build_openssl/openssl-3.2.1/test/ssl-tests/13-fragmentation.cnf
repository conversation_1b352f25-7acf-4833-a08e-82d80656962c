# Generated with generate_ssl_tests.pl

num_tests = 22

test-0 = 0-one-fragment-minus-app-data
test-1 = 1-one-fragment-app-data
test-2 = 2-one-fragment-plus-app-data
test-3 = 3-small-app-data
test-4 = 4-small-app-data-large-fragment-size
test-5 = 5-medium-app-data
test-6 = 6-medium-plus-app-data
test-7 = 7-large-app-data
test-8 = 8-large-app-data-large-fragment-size
test-9 = 9-large-app-data-odd-fragment-size
test-10 = 10-large-app-data-aes-sha1-multibuffer
test-11 = 11-large-app-data-aes-sha2-multibuffer
test-12 = 12-large-app-data-aes-sha1-multibuffer-odd-fragment
test-13 = 13-large-app-data-aes-sha2-multibuffer-odd-fragment
test-14 = 14-small-app-data-aes-sha1-multibuffer
test-15 = 15-small-app-data-aes-sha2-multibuffer
test-16 = 16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled
test-17 = 17-Maximum Fragment Len extension equal FragmentSize to 2048
test-18 = 18-Maximum Fragment Len extension 512 lower than FragmentSize 1024
test-19 = 19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024
test-20 = 20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048
test-21 = 21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024
# ===========================================================

[0-one-fragment-minus-app-data]
ssl_conf = 0-one-fragment-minus-app-data-ssl

[0-one-fragment-minus-app-data-ssl]
server = 0-one-fragment-minus-app-data-server
client = 0-one-fragment-minus-app-data-client

[0-one-fragment-minus-app-data-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-one-fragment-minus-app-data-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ApplicationData = 511


# ===========================================================

[1-one-fragment-app-data]
ssl_conf = 1-one-fragment-app-data-ssl

[1-one-fragment-app-data-ssl]
server = 1-one-fragment-app-data-server
client = 1-one-fragment-app-data-client

[1-one-fragment-app-data-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-one-fragment-app-data-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ApplicationData = 512


# ===========================================================

[2-one-fragment-plus-app-data]
ssl_conf = 2-one-fragment-plus-app-data-ssl

[2-one-fragment-plus-app-data-ssl]
server = 2-one-fragment-plus-app-data-server
client = 2-one-fragment-plus-app-data-client

[2-one-fragment-plus-app-data-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-one-fragment-plus-app-data-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ApplicationData = 513


# ===========================================================

[3-small-app-data]
ssl_conf = 3-small-app-data-ssl

[3-small-app-data-ssl]
server = 3-small-app-data-server
client = 3-small-app-data-client

[3-small-app-data-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-small-app-data-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ApplicationData = 4097


# ===========================================================

[4-small-app-data-large-fragment-size]
ssl_conf = 4-small-app-data-large-fragment-size-ssl

[4-small-app-data-large-fragment-size-ssl]
server = 4-small-app-data-large-fragment-size-server
client = 4-small-app-data-large-fragment-size-client

[4-small-app-data-large-fragment-size-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-small-app-data-large-fragment-size-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ApplicationData = 4097
MaxFragmentSize = 16384


# ===========================================================

[5-medium-app-data]
ssl_conf = 5-medium-app-data-ssl

[5-medium-app-data-ssl]
server = 5-medium-app-data-server
client = 5-medium-app-data-client

[5-medium-app-data-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-medium-app-data-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ApplicationData = 32775


# ===========================================================

[6-medium-plus-app-data]
ssl_conf = 6-medium-plus-app-data-ssl

[6-medium-plus-app-data-ssl]
server = 6-medium-plus-app-data-server
client = 6-medium-plus-app-data-client

[6-medium-plus-app-data-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-medium-plus-app-data-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ApplicationData = 131069


# ===========================================================

[7-large-app-data]
ssl_conf = 7-large-app-data-ssl

[7-large-app-data-ssl]
server = 7-large-app-data-server
client = 7-large-app-data-client

[7-large-app-data-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-large-app-data-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ApplicationData = 1048576


# ===========================================================

[8-large-app-data-large-fragment-size]
ssl_conf = 8-large-app-data-large-fragment-size-ssl

[8-large-app-data-large-fragment-size-ssl]
server = 8-large-app-data-large-fragment-size-server
client = 8-large-app-data-large-fragment-size-client

[8-large-app-data-large-fragment-size-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-large-app-data-large-fragment-size-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ApplicationData = 1048576
MaxFragmentSize = 16384


# ===========================================================

[9-large-app-data-odd-fragment-size]
ssl_conf = 9-large-app-data-odd-fragment-size-ssl

[9-large-app-data-odd-fragment-size-ssl]
server = 9-large-app-data-odd-fragment-size-server
client = 9-large-app-data-odd-fragment-size-client

[9-large-app-data-odd-fragment-size-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-large-app-data-odd-fragment-size-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ApplicationData = 1048576
MaxFragmentSize = 5115


# ===========================================================

[10-large-app-data-aes-sha1-multibuffer]
ssl_conf = 10-large-app-data-aes-sha1-multibuffer-ssl

[10-large-app-data-aes-sha1-multibuffer-ssl]
server = 10-large-app-data-aes-sha1-multibuffer-server
client = 10-large-app-data-aes-sha1-multibuffer-client

[10-large-app-data-aes-sha1-multibuffer-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-large-app-data-aes-sha1-multibuffer-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ApplicationData = 1048576
MaxFragmentSize = 4096


# ===========================================================

[11-large-app-data-aes-sha2-multibuffer]
ssl_conf = 11-large-app-data-aes-sha2-multibuffer-ssl

[11-large-app-data-aes-sha2-multibuffer-ssl]
server = 11-large-app-data-aes-sha2-multibuffer-server
client = 11-large-app-data-aes-sha2-multibuffer-client

[11-large-app-data-aes-sha2-multibuffer-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-large-app-data-aes-sha2-multibuffer-client]
CipherString = AES128-SHA256
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ApplicationData = 1048576
MaxFragmentSize = 4096


# ===========================================================

[12-large-app-data-aes-sha1-multibuffer-odd-fragment]
ssl_conf = 12-large-app-data-aes-sha1-multibuffer-odd-fragment-ssl

[12-large-app-data-aes-sha1-multibuffer-odd-fragment-ssl]
server = 12-large-app-data-aes-sha1-multibuffer-odd-fragment-server
client = 12-large-app-data-aes-sha1-multibuffer-odd-fragment-client

[12-large-app-data-aes-sha1-multibuffer-odd-fragment-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-large-app-data-aes-sha1-multibuffer-odd-fragment-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ApplicationData = 1048579
MaxFragmentSize = 5115


# ===========================================================

[13-large-app-data-aes-sha2-multibuffer-odd-fragment]
ssl_conf = 13-large-app-data-aes-sha2-multibuffer-odd-fragment-ssl

[13-large-app-data-aes-sha2-multibuffer-odd-fragment-ssl]
server = 13-large-app-data-aes-sha2-multibuffer-odd-fragment-server
client = 13-large-app-data-aes-sha2-multibuffer-odd-fragment-client

[13-large-app-data-aes-sha2-multibuffer-odd-fragment-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-large-app-data-aes-sha2-multibuffer-odd-fragment-client]
CipherString = AES128-SHA256
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ApplicationData = 1048573
MaxFragmentSize = 5125


# ===========================================================

[14-small-app-data-aes-sha1-multibuffer]
ssl_conf = 14-small-app-data-aes-sha1-multibuffer-ssl

[14-small-app-data-aes-sha1-multibuffer-ssl]
server = 14-small-app-data-aes-sha1-multibuffer-server
client = 14-small-app-data-aes-sha1-multibuffer-client

[14-small-app-data-aes-sha1-multibuffer-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-small-app-data-aes-sha1-multibuffer-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ApplicationData = 4096
MaxFragmentSize = 4096


# ===========================================================

[15-small-app-data-aes-sha2-multibuffer]
ssl_conf = 15-small-app-data-aes-sha2-multibuffer-ssl

[15-small-app-data-aes-sha2-multibuffer-ssl]
server = 15-small-app-data-aes-sha2-multibuffer-server
client = 15-small-app-data-aes-sha2-multibuffer-client

[15-small-app-data-aes-sha2-multibuffer-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-small-app-data-aes-sha2-multibuffer-client]
CipherString = AES128-SHA256
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ApplicationData = 4096
MaxFragmentSize = 4096


# ===========================================================

[16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled]
ssl_conf = 16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled-ssl

[16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled-ssl]
server = 16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled-server
client = 16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled-client

[16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-16]
ApplicationData = 3072
MaxFragmentSize = 16384
client = 16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled-client-extra

[16-Maximum Fragment Len extension set to 1024 w. FragmentSize disabled-client-extra]
MaxFragmentLenExt = 1024


# ===========================================================

[17-Maximum Fragment Len extension equal FragmentSize to 2048]
ssl_conf = 17-Maximum Fragment Len extension equal FragmentSize to 2048-ssl

[17-Maximum Fragment Len extension equal FragmentSize to 2048-ssl]
server = 17-Maximum Fragment Len extension equal FragmentSize to 2048-server
client = 17-Maximum Fragment Len extension equal FragmentSize to 2048-client

[17-Maximum Fragment Len extension equal FragmentSize to 2048-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[17-Maximum Fragment Len extension equal FragmentSize to 2048-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
ApplicationData = 3072
MaxFragmentSize = 2048
client = 17-Maximum Fragment Len extension equal FragmentSize to 2048-client-extra

[17-Maximum Fragment Len extension equal FragmentSize to 2048-client-extra]
MaxFragmentLenExt = 2048


# ===========================================================

[18-Maximum Fragment Len extension 512 lower than FragmentSize 1024]
ssl_conf = 18-Maximum Fragment Len extension 512 lower than FragmentSize 1024-ssl

[18-Maximum Fragment Len extension 512 lower than FragmentSize 1024-ssl]
server = 18-Maximum Fragment Len extension 512 lower than FragmentSize 1024-server
client = 18-Maximum Fragment Len extension 512 lower than FragmentSize 1024-client

[18-Maximum Fragment Len extension 512 lower than FragmentSize 1024-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-Maximum Fragment Len extension 512 lower than FragmentSize 1024-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-18]
ApplicationData = 3072
MaxFragmentSize = 1024
client = 18-Maximum Fragment Len extension 512 lower than FragmentSize 1024-client-extra

[18-Maximum Fragment Len extension 512 lower than FragmentSize 1024-client-extra]
MaxFragmentLenExt = 512


# ===========================================================

[19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024]
ssl_conf = 19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024-ssl

[19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024-ssl]
server = 19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024-server
client = 19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024-client

[19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-19]
ApplicationData = 3072
MaxFragmentSize = 1024
client = 19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024-client-extra

[19-Maximum Fragment Len extension 1024 lower than FragmentSize 1024-client-extra]
MaxFragmentLenExt = 2048


# ===========================================================

[20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048]
ssl_conf = 20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048-ssl

[20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048-ssl]
server = 20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048-server
client = 20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048-client

[20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-20]
ApplicationData = 8196
MaxFragmentSize = 2048
client = 20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048-client-extra

[20-Maximum Fragment Len extension 4096 greater than FragmentSize 2048-client-extra]
MaxFragmentLenExt = 4096


# ===========================================================

[21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024]
ssl_conf = 21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024-ssl

[21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024-ssl]
server = 21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024-server
client = 21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024-client

[21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-21]
ApplicationData = 3072
MaxFragmentSize = 1024
client = 21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024-client-extra

[21-Maximum Fragment Len extension 2048 greater than FragmentSize 1024-client-extra]
MaxFragmentLenExt = 2048


