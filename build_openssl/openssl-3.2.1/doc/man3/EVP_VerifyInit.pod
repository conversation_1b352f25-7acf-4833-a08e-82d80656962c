=pod

=head1 NAME

EVP_VerifyInit_ex,
EVP_VerifyInit, EVP_VerifyUpdate, EVP_VerifyFinal_ex, EVP_VerifyFinal
- EVP signature verification functions

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_VerifyInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
 int EVP_VerifyUpdate(EVP_MD_CTX *ctx, const void *d, unsigned int cnt);
 int EVP_VerifyFinal_ex(EVP_MD_CTX *ctx, const unsigned char *sigbuf,
                        unsigned int siglen, EVP_PKEY *pkey,
                        OSSL_LIB_CTX *libctx, const char *propq);
 int EVP_VerifyFinal(EVP_MD_CTX *ctx, unsigned char *sigbuf, unsigned int siglen,
                     EVP_PKEY *pkey);

 int EVP_VerifyInit(EVP_MD_CTX *ctx, const EVP_MD *type);

=head1 DESCRIPTION

The EVP signature verification routines are a high-level interface to digital
signatures.

EVP_VerifyInit_ex() sets up verification context I<ctx> to use digest
I<type> from ENGINE I<impl>. I<ctx> must be created by calling
EVP_MD_CTX_new() before calling this function.

EVP_VerifyUpdate() hashes I<cnt> bytes of data at I<d> into the
verification context I<ctx>. This function can be called several times on the
same I<ctx> to include additional data.

EVP_VerifyFinal_ex() verifies the data in I<ctx> using the public key
I<pkey> and I<siglen> bytes in I<sigbuf>.
The library context I<libctx> and property query I<propq> are used when creating
a context to use with the key I<pkey>.

EVP_VerifyFinal() is similar to EVP_VerifyFinal_ex() but uses default
values of NULL for the library context I<libctx> and the property query I<propq>.

EVP_VerifyInit() initializes verification context I<ctx> to use the default
implementation of digest I<type>.

=head1 RETURN VALUES

EVP_VerifyInit_ex() and EVP_VerifyUpdate() return 1 for success and 0 for
failure.

EVP_VerifyFinal_ex() and EVP_VerifyFinal() return 1 for a correct
signature, 0 for failure and a negative value if some other error occurred.

The error codes can be obtained by L<ERR_get_error(3)>.

=head1 NOTES

The B<EVP> interface to digital signatures should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the algorithm used and much more flexible.

The call to EVP_VerifyFinal() internally finalizes a copy of the digest context.
This means that calls to EVP_VerifyUpdate() and EVP_VerifyFinal() can be called
later to digest and verify additional data. Applications may disable this
behavior by setting the EVP_MD_CTX_FLAG_FINALISE context flag via
L<EVP_MD_CTX_set_flags(3)>.

Since only a copy of the digest context is ever finalized the context must
be cleaned up after use by calling EVP_MD_CTX_free() or a memory leak
will occur.

Note that not all providers support continuation, in case the selected
provider does not allow to duplicate contexts EVP_VerifyFinal() will
finalize the digest context and attempting to process additional data via
EVP_VerifyUpdate() will result in an error.

=head1 BUGS

Older versions of this documentation wrongly stated that calls to
EVP_VerifyUpdate() could not be made after calling EVP_VerifyFinal().

Since the public key is passed in the call to EVP_SignFinal() any error
relating to the private key (for example an unsuitable key and digest
combination) will not be indicated until after potentially large amounts of
data have been passed through EVP_SignUpdate().

It is not possible to change the signing parameters using these function.

The previous two bugs are fixed in the newer EVP_DigestVerify*() function.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_SignInit(3)>,
L<EVP_DigestInit(3)>,
L<evp(7)>, L<HMAC(3)>, L<MD2(3)>,
L<MD5(3)>, L<MDC2(3)>, L<RIPEMD160(3)>,
L<SHA1(3)>, L<openssl-dgst(1)>

=head1 HISTORY

The function EVP_VerifyFinal_ex() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
