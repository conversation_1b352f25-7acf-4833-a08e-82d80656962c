=pod

=head1 NAME

OSSL_PROVIDER-null - OpenSSL null provider

=head1 DESCRIPTION

The OpenSSL null provider supplies no algorithms.

It can used to guarantee that the default library context and a fallback
provider will not be accidentally accessed.

=head2 Properties

The null provider defines no properties.

=head1 OPERATIONS AND <PERSON><PERSON><PERSON><PERSON>HMS

The OpenSSL null provider supports no operations and algorithms.

=head1 SEE ALSO

L<provider(7)>

=head1 HISTORY

This functionality was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
