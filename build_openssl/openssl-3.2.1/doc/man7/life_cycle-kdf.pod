=pod

=head1 NAME

life_cycle-kdf - The KDF algorithm life-cycle

=head1 DESCRIPTION

All key derivation functions (KDFs) and pseudo random functions (PRFs)
go through a number of stages in their life-cycle:

=over 4

=item start

This state represents the KDF/PRF before it has been allocated.  It is the
starting state for any life-cycle transitions.

=item newed

This state represents the KDF/PRF after it has been allocated.

=item deriving

This state represents the KDF/PRF when it is set up and capable of generating
output.

=item freed

This state is entered when the KDF/PRF is freed.  It is the terminal state
for all life-cycle transitions.

=back

=head2 State Transition Diagram

The usual life-cycle of a KDF/PRF is illustrated:

=begin man

                     +-------------------+
                     |       start       |
                     +-------------------+
                       |
                       | EVP_KDF_CTX_new
                       v
                     +-------------------+
                     |       newed       | <+
                     +-------------------+  |
                       |                    |
                       | EVP_KDF_derive     |
                       v                    | EVP_KDF_CTX_reset
    EVP_KDF_derive   +-------------------+  |
  + - - - - - - - -  |                   |  |
  '                  |     deriving      |  |
  + - - - - - - - -> |                   | -+
                     +-------------------+
                       |
                       | EVP_KDF_CTX_free
                       v
                     +-------------------+
                     |       freed       |
                     +-------------------+

=end man

=for html <img src="img/kdf.png">

=head2 Formal State Transitions

This section defines all of the legal state transitions.
This is the canonical list.

=begin man

 Function Call                   ------------- Current State -------------
                                 start       newed       deriving    freed
 EVP_KDF_CTX_new                 newed
 EVP_KDF_derive                             deriving     deriving
 EVP_KDF_CTX_free                freed       freed        freed
 EVP_KDF_CTX_reset                           newed        newed
 EVP_KDF_CTX_get_params                      newed       deriving
 EVP_KDF_CTX_set_params                      newed       deriving
 EVP_KDF_CTX_gettable_params                 newed       deriving
 EVP_KDF_CTX_settable_params                 newed       deriving

=end man

=begin html

<table style="border:1px solid; border-collapse:collapse">
<tr><th style="border:1px solid" align="left">Function Call</th>
    <th style="border:1px solid" colspan="4">Current State</th></tr>
<tr><th style="border:1px solid"></th>
    <th style="border:1px solid" align="center">start</th>
    <th style="border:1px solid" align="center">newed</th>
    <th style="border:1px solid" align="center">deriving</th>
    <th style="border:1px solid" align="center">freed</th></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_new</th>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid"></td>
    <td style="border:1px solid"></td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_derive</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_free</th>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_reset</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_get_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_set_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_gettable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_settable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
</table>

=end html

=head1 NOTES

At some point the EVP layer will begin enforcing the transitions described
herein.

=head1 SEE ALSO

L<provider-kdf(7)>, L<EVP_KDF(3)>.

=head1 HISTORY

The provider KDF interface was introduced in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
