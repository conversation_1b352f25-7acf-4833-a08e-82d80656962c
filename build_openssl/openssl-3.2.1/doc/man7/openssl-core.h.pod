=pod

=head1 NAME

openssl/core.h - OpenSSL Core types

=head1 SYNOPSIS

 #include <openssl/core.h>

=head1 DESCRIPTION

The F<< <openssl/core.h> >> header defines a number of public types that
are used to communicate between the OpenSSL libraries and
implementation providers.
These types are designed to minimise the need for intimate knowledge
of internal structures between the OpenSSL libraries and the providers.

The types are:

=over 4

=item L<OSSL_DISPATCH(3)>

=item L<OSSL_ITEM(3)>

=item L<OSSL_ALGORITHM(3)>

=item L<OSSL_PARAM(3)>

=item L<OSSL_CALLBACK(3)>

=item L<OSSL_PASSPHRASE_CALLBACK(3)>

=back

=head1 SEE ALSO

L<openssl-core_dispatch.h(7)>

=head1 HISTORY

The types described here were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
