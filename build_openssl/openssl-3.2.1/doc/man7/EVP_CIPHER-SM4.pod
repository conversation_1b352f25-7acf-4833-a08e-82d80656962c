=pod

=head1 NAME

EVP_CIPHER-SM4 - The SM4 EVP_CIPHER implementations

=head1 DESCRIPTION

Support for SM4 symmetric encryption using the B<EVP_CIPHER> API.

=head2 Algorithm Names

The following algorithms are available in the default provider:

=over 4

=item "SM4-CBC:SM4"

=item "SM4-ECB"

=item "SM4-CTR"

=item "SM4-OFB" or "SM4-OFB128"

=item "SM4-CFB" or "SM4-CFB128"

=item "SM4-GCM"

=item "SM4-CCM"

=item "SM4-XTS"

=back

=head2 Parameters

This implementation supports the parameters described in
L<EVP_EncryptInit(3)/PARAMETERS>.

=head1 NOTES

The SM4-XTS implementation allows streaming to be performed, but each
L<EVP_EncryptUpdate(3)> or L<EVP_DecryptUpdate(3)> call requires each input
to be a multiple of the blocksize. Only the final EVP_EncryptUpdate() or
EVP_DecryptUpdate() call can optionally have an input that is not a multiple
of the blocksize but is larger than one block. In that case ciphertext
stealing (CTS) is used to fill the block.

=head1 SEE ALSO

L<provider-cipher(7)>, L<OSSL_PROVIDER-default(7)>

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
