=pod

=head1 NAME

OSSL_PROVIDER-default - OpenSSL default provider

=head1 DESCRIPTION

The OpenSSL default provider supplies the majority of OpenSSL's diverse
algorithm implementations. If an application doesn't specify anything else
explicitly (e.g. in the application or via config), then this is the
provider that will be used as fallback: It is loaded automatically the
first time that an algorithm is fetched from a provider or a function
acting on providers is called and no other provider has been loaded yet.

If an attempt to load a provider has already been made (whether successful
or not) then the default provider won't be loaded automatically. Therefore
if the default provider is to be used in conjunction with other providers
then it must be loaded explicitly. Automatic loading of the default
provider only occurs a maximum of once; if the default provider is
explicitly unloaded then the default provider will not be automatically
loaded again.

=head2 Properties

The implementations in this provider specifically have this property
defined:

=over 4

=item "provider=default"

=back

It may be used in a property query string with fetching functions such as
L<EVP_MD_fetch(3)> or L<EVP_CIPHER_fetch(3)>, as well as with other
functions that take a property query string, such as
L<EVP_PKEY_CTX_new_from_name(3)>.

It isn't mandatory to query for this property, except to make sure to get
implementations of this provider and none other.

Some implementations may define additional properties.  Exact information is
listed below

=head1 OPERATIONS AND ALGORITHMS

The OpenSSL default provider supports these operations and algorithms:

=head2 Hashing Algorithms / Message Digests

=over 4

=item SHA1, see L<EVP_MD-SHA1(7)>

=item SHA2, see L<EVP_MD-SHA2(7)>

=item SHA3, see L<EVP_MD-SHA3(7)>

=item KECCAK, see L<EVP_MD-KECCAK(7)>

=item KECCAK-KMAC, see L<EVP_MD-KECCAK-KMAC(7)>

=item SHAKE, see L<EVP_MD-SHAKE(7)>

=item BLAKE2, see L<EVP_MD-BLAKE2(7)>

=item SM3, see L<EVP_MD-SM3(7)>

=item MD5, see L<EVP_MD-MD5(7)>

=item MD5-SHA1, see L<EVP_MD-MD5-SHA1(7)>

=item RIPEMD160, see L<EVP_MD-RIPEMD160(7)>

=item NULL, see L<EVP_MD-NULL(7)>

=back

=head2 Symmetric Ciphers

=over 4

=item AES, see L<EVP_CIPHER-AES(7)>

=item ARIA, see L<EVP_CIPHER-ARIA(7)>

=item CAMELLIA, see L<EVP_CIPHER-CAMELLIA(7)>

=item 3DES, see L<EVP_CIPHER-DES(7)>

=item SM4, see L<EVP_CIPHER-SM4(7)>

=item ChaCha20, see L<EVP_CIPHER-CHACHA(7)>

=item ChaCha20-Poly1305, see L<EVP_CIPHER-CHACHA(7)>

=item NULL, see L<EVP_CIPHER-NULL(7)>

=back

=head2 Message Authentication Code (MAC)

=over 4

=item BLAKE2, see L<EVP_MAC-BLAKE2(7)>

=item CMAC, see L<EVP_MAC-CMAC(7)>

=item GMAC, see L<EVP_MAC-GMAC(7)>

=item HMAC, see L<EVP_MAC-HMAC(7)>

=item KMAC, see L<EVP_MAC-KMAC(7)>

=item SIPHASH, see L<EVP_MAC-Siphash(7)>

=item POLY1305, see L<EVP_MAC-Poly1305(7)>

=back

=head2 Key Derivation Function (KDF)

=over 4

=item HKDF, see L<EVP_KDF-HKDF(7)>

=item TLS13-KDF, see L<EVP_KDF-TLS13_KDF(7)>

=item SSKDF, see L<EVP_KDF-SS(7)>

=item PBKDF2, see L<EVP_KDF-PBKDF2(7)>

=item PKCS12KDF, see L<EVP_KDF-PKCS12KDF(7)>

=item SSHKDF, see L<EVP_KDF-SSHKDF(7)>

=item TLS1-PRF, see L<EVP_KDF-TLS1_PRF(7)>

=item KBKDF, see L<EVP_KDF-KB(7)>

=item X942KDF-ASN1, see L<EVP_KDF-X942-ASN1(7)>

=item X942KDF-CONCAT, see L<EVP_KDF-X942-CONCAT(7)>

=item X963KDF, see L<EVP_KDF-X963(7)>

=item SCRYPT, see L<EVP_KDF-SCRYPT(7)>

=item KRB5KDF, see L<EVP_KDF-KRB5KDF(7)>

=item HMAC-DRBG, see L<EVP_KDF-HMAC-DRBG(7)>

=item ARGON2, see L<EVP_KDF-ARGON2(7)>

=back

=head2 Key Exchange

=over 4

=item DH, see L<EVP_KEYEXCH-DH(7)>

=item ECDH, see L<EVP_KEYEXCH-ECDH(7)>

=item X25519, see L<EVP_KEYEXCH-X25519(7)>

=item X448, see L<EVP_KEYEXCH-X448(7)>

=item TLS1-PRF

=item HKDF

=item SCRYPT

=back

=head2 Asymmetric Signature

=over 4

=item DSA, see L<EVP_SIGNATURE-DSA(7)>

=item RSA, see L<EVP_SIGNATURE-RSA(7)>

=item ED25519, see L<EVP_SIGNATURE-ED25519(7)>

=item ED448, see L<EVP_SIGNATURE-ED448(7)>

=item ECDSA, see L<EVP_SIGNATURE-ECDSA(7)>

=item SM2

=item HMAC, see L<EVP_SIGNATURE-HMAC(7)>

=item SIPHASH, see L<EVP_SIGNATURE-Siphash(7)>

=item POLY1305, see L<EVP_SIGNATURE-Poly1305(7)>

=item CMAC, see L<EVP_SIGNATURE-CMAC(7)>

=back

=head2 Asymmetric Cipher

=over 4

=item RSA, see L<EVP_ASYM_CIPHER-RSA(7)>

=item SM2, see L<EVP_ASYM_CIPHER-SM2(7)>

=back

=head2 Asymmetric Key Encapsulation

=over 4

=item RSA, see L<EVP_KEM-RSA(7)>

=item X25519, see L<EVP_KEM-X25519(7)>

=item X448, see L<EVP_KEM-X448(7)>

=item EC, see L<EVP_KEM-EC(7)>

=back

=head2 Asymmetric Key Management

=over 4

=item DH, see L<EVP_KEYMGMT-DH(7)>

=item DHX, see L<EVP_KEYMGMT-DHX(7)>

=item DSA, see L<EVP_KEYMGMT-DSA(7)>

=item RSA, see L<EVP_KEYMGMT-RSA(7)>

=item RSA-PSS

=item EC, see L<EVP_KEYMGMT-EC(7)>

=item X25519, see L<EVP_KEYMGMT-X25519(7)>

=item X448, see L<EVP_KEYMGMT-X448(7)>

=item ED25519, see L<EVP_KEYMGMT-ED25519(7)>

=item ED448, see L<EVP_KEYMGMT-ED448(7)>

=item TLS1-PRF

=item HKDF

=item SCRYPT

=item HMAC, see L<EVP_KEYMGMT-HMAC(7)>

=item SIPHASH, see L<EVP_KEYMGMT-Siphash(7)>

=item POLY1305, see L<EVP_KEYMGMT-Poly1305(7)>

=item CMAC, see L<EVP_KEYMGMT-CMAC(7)>

=item SM2, see L<EVP_KEYMGMT-SM2(7)>

=back

=head2 Random Number Generation

=over 4

=item CTR-DRBG, see L<EVP_RAND-CTR-DRBG(7)>

=item HASH-DRBG, see L<EVP_RAND-HASH-DRBG(7)>

=item HMAC-DRBG, see L<EVP_RAND-HMAC-DRBG(7)>

=item SEED-SRC,  see L<EVP_RAND-SEED-SRC(7)>

=item TEST-RAND, see L<EVP_RAND-TEST-RAND(7)>

=back

In addition to this provider, the "SEED-SRC" algorithm is also available in the
base provider.

=head2 Asymmetric Key Encoder

=over 4

=item RSA

=item RSA-PSS

=item DH

=item DHX

=item DSA

=item EC

=item ED25519

=item ED448

=item X25519

=item X448

=item SM2

=back

In addition to this provider, all of these encoding algorithms are also
available in the base provider. Some of these algorithms may be used in
combination with the FIPS provider.

=head2 Asymmetric Key Decoder

=over 4

=item RSA

=item RSA-PSS

=item DH

=item DHX

=item DSA

=item EC

=item ED25519

=item ED448

=item X25519

=item X448

=item SM2

=item DER

=back

In addition to this provider, all of these decoding algorithms are also
available in the base provider. Some of these algorithms may be used in
combination with the FIPS provider.

=head2 Stores

=over 4

=item file

=item org.openssl.winstore

=back

In addition to this provider, all of these store algorithms are also
available in the base provider.

=head1 SEE ALSO

L<openssl-core.h(7)>, L<openssl-core_dispatch.h(7)>, L<provider(7)>,
L<OSSL_PROVIDER-base(7)>

=head1 HISTORY

The RIPEMD160 digest was added to the default provider in OpenSSL 3.0.7.

All other functionality was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
