=pod

=head1 NAME

EVP_CIPHER-IDEA - The IDEA EVP_CIPHER implementations

=head1 DESCRIPTION

Support for IDEA symmetric encryption using the B<EVP_CIPHER> API.

=head2 Algorithm Names

The following algorithms are available in the legacy provider:

=over 4

=item "IDEA-ECB"

=item "IDEA-CBC"

=item "IDEA-OFB" or "IDEA-OFB64"

=item "IDEA-CFB" or "IDEA-CFB64"

=back

=head2 Parameters

This implementation supports the parameters described in
L<EVP_EncryptInit(3)/PARAMETERS>.

=head1 SEE ALSO

L<provider-cipher(7)>, L<OSSL_PROVIDER-legacy(7)>

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
