=pod

=head1 NAME

EVP_MD-SM3 - The SM3 EVP_MD implementations

=head1 DESCRIPTION

Support for computing SM3 digests through the B<EVP_MD> API.

=head2 Identity

This implementation is only available with the default provider, and is
identified with the name "SM3".

=head2 Gettable Parameters

This implementation supports the common gettable parameters described
in L<EVP_MD-common(7)>.

=head1 SEE ALSO

L<provider-digest(7)>, L<OSSL_PROVIDER-default(7)>

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
