digraph digest {
    bgcolor="transparent";

    begin [label=start, color="#deeaee", style="filled"];
    newed [label=newed, fontcolor="#c94c4c", style="solid"];
    initialised [label=initialised, fontcolor="#c94c4c"];
    updated [label=updated, fontcolor="#c94c4c"];
    finaled [label="finaled", fontcolor="#c94c4c"];
    end [label="freed", color="#deeaee", style="filled"];

    begin -> newed [label="EVP_MD_CTX_new"];
    newed -> initialised [label="EVP_DigestInit"];
    initialised -> updated [label="EVP_DigestUpdate", weight=3];
    updated -> updated [label="EVP_DigestUpdate"];
    updated -> finaled [label="EVP_DigestFinal"];
    updated -> finaled [label="EVP_DigestFinalXOF",
                        fontcolor="#808080", color="#808080"];
    /* Once this works it should go back in:
    finaled -> finaled [taillabel="EVP_DigestFinalXOF",
                        labeldistance=9, labelangle=345,
                        labelfontcolor="#808080", color="#808080"];
    */
    finaled -> end [label="EVP_MD_CTX_free"];
    finaled -> newed [label="EVP_MD_CTX_reset", style=dashed, weight=2,
                      color="#034f84", fontcolor="#034f84"];
    updated -> newed [label="EVP_MD_CTX_reset", style=dashed,
                      color="#034f84", fontcolor="#034f84"];
    updated -> initialised [label="EVP_DigestInit", weight=0, style=dashed,
                            color="#034f84", fontcolor="#034f84"];
    finaled -> initialised [label="EVP_DigestInit", style=dashed,
                            color="#034f84", fontcolor="#034f84"];
}

