#
# To run the demos when linked with a shared library (default) ensure that
# libcrypto is on the library path. For example, to run the
# cms_enc demo:
#
#    LD_LIBRARY_PATH=../.. ./cms_enc

TESTS = cms_comp \
        cms_ddec \
        cms_dec \
        cms_denc \
        cms_enc \
        cms_sign \
        cms_sign2 \
        cms_uncomp \
        cms_ver

CFLAGS  = -I../../include -g
LDFLAGS = -L../..
LDLIBS  = -lcrypto

all: $(TESTS)

clean:
	$(RM) $(TESTS) *.o

cms_%: cms_%.c
	$(CC) $(CFLAGS) $(LDFLAGS) -o "$@" "$<" $(LDLIBS)

test: all
	@echo "\nCMS tests:"
	LD_LIBRARY_PATH=../.. ./cms_enc
	LD_LIBRARY_PATH=../.. ./cms_dec
	LD_LIBRARY_PATH=../.. ./cms_sign2
	LD_LIBRARY_PATH=../.. ./cms_ver
