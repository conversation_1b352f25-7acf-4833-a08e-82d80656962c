/*
 * Copyright 1995-2016 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OSSL_CRYPTO_BF_LOCAL_H
# define OSSL_CRYPTO_BF_LOCAL_H
# include <openssl/opensslconf.h>

/* NOTE - c is not incremented as per n2l */
# define n2ln(c,l1,l2,n) { \
                        c+=n; \
                        l1=l2=0; \
                        switch (n) { \
                        case 8: l2 =((unsigned long)(*(--(c))))    ; \
                        /* fall through */                              \
                        case 7: l2|=((unsigned long)(*(--(c))))<< 8; \
                        /* fall through */                              \
                        case 6: l2|=((unsigned long)(*(--(c))))<<16; \
                        /* fall through */                              \
                        case 5: l2|=((unsigned long)(*(--(c))))<<24; \
                        /* fall through */                              \
                        case 4: l1 =((unsigned long)(*(--(c))))    ; \
                        /* fall through */                              \
                        case 3: l1|=((unsigned long)(*(--(c))))<< 8; \
                        /* fall through */                              \
                        case 2: l1|=((unsigned long)(*(--(c))))<<16; \
                        /* fall through */                              \
                        case 1: l1|=((unsigned long)(*(--(c))))<<24; \
                                } \
                        }

/* NOTE - c is not incremented as per l2n */
# define l2nn(l1,l2,c,n) { \
                        c+=n; \
                        switch (n) { \
                        case 8: *(--(c))=(unsigned char)(((l2)    )&0xff); \
                        /* fall through */                                    \
                        case 7: *(--(c))=(unsigned char)(((l2)>> 8)&0xff); \
                        /* fall through */                                    \
                        case 6: *(--(c))=(unsigned char)(((l2)>>16)&0xff); \
                        /* fall through */                                    \
                        case 5: *(--(c))=(unsigned char)(((l2)>>24)&0xff); \
                        /* fall through */                                    \
                        case 4: *(--(c))=(unsigned char)(((l1)    )&0xff); \
                        /* fall through */                                    \
                        case 3: *(--(c))=(unsigned char)(((l1)>> 8)&0xff); \
                        /* fall through */                                    \
                        case 2: *(--(c))=(unsigned char)(((l1)>>16)&0xff); \
                        /* fall through */                                    \
                        case 1: *(--(c))=(unsigned char)(((l1)>>24)&0xff); \
                                } \
                        }

# undef n2l
# define n2l(c,l)        (l =((unsigned long)(*((c)++)))<<24L, \
                         l|=((unsigned long)(*((c)++)))<<16L, \
                         l|=((unsigned long)(*((c)++)))<< 8L, \
                         l|=((unsigned long)(*((c)++))))

# undef l2n
# define l2n(l,c)        (*((c)++)=(unsigned char)(((l)>>24L)&0xff), \
                         *((c)++)=(unsigned char)(((l)>>16L)&0xff), \
                         *((c)++)=(unsigned char)(((l)>> 8L)&0xff), \
                         *((c)++)=(unsigned char)(((l)     )&0xff))

/*
 * This is actually a big endian algorithm, the most significant byte is used
 * to lookup array 0
 */

# define BF_ENC(LL,R,S,P) ( \
        LL^=P, \
        LL^=((( S[       ((R>>24)&0xff)] + \
                S[0x0100+((R>>16)&0xff)])^ \
                S[0x0200+((R>> 8)&0xff)])+ \
                S[0x0300+((R    )&0xff)])&0xffffffffU \
        )

#endif
