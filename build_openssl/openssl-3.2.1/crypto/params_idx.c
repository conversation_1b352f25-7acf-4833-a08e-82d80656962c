/*
 * WARNING: do not edit!
 * Generated by <PERSON><PERSON><PERSON> from crypto/params_idx.c.in
 *
 * Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */


#include "internal/e_os.h"
#include "internal/param_names.h"
#include <string.h>

/* Machine generated TRIE -- generated by util/perl/OpenSSL/paramnames.pm */
int ossl_param_find_pidx(const char *s)
{
    switch(s[0]) {
    default:
        break;
    case 'a':
        switch(s[1]) {
        default:
            break;
        case 'c':
            if (strcmp("vp-info", s + 2) == 0)
                return PIDX_KDF_PARAM_X942_ACVPINFO;
            break;
        case 'd':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_KDF_PARAM_ARGON2_AD;
            }
            break;
        case 'e':
            if (strcmp("ad", s + 2) == 0)
                return PIDX_CIPHER_PARAM_AEAD;
            break;
        case 'l':
            switch(s[2]) {
            default:
                break;
            case 'g':
                switch(s[3]) {
                default:
                    break;
                case '_':
                    if (strcmp("id_param", s + 4) == 0)
                        return PIDX_CIPHER_PARAM_ALGORITHM_ID_PARAMS;
                    break;
                case 'i':
                    if (strcmp("d-absent", s + 4) == 0)
                        return PIDX_DIGEST_PARAM_ALGID_ABSENT;
                    break;
                case 'o':
                    if (strcmp("rithm-id", s + 4) == 0)
                        return PIDX_SIGNATURE_PARAM_ALGORITHM_ID;
                }
                break;
            case 'i':
                if (strcmp("as", s + 3) == 0)
                    return PIDX_STORE_PARAM_ALIAS;
            }
            break;
        case '\0':
            return PIDX_PKEY_PARAM_EC_A;
        }
        break;
    case 'b':
        switch(s[1]) {
        default:
            break;
        case 'a':
            if (strcmp("sis-type", s + 2) == 0)
                return PIDX_PKEY_PARAM_EC_CHAR2_TYPE;
            break;
        case 'i':
            if (strcmp("ts", s + 2) == 0)
                return PIDX_PKEY_PARAM_BITS;
            break;
        case 'l':
            switch(s[2]) {
            default:
                break;
            case 'o':
                switch(s[3]) {
                default:
                    break;
                case 'c':
                    switch(s[4]) {
                    default:
                        break;
                    case 'k':
                        switch(s[5]) {
                        default:
                            break;
                        case '-':
                            if (strcmp("size", s + 6) == 0)
                                return PIDX_MAC_PARAM_BLOCK_SIZE;
                            break;
                        case '_':
                            if (strcmp("padding", s + 6) == 0)
                                return PIDX_LIBSSL_RECORD_LAYER_PARAM_BLOCK_PADDING;
                            break;
                        case 's':
                            if (strcmp("ize", s + 6) == 0)
                                return PIDX_CIPHER_PARAM_BLOCK_SIZE;
                        }
                    }
                }
            }
            break;
        case 'u':
            if (strcmp("ildinfo", s + 2) == 0)
                return PIDX_PROV_PARAM_BUILDINFO;
            break;
        case '\0':
            return PIDX_PKEY_PARAM_EC_B;
        }
        break;
    case 'c':
        switch(s[1]) {
        default:
            break;
        case '-':
            if (strcmp("rounds", s + 2) == 0)
                return PIDX_MAC_PARAM_C_ROUNDS;
            break;
        case 'e':
            if (strcmp("kalg", s + 2) == 0)
                return PIDX_KDF_PARAM_CEK_ALG;
            break;
        case 'i':
            if (strcmp("pher", s + 2) == 0)
                return PIDX_ALG_PARAM_CIPHER;
            break;
        case 'o':
            switch(s[2]) {
            default:
                break;
            case 'f':
                if (strcmp("actor", s + 3) == 0)
                    return PIDX_PKEY_PARAM_EC_COFACTOR;
                break;
            case 'n':
                switch(s[3]) {
                default:
                    break;
                case 's':
                    if (strcmp("tant", s + 4) == 0)
                        return PIDX_KDF_PARAM_CONSTANT;
                    break;
                case 't':
                    if (strcmp("ext-string", s + 4) == 0)
                        return PIDX_SIGNATURE_PARAM_CONTEXT_STRING;
                }
            }
            break;
        case 't':
            switch(s[2]) {
            default:
                break;
            case 's':
                switch(s[3]) {
                default:
                    break;
                case '_':
                    if (strcmp("mode", s + 4) == 0)
                        return PIDX_CIPHER_PARAM_CTS_MODE;
                    break;
                case '\0':
                    return PIDX_CIPHER_PARAM_CTS;
                }
            }
            break;
        case 'u':
            switch(s[2]) {
            default:
                break;
            case 's':
                switch(s[3]) {
                default:
                    break;
                case 't':
                    switch(s[4]) {
                    default:
                        break;
                    case 'o':
                        switch(s[5]) {
                        default:
                            break;
                        case 'm':
                            switch(s[6]) {
                            default:
                                break;
                            case '-':
                                if (strcmp("iv", s + 7) == 0)
                                    return PIDX_CIPHER_PARAM_CUSTOM_IV;
                                break;
                            case '\0':
                                return PIDX_MAC_PARAM_CUSTOM;
                            }
                        }
                    }
                }
            }
        }
        break;
    case 'd':
        switch(s[1]) {
        default:
            break;
        case '-':
            if (strcmp("rounds", s + 2) == 0)
                return PIDX_MAC_PARAM_D_ROUNDS;
            break;
        case 'a':
            switch(s[2]) {
            default:
                break;
            case 't':
                switch(s[3]) {
                default:
                    break;
                case 'a':
                    switch(s[4]) {
                    default:
                        break;
                    case '-':
                        switch(s[5]) {
                        default:
                            break;
                        case 's':
                            if (strcmp("tructure", s + 6) == 0)
                                return PIDX_OBJECT_PARAM_DATA_STRUCTURE;
                            break;
                        case 't':
                            if (strcmp("ype", s + 6) == 0)
                                return PIDX_OBJECT_PARAM_DATA_TYPE;
                        }
                        break;
                    case '\0':
                        return PIDX_KDF_PARAM_DATA;
                    }
                }
            }
            break;
        case 'e':
            switch(s[2]) {
            default:
                break;
            case 'c':
                if (strcmp("oded-from-explicit", s + 3) == 0)
                    return PIDX_PKEY_PARAM_EC_DECODED_FROM_EXPLICIT_PARAMS;
                break;
            case 'f':
                if (strcmp("ault-digest", s + 3) == 0)
                    return PIDX_PKEY_PARAM_DEFAULT_DIGEST;
                break;
            case 's':
                if (strcmp("c", s + 3) == 0)
                    return PIDX_OBJECT_PARAM_DESC;
            }
            break;
        case 'h':
            if (strcmp("kem-ikm", s + 2) == 0)
                return PIDX_PKEY_PARAM_DHKEM_IKM;
            break;
        case 'i':
            switch(s[2]) {
            default:
                break;
            case 'g':
                switch(s[3]) {
                default:
                    break;
                case 'e':
                    switch(s[4]) {
                    default:
                        break;
                    case 's':
                        switch(s[5]) {
                        default:
                            break;
                        case 't':
                            switch(s[6]) {
                            default:
                                break;
                            case '-':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'n':
                                    if (strcmp("oinit", s + 8) == 0)
                                        return PIDX_MAC_PARAM_DIGEST_NOINIT;
                                    break;
                                case 'o':
                                    if (strcmp("neshot", s + 8) == 0)
                                        return PIDX_MAC_PARAM_DIGEST_ONESHOT;
                                    break;
                                case 'p':
                                    if (strcmp("rops", s + 8) == 0)
                                        return PIDX_ASYM_CIPHER_PARAM_OAEP_DIGEST_PROPS;
                                    break;
                                case 's':
                                    if (strcmp("ize", s + 8) == 0)
                                        return PIDX_PKEY_PARAM_DIGEST_SIZE;
                                }
                                break;
                            case '\0':
                                return PIDX_ALG_PARAM_DIGEST;
                            }
                        }
                    }
                }
                break;
            case 's':
                if (strcmp("tid", s + 3) == 0)
                    return PIDX_PKEY_PARAM_DIST_ID;
            }
            break;
        case 'r':
            if (strcmp("bg-no-trunc-md", s + 2) == 0)
                return PIDX_PROV_PARAM_DRBG_TRUNC_DIGEST;
            break;
        case '\0':
            return PIDX_PKEY_PARAM_RSA_D;
        }
        break;
    case 'e':
        switch(s[1]) {
        default:
            break;
        case 'a':
            if (strcmp("rly_clean", s + 2) == 0)
                return PIDX_KDF_PARAM_EARLY_CLEAN;
            break;
        case 'c':
            if (strcmp("dh-cofactor-mode", s + 2) == 0)
                return PIDX_EXCHANGE_PARAM_EC_ECDH_COFACTOR_MODE;
            break;
        case 'n':
            switch(s[2]) {
            default:
                break;
            case 'c':
                switch(s[3]) {
                default:
                    break;
                case 'o':
                    switch(s[4]) {
                    default:
                        break;
                    case 'd':
                        switch(s[5]) {
                        default:
                            break;
                        case 'e':
                            if (strcmp("d-pub-key", s + 6) == 0)
                                return PIDX_PKEY_PARAM_ENCODED_PUBLIC_KEY;
                            break;
                        case 'i':
                            if (strcmp("ng", s + 6) == 0)
                                return PIDX_PKEY_PARAM_EC_ENCODING;
                        }
                    }
                    break;
                case 'r':
                    if (strcmp("ypt-level", s + 4) == 0)
                        return PIDX_ENCODER_PARAM_ENCRYPT_LEVEL;
                }
                break;
            case 'g':
                if (strcmp("ine", s + 3) == 0)
                    return PIDX_ALG_PARAM_ENGINE;
                break;
            case 't':
                switch(s[3]) {
                default:
                    break;
                case 'r':
                    switch(s[4]) {
                    default:
                        break;
                    case 'o':
                        switch(s[5]) {
                        default:
                            break;
                        case 'p':
                            switch(s[6]) {
                            default:
                                break;
                            case 'y':
                                switch(s[7]) {
                                default:
                                    break;
                                case '_':
                                    if (strcmp("required", s + 8) == 0)
                                        return PIDX_DRBG_PARAM_ENTROPY_REQUIRED;
                                    break;
                                case '\0':
                                    return PIDX_KDF_PARAM_HMACDRBG_ENTROPY;
                                }
                            }
                        }
                    }
                }
            }
            break;
        case '\0':
            return PIDX_PKEY_PARAM_RSA_E;
            break;
        case 'x':
            if (strcmp("pect", s + 2) == 0)
                return PIDX_STORE_PARAM_EXPECT;
        }
        break;
    case 'f':
        switch(s[1]) {
        default:
            break;
        case 'i':
            switch(s[2]) {
            default:
                break;
            case 'e':
                if (strcmp("ld-type", s + 3) == 0)
                    return PIDX_PKEY_PARAM_EC_FIELD_TYPE;
                break;
            case 'n':
                if (strcmp("gerprint", s + 3) == 0)
                    return PIDX_STORE_PARAM_FINGERPRINT;
            }
        }
        break;
    case 'g':
        switch(s[1]) {
        default:
            break;
        case 'e':
            switch(s[2]) {
            default:
                break;
            case 'n':
                switch(s[3]) {
                default:
                    break;
                case 'e':
                    switch(s[4]) {
                    default:
                        break;
                    case 'r':
                        switch(s[5]) {
                        default:
                            break;
                        case 'a':
                            switch(s[6]) {
                            default:
                                break;
                            case 't':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'e':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case '\0':
                                        return PIDX_RAND_PARAM_GENERATE;
                                    }
                                    break;
                                case 'o':
                                    if (strcmp("r", s + 8) == 0)
                                        return PIDX_PKEY_PARAM_EC_GENERATOR;
                                }
                            }
                        }
                    }
                }
            }
            break;
        case 'i':
            if (strcmp("ndex", s + 2) == 0)
                return PIDX_PKEY_PARAM_FFC_GINDEX;
            break;
        case 'r':
            switch(s[2]) {
            default:
                break;
            case 'o':
                switch(s[3]) {
                default:
                    break;
                case 'u':
                    switch(s[4]) {
                    default:
                        break;
                    case 'p':
                        switch(s[5]) {
                        default:
                            break;
                        case '-':
                            if (strcmp("check", s + 6) == 0)
                                return PIDX_PKEY_PARAM_EC_GROUP_CHECK_TYPE;
                            break;
                        case '\0':
                            return PIDX_PKEY_PARAM_GROUP_NAME;
                        }
                    }
                }
            }
            break;
        case '\0':
            return PIDX_PKEY_PARAM_FFC_G;
        }
        break;
    case 'h':
        switch(s[1]) {
        default:
            break;
        case 'a':
            if (strcmp("s-randkey", s + 2) == 0)
                return PIDX_CIPHER_PARAM_HAS_RAND_KEY;
            break;
        case 'i':
            if (strcmp("ndex", s + 2) == 0)
                return PIDX_PKEY_PARAM_FFC_H;
        }
        break;
    case 'i':
        switch(s[1]) {
        default:
            break;
        case 'd':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_KDF_PARAM_PKCS12_ID;
            }
            break;
        case 'k':
            if (strcmp("me", s + 2) == 0)
                return PIDX_KEM_PARAM_IKME;
            break;
        case 'm':
            if (strcmp("plicit-rejection", s + 2) == 0)
                return PIDX_PKEY_PARAM_IMPLICIT_REJECTION;
            break;
        case 'n':
            switch(s[2]) {
            default:
                break;
            case 'c':
                if (strcmp("lude-public", s + 3) == 0)
                    return PIDX_PKEY_PARAM_EC_INCLUDE_PUBLIC;
                break;
            case 'f':
                if (strcmp("o", s + 3) == 0)
                    return PIDX_PASSPHRASE_PARAM_INFO;
                break;
            case 'p':
                if (strcmp("ut-type", s + 3) == 0)
                    return PIDX_STORE_PARAM_INPUT_TYPE;
                break;
            case 's':
                if (strcmp("tance", s + 3) == 0)
                    return PIDX_SIGNATURE_PARAM_INSTANCE;
            }
            break;
        case 't':
            switch(s[2]) {
            default:
                break;
            case 'e':
                switch(s[3]) {
                default:
                    break;
                case 'r':
                    switch(s[4]) {
                    default:
                        break;
                    case 'a':
                        if (strcmp("tion", s + 5) == 0)
                            return PIDX_GEN_PARAM_ITERATION;
                        break;
                    case '\0':
                        return PIDX_KDF_PARAM_ITER;
                    }
                }
            }
            break;
        case 'v':
            switch(s[2]) {
            default:
                break;
            case 'l':
                if (strcmp("en", s + 3) == 0)
                    return PIDX_CIPHER_PARAM_IVLEN;
                break;
            case '\0':
                return PIDX_CIPHER_PARAM_IV;
            }
        }
        break;
    case 'j':
        switch(s[1]) {
        default:
            break;
        case '\0':
            return PIDX_PKEY_PARAM_FFC_COFACTOR;
        }
        break;
    case 'k':
        switch(s[1]) {
        default:
            break;
        case '1':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_EC_CHAR2_PP_K1;
            }
            break;
        case '2':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_EC_CHAR2_PP_K2;
            }
            break;
        case '3':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_EC_CHAR2_PP_K3;
            }
            break;
        case 'a':
            if (strcmp("t", s + 2) == 0)
                return PIDX_SIGNATURE_PARAM_KAT;
            break;
        case 'd':
            switch(s[2]) {
            default:
                break;
            case 'f':
                switch(s[3]) {
                default:
                    break;
                case '-':
                    switch(s[4]) {
                    default:
                        break;
                    case 'd':
                        switch(s[5]) {
                        default:
                            break;
                        case 'i':
                            switch(s[6]) {
                            default:
                                break;
                            case 'g':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'e':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 's':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case 't':
                                            switch(s[10]) {
                                            default:
                                                break;
                                            case '-':
                                                if (strcmp("props", s + 11) == 0)
                                                    return PIDX_EXCHANGE_PARAM_KDF_DIGEST_PROPS;
                                                break;
                                            case '\0':
                                                return PIDX_EXCHANGE_PARAM_KDF_DIGEST;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        break;
                    case 'o':
                        if (strcmp("utlen", s + 5) == 0)
                            return PIDX_EXCHANGE_PARAM_KDF_OUTLEN;
                        break;
                    case 't':
                        if (strcmp("ype", s + 5) == 0)
                            return PIDX_EXCHANGE_PARAM_KDF_TYPE;
                        break;
                    case 'u':
                        if (strcmp("km", s + 5) == 0)
                            return PIDX_EXCHANGE_PARAM_KDF_UKM;
                    }
                }
            }
            break;
        case 'e':
            switch(s[2]) {
            default:
                break;
            case 'y':
                switch(s[3]) {
                default:
                    break;
                case 'b':
                    if (strcmp("its", s + 4) == 0)
                        return PIDX_CIPHER_PARAM_RC2_KEYBITS;
                    break;
                case 'l':
                    if (strcmp("en", s + 4) == 0)
                        return PIDX_CIPHER_PARAM_KEYLEN;
                    break;
                case '\0':
                    return PIDX_KDF_PARAM_KEY;
                }
            }
        }
        break;
    case 'l':
        switch(s[1]) {
        default:
            break;
        case 'a':
            switch(s[2]) {
            default:
                break;
            case 'b':
                if (strcmp("el", s + 3) == 0)
                    return PIDX_KDF_PARAM_LABEL;
                break;
            case 'n':
                if (strcmp("es", s + 3) == 0)
                    return PIDX_KDF_PARAM_ARGON2_LANES;
            }
        }
        break;
    case 'm':
        switch(s[1]) {
        default:
            break;
        case 'a':
            switch(s[2]) {
            default:
                break;
            case 'c':
                switch(s[3]) {
                default:
                    break;
                case 'k':
                    if (strcmp("ey", s + 4) == 0)
                        return PIDX_CIPHER_PARAM_AEAD_MAC_KEY;
                    break;
                case 'l':
                    if (strcmp("en", s + 4) == 0)
                        return PIDX_KDF_PARAM_MAC_SIZE;
                    break;
                case '\0':
                    return PIDX_ALG_PARAM_MAC;
                }
                break;
            case 'n':
                if (strcmp("datory-digest", s + 3) == 0)
                    return PIDX_PKEY_PARAM_MANDATORY_DIGEST;
                break;
            case 'x':
                switch(s[3]) {
                default:
                    break;
                case '-':
                    if (strcmp("size", s + 4) == 0)
                        return PIDX_PKEY_PARAM_MAX_SIZE;
                    break;
                case '_':
                    switch(s[4]) {
                    default:
                        break;
                    case 'a':
                        if (strcmp("dinlen", s + 5) == 0)
                            return PIDX_DRBG_PARAM_MAX_ADINLEN;
                        break;
                    case 'e':
                        switch(s[5]) {
                        default:
                            break;
                        case 'a':
                            if (strcmp("rly_data", s + 6) == 0)
                                return PIDX_LIBSSL_RECORD_LAYER_PARAM_MAX_EARLY_DATA;
                            break;
                        case 'n':
                            if (strcmp("tropylen", s + 6) == 0)
                                return PIDX_DRBG_PARAM_MAX_ENTROPYLEN;
                        }
                        break;
                    case 'f':
                        if (strcmp("rag_len", s + 5) == 0)
                            return PIDX_LIBSSL_RECORD_LAYER_PARAM_MAX_FRAG_LEN;
                        break;
                    case 'n':
                        if (strcmp("oncelen", s + 5) == 0)
                            return PIDX_DRBG_PARAM_MAX_NONCELEN;
                        break;
                    case 'p':
                        if (strcmp("erslen", s + 5) == 0)
                            return PIDX_DRBG_PARAM_MAX_PERSLEN;
                        break;
                    case 'r':
                        if (strcmp("equest", s + 5) == 0)
                            return PIDX_RAND_PARAM_MAX_REQUEST;
                    }
                    break;
                case 'i':
                    if (strcmp("um_length", s + 4) == 0)
                        return PIDX_DRBG_PARAM_MAX_LENGTH;
                    break;
                case 'm':
                    if (strcmp("em_bytes", s + 4) == 0)
                        return PIDX_KDF_PARAM_SCRYPT_MAXMEM;
                }
            }
            break;
        case 'e':
            if (strcmp("mcost", s + 2) == 0)
                return PIDX_KDF_PARAM_ARGON2_MEMCOST;
            break;
        case 'g':
            switch(s[2]) {
            default:
                break;
            case 'f':
                switch(s[3]) {
                default:
                    break;
                case '1':
                    switch(s[4]) {
                    default:
                        break;
                    case '-':
                        switch(s[5]) {
                        default:
                            break;
                        case 'd':
                            if (strcmp("igest", s + 6) == 0)
                                return PIDX_PKEY_PARAM_MGF1_DIGEST;
                            break;
                        case 'p':
                            if (strcmp("roperties", s + 6) == 0)
                                return PIDX_PKEY_PARAM_MGF1_PROPERTIES;
                        }
                    }
                    break;
                case '\0':
                    return PIDX_PKEY_PARAM_MASKGENFUNC;
                }
            }
            break;
        case 'i':
            switch(s[2]) {
            default:
                break;
            case 'c':
                if (strcmp("alg", s + 3) == 0)
                    return PIDX_DIGEST_PARAM_MICALG;
                break;
            case 'n':
                switch(s[3]) {
                default:
                    break;
                case '_':
                    switch(s[4]) {
                    default:
                        break;
                    case 'e':
                        if (strcmp("ntropylen", s + 5) == 0)
                            return PIDX_DRBG_PARAM_MIN_ENTROPYLEN;
                        break;
                    case 'n':
                        if (strcmp("oncelen", s + 5) == 0)
                            return PIDX_DRBG_PARAM_MIN_NONCELEN;
                    }
                    break;
                case 'i':
                    if (strcmp("um_length", s + 4) == 0)
                        return PIDX_DRBG_PARAM_MIN_LENGTH;
                }
            }
            break;
        case 'o':
            switch(s[2]) {
            default:
                break;
            case 'd':
                switch(s[3]) {
                default:
                    break;
                case 'e':
                    switch(s[4]) {
                    default:
                        break;
                    case '\0':
                        return PIDX_CIPHER_PARAM_MODE;
                    }
                    break;
                case 'u':
                    if (strcmp("le-filename", s + 4) == 0)
                        return PIDX_PROV_PARAM_CORE_MODULE_FILENAME;
                }
            }
            break;
        case '\0':
            return PIDX_PKEY_PARAM_EC_CHAR2_M;
        }
        break;
    case 'n':
        switch(s[1]) {
        default:
            break;
        case 'a':
            if (strcmp("me", s + 2) == 0)
                return PIDX_PROV_PARAM_NAME;
            break;
        case 'o':
            switch(s[2]) {
            default:
                break;
            case 'n':
                switch(s[3]) {
                default:
                    break;
                case 'c':
                    switch(s[4]) {
                    default:
                        break;
                    case 'e':
                        switch(s[5]) {
                        default:
                            break;
                        case '-':
                            if (strcmp("type", s + 6) == 0)
                                return PIDX_SIGNATURE_PARAM_NONCE_TYPE;
                            break;
                        case '\0':
                            return PIDX_KDF_PARAM_HMACDRBG_NONCE;
                        }
                    }
                }
            }
            break;
        case 'u':
            if (strcmp("m", s + 2) == 0)
                return PIDX_CIPHER_PARAM_NUM;
            break;
        case '\0':
            return PIDX_KDF_PARAM_SCRYPT_N;
        }
        break;
    case 'o':
        switch(s[1]) {
        default:
            break;
        case 'a':
            if (strcmp("ep-label", s + 2) == 0)
                return PIDX_ASYM_CIPHER_PARAM_OAEP_LABEL;
            break;
        case 'p':
            switch(s[2]) {
            default:
                break;
            case 'e':
                switch(s[3]) {
                default:
                    break;
                case 'n':
                    if (strcmp("ssl-version", s + 4) == 0)
                        return PIDX_PROV_PARAM_CORE_VERSION;
                    break;
                case 'r':
                    if (strcmp("ation", s + 4) == 0)
                        return PIDX_KEM_PARAM_OPERATION;
                }
                break;
            case 't':
                if (strcmp("ions", s + 3) == 0)
                    return PIDX_LIBSSL_RECORD_LAYER_PARAM_OPTIONS;
            }
            break;
        case 'r':
            if (strcmp("der", s + 2) == 0)
                return PIDX_PKEY_PARAM_EC_ORDER;
        }
        break;
    case 'p':
        switch(s[1]) {
        default:
            break;
        case '1':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_RSA_TEST_P1;
            }
            break;
        case '2':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_RSA_TEST_P2;
            }
            break;
        case 'a':
            switch(s[2]) {
            default:
                break;
            case 'd':
                switch(s[3]) {
                default:
                    break;
                case '-':
                    switch(s[4]) {
                    default:
                        break;
                    case 'm':
                        if (strcmp("ode", s + 5) == 0)
                            return PIDX_PKEY_PARAM_PAD_MODE;
                        break;
                    case 't':
                        if (strcmp("ype", s + 5) == 0)
                            return PIDX_DIGEST_PARAM_PAD_TYPE;
                    }
                    break;
                case 'd':
                    if (strcmp("ing", s + 4) == 0)
                        return PIDX_CIPHER_PARAM_PADDING;
                    break;
                case '\0':
                    return PIDX_EXCHANGE_PARAM_PAD;
                }
                break;
            case 'r':
                switch(s[3]) {
                default:
                    break;
                case 't':
                    switch(s[4]) {
                    default:
                        break;
                    case 'y':
                        switch(s[5]) {
                        default:
                            break;
                        case 'u':
                            if (strcmp("-info", s + 6) == 0)
                                return PIDX_KDF_PARAM_X942_PARTYUINFO;
                            break;
                        case 'v':
                            if (strcmp("-info", s + 6) == 0)
                                return PIDX_KDF_PARAM_X942_PARTYVINFO;
                        }
                    }
                }
                break;
            case 's':
                if (strcmp("s", s + 3) == 0)
                    return PIDX_KDF_PARAM_PASSWORD;
            }
            break;
        case 'b':
            if (strcmp("its", s + 2) == 0)
                return PIDX_PKEY_PARAM_FFC_PBITS;
            break;
        case 'c':
            if (strcmp("ounter", s + 2) == 0)
                return PIDX_PKEY_PARAM_FFC_PCOUNTER;
            break;
        case 'k':
            if (strcmp("cs5", s + 2) == 0)
                return PIDX_KDF_PARAM_PKCS5;
            break;
        case 'o':
            switch(s[2]) {
            default:
                break;
            case 'i':
                if (strcmp("nt-format", s + 3) == 0)
                    return PIDX_PKEY_PARAM_EC_POINT_CONVERSION_FORMAT;
                break;
            case 't':
                if (strcmp("ential", s + 3) == 0)
                    return PIDX_GEN_PARAM_POTENTIAL;
            }
            break;
        case 'r':
            switch(s[2]) {
            default:
                break;
            case 'e':
                switch(s[3]) {
                default:
                    break;
                case 'd':
                    if (strcmp("iction_resistance", s + 4) == 0)
                        return PIDX_DRBG_PARAM_PREDICTION_RESISTANCE;
                    break;
                case 'f':
                    if (strcmp("ix", s + 4) == 0)
                        return PIDX_KDF_PARAM_PREFIX;
                }
                break;
            case 'i':
                switch(s[3]) {
                default:
                    break;
                case 'm':
                    if (strcmp("es", s + 4) == 0)
                        return PIDX_PKEY_PARAM_RSA_PRIMES;
                    break;
                case 'v':
                    switch(s[4]) {
                    default:
                        break;
                    case '_':
                        if (strcmp("len", s + 5) == 0)
                            return PIDX_PKEY_PARAM_DH_PRIV_LEN;
                        break;
                    case '\0':
                        return PIDX_PKEY_PARAM_PRIV_KEY;
                    }
                }
                break;
            case 'o':
                switch(s[3]) {
                default:
                    break;
                case 'p':
                    if (strcmp("erties", s + 4) == 0)
                        return PIDX_ALG_PARAM_PROPERTIES;
                    break;
                case 'v':
                    if (strcmp("ider-name", s + 4) == 0)
                        return PIDX_PROV_PARAM_CORE_PROV_NAME;
                }
            }
            break;
        case 'u':
            if (strcmp("b", s + 2) == 0)
                return PIDX_PKEY_PARAM_PUB_KEY;
            break;
        case '\0':
            return PIDX_KDF_PARAM_SCRYPT_P;
        }
        break;
    case 'q':
        switch(s[1]) {
        default:
            break;
        case '1':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_RSA_TEST_Q1;
            }
            break;
        case '2':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_RSA_TEST_Q2;
            }
            break;
        case 'b':
            if (strcmp("its", s + 2) == 0)
                return PIDX_PKEY_PARAM_FFC_QBITS;
            break;
        case '\0':
            return PIDX_PKEY_PARAM_FFC_Q;
            break;
        case 'x':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_EC_PUB_X;
            }
            break;
        case 'y':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_EC_PUB_Y;
            }
        }
        break;
    case 'r':
        switch(s[1]) {
        default:
            break;
        case 'a':
            switch(s[2]) {
            default:
                break;
            case 'n':
                switch(s[3]) {
                default:
                    break;
                case 'd':
                    switch(s[4]) {
                    default:
                        break;
                    case 'k':
                        if (strcmp("ey", s + 5) == 0)
                            return PIDX_CIPHER_PARAM_RANDOM_KEY;
                        break;
                    case 'o':
                        if (strcmp("m_data", s + 5) == 0)
                            return PIDX_DRBG_PARAM_RANDOM_DATA;
                    }
                }
            }
            break;
        case 'e':
            switch(s[2]) {
            default:
                break;
            case 'a':
                switch(s[3]) {
                default:
                    break;
                case 'd':
                    switch(s[4]) {
                    default:
                        break;
                    case '_':
                        switch(s[5]) {
                        default:
                            break;
                        case 'a':
                            if (strcmp("head", s + 6) == 0)
                                return PIDX_LIBSSL_RECORD_LAYER_PARAM_READ_AHEAD;
                            break;
                        case 'b':
                            if (strcmp("uffer_len", s + 6) == 0)
                                return PIDX_LIBSSL_RECORD_LAYER_READ_BUFFER_LEN;
                        }
                    }
                }
                break;
            case 'f':
                if (strcmp("erence", s + 3) == 0)
                    return PIDX_OBJECT_PARAM_REFERENCE;
                break;
            case 's':
                switch(s[3]) {
                default:
                    break;
                case 'e':
                    switch(s[4]) {
                    default:
                        break;
                    case 'e':
                        switch(s[5]) {
                        default:
                            break;
                        case 'd':
                            switch(s[6]) {
                            default:
                                break;
                            case '_':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'c':
                                    if (strcmp("ounter", s + 8) == 0)
                                        return PIDX_DRBG_PARAM_RESEED_COUNTER;
                                    break;
                                case 'r':
                                    if (strcmp("equests", s + 8) == 0)
                                        return PIDX_DRBG_PARAM_RESEED_REQUESTS;
                                    break;
                                case 't':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 'i':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case 'm':
                                            switch(s[10]) {
                                            default:
                                                break;
                                            case 'e':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '_':
                                                    if (strcmp("interval", s + 12) == 0)
                                                        return PIDX_DRBG_PARAM_RESEED_TIME_INTERVAL;
                                                    break;
                                                case '\0':
                                                    return PIDX_DRBG_PARAM_RESEED_TIME;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            break;
        case 'o':
            if (strcmp("unds", s + 2) == 0)
                return PIDX_CIPHER_PARAM_ROUNDS;
            break;
        case 's':
            switch(s[2]) {
            default:
                break;
            case 'a':
                switch(s[3]) {
                default:
                    break;
                case '-':
                    switch(s[4]) {
                    default:
                        break;
                    case 'c':
                        switch(s[5]) {
                        default:
                            break;
                        case 'o':
                            switch(s[6]) {
                            default:
                                break;
                            case 'e':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'f':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 'f':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case 'i':
                                            switch(s[10]) {
                                            default:
                                                break;
                                            case 'c':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case 'i':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case 'e':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case 'n':
                                                            switch(s[14]) {
                                                            default:
                                                                break;
                                                            case 't':
                                                                switch(s[15]) {
                                                                default:
                                                                    break;
                                                                case '1':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case '\0':
                                                                        return PIDX_PKEY_PARAM_RSA_COEFFICIENT1;
                                                                    }
                                                                    break;
                                                                case '2':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case '\0':
                                                                        return PIDX_PKEY_PARAM_RSA_COEFFICIENT2;
                                                                    }
                                                                    break;
                                                                case '3':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case '\0':
                                                                        return PIDX_PKEY_PARAM_RSA_COEFFICIENT3;
                                                                    }
                                                                    break;
                                                                case '4':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case '\0':
                                                                        return PIDX_PKEY_PARAM_RSA_COEFFICIENT4;
                                                                    }
                                                                    break;
                                                                case '5':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case '\0':
                                                                        return PIDX_PKEY_PARAM_RSA_COEFFICIENT5;
                                                                    }
                                                                    break;
                                                                case '6':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case '\0':
                                                                        return PIDX_PKEY_PARAM_RSA_COEFFICIENT6;
                                                                    }
                                                                    break;
                                                                case '7':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case '\0':
                                                                        return PIDX_PKEY_PARAM_RSA_COEFFICIENT7;
                                                                    }
                                                                    break;
                                                                case '8':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case '\0':
                                                                        return PIDX_PKEY_PARAM_RSA_COEFFICIENT8;
                                                                    }
                                                                    break;
                                                                case '9':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case '\0':
                                                                        return PIDX_PKEY_PARAM_RSA_COEFFICIENT9;
                                                                    }
                                                                    break;
                                                                case '\0':
                                                                    return PIDX_PKEY_PARAM_RSA_COEFFICIENT;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        break;
                    case 'e':
                        switch(s[5]) {
                        default:
                            break;
                        case 'x':
                            switch(s[6]) {
                            default:
                                break;
                            case 'p':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'o':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 'n':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case 'e':
                                            switch(s[10]) {
                                            default:
                                                break;
                                            case 'n':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case 't':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case '1':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case '0':
                                                            switch(s[14]) {
                                                            default:
                                                                break;
                                                            case '\0':
                                                                return PIDX_PKEY_PARAM_RSA_EXPONENT10;
                                                            }
                                                            break;
                                                        case '\0':
                                                            return PIDX_PKEY_PARAM_RSA_EXPONENT1;
                                                        }
                                                        break;
                                                    case '2':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case '\0':
                                                            return PIDX_PKEY_PARAM_RSA_EXPONENT2;
                                                        }
                                                        break;
                                                    case '3':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case '\0':
                                                            return PIDX_PKEY_PARAM_RSA_EXPONENT3;
                                                        }
                                                        break;
                                                    case '4':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case '\0':
                                                            return PIDX_PKEY_PARAM_RSA_EXPONENT4;
                                                        }
                                                        break;
                                                    case '5':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case '\0':
                                                            return PIDX_PKEY_PARAM_RSA_EXPONENT5;
                                                        }
                                                        break;
                                                    case '6':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case '\0':
                                                            return PIDX_PKEY_PARAM_RSA_EXPONENT6;
                                                        }
                                                        break;
                                                    case '7':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case '\0':
                                                            return PIDX_PKEY_PARAM_RSA_EXPONENT7;
                                                        }
                                                        break;
                                                    case '8':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case '\0':
                                                            return PIDX_PKEY_PARAM_RSA_EXPONENT8;
                                                        }
                                                        break;
                                                    case '9':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case '\0':
                                                            return PIDX_PKEY_PARAM_RSA_EXPONENT9;
                                                        }
                                                        break;
                                                    case '\0':
                                                        return PIDX_PKEY_PARAM_RSA_EXPONENT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        break;
                    case 'f':
                        switch(s[5]) {
                        default:
                            break;
                        case 'a':
                            switch(s[6]) {
                            default:
                                break;
                            case 'c':
                                switch(s[7]) {
                                default:
                                    break;
                                case 't':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 'o':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case 'r':
                                            switch(s[10]) {
                                            default:
                                                break;
                                            case '1':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '0':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case '\0':
                                                        return PIDX_PKEY_PARAM_RSA_FACTOR10;
                                                    }
                                                    break;
                                                case '\0':
                                                    return PIDX_PKEY_PARAM_RSA_FACTOR1;
                                                }
                                                break;
                                            case '2':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '\0':
                                                    return PIDX_PKEY_PARAM_RSA_FACTOR2;
                                                }
                                                break;
                                            case '3':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '\0':
                                                    return PIDX_PKEY_PARAM_RSA_FACTOR3;
                                                }
                                                break;
                                            case '4':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '\0':
                                                    return PIDX_PKEY_PARAM_RSA_FACTOR4;
                                                }
                                                break;
                                            case '5':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '\0':
                                                    return PIDX_PKEY_PARAM_RSA_FACTOR5;
                                                }
                                                break;
                                            case '6':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '\0':
                                                    return PIDX_PKEY_PARAM_RSA_FACTOR6;
                                                }
                                                break;
                                            case '7':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '\0':
                                                    return PIDX_PKEY_PARAM_RSA_FACTOR7;
                                                }
                                                break;
                                            case '8':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '\0':
                                                    return PIDX_PKEY_PARAM_RSA_FACTOR8;
                                                }
                                                break;
                                            case '9':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case '\0':
                                                    return PIDX_PKEY_PARAM_RSA_FACTOR9;
                                                }
                                                break;
                                            case '\0':
                                                return PIDX_PKEY_PARAM_RSA_FACTOR;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            break;
        case '\0':
            return PIDX_KDF_PARAM_KBKDF_R;
        }
        break;
    case 's':
        switch(s[1]) {
        default:
            break;
        case 'a':
            switch(s[2]) {
            default:
                break;
            case 'f':
                if (strcmp("eprime-generator", s + 3) == 0)
                    return PIDX_PKEY_PARAM_DH_GENERATOR;
                break;
            case 'l':
                switch(s[3]) {
                default:
                    break;
                case 't':
                    switch(s[4]) {
                    default:
                        break;
                    case 'l':
                        if (strcmp("en", s + 5) == 0)
                            return PIDX_PKEY_PARAM_RSA_PSS_SALTLEN;
                        break;
                    case '\0':
                        return PIDX_MAC_PARAM_SALT;
                    }
                }
                break;
            case 'v':
                if (strcmp("e-parameters", s + 3) == 0)
                    return PIDX_ENCODER_PARAM_SAVE_PARAMETERS;
            }
            break;
        case 'e':
            switch(s[2]) {
            default:
                break;
            case 'c':
                switch(s[3]) {
                default:
                    break;
                case 'r':
                    if (strcmp("et", s + 4) == 0)
                        return PIDX_KDF_PARAM_SECRET;
                    break;
                case 'u':
                    switch(s[4]) {
                    default:
                        break;
                    case 'r':
                        switch(s[5]) {
                        default:
                            break;
                        case 'i':
                            switch(s[6]) {
                            default:
                                break;
                            case 't':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'y':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case '-':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case 'b':
                                            if (strcmp("its", s + 10) == 0)
                                                return PIDX_PKEY_PARAM_SECURITY_BITS;
                                            break;
                                        case 'c':
                                            if (strcmp("hecks", s + 10) == 0)
                                                return PIDX_PROV_PARAM_SECURITY_CHECKS;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                break;
            case 'e':
                if (strcmp("d", s + 3) == 0)
                    return PIDX_PKEY_PARAM_FFC_SEED;
                break;
            case 'r':
                if (strcmp("ial", s + 3) == 0)
                    return PIDX_STORE_PARAM_SERIAL;
                break;
            case 's':
                if (strcmp("sion_id", s + 3) == 0)
                    return PIDX_KDF_PARAM_SSHKDF_SESSION_ID;
            }
            break;
        case 'i':
            if (strcmp("ze", s + 2) == 0)
                return PIDX_MAC_PARAM_SIZE;
            break;
        case 'p':
            if (strcmp("eed", s + 2) == 0)
                return PIDX_CIPHER_PARAM_SPEED;
            break;
        case 's':
            if (strcmp("l3-ms", s + 2) == 0)
                return PIDX_DIGEST_PARAM_SSL3_MS;
            break;
        case 't':
            switch(s[2]) {
            default:
                break;
            case '-':
                switch(s[3]) {
                default:
                    break;
                case 'd':
                    if (strcmp("esc", s + 4) == 0)
                        return PIDX_PROV_PARAM_SELF_TEST_DESC;
                    break;
                case 'p':
                    if (strcmp("hase", s + 4) == 0)
                        return PIDX_PROV_PARAM_SELF_TEST_PHASE;
                    break;
                case 't':
                    if (strcmp("ype", s + 4) == 0)
                        return PIDX_PROV_PARAM_SELF_TEST_TYPE;
                }
                break;
            case 'a':
                switch(s[3]) {
                default:
                    break;
                case 't':
                    switch(s[4]) {
                    default:
                        break;
                    case 'e':
                        switch(s[5]) {
                        default:
                            break;
                        case '\0':
                            return PIDX_RAND_PARAM_STATE;
                        }
                        break;
                    case 'u':
                        if (strcmp("s", s + 5) == 0)
                            return PIDX_PROV_PARAM_STATUS;
                    }
                }
                break;
            case 'r':
                switch(s[3]) {
                default:
                    break;
                case 'e':
                    switch(s[4]) {
                    default:
                        break;
                    case 'a':
                        if (strcmp("m_mac", s + 5) == 0)
                            return PIDX_LIBSSL_RECORD_LAYER_PARAM_STREAM_MAC;
                        break;
                    case 'n':
                        if (strcmp("gth", s + 5) == 0)
                            return PIDX_RAND_PARAM_STRENGTH;
                    }
                }
            }
            break;
        case 'u':
            switch(s[2]) {
            default:
                break;
            case 'b':
                if (strcmp("ject", s + 3) == 0)
                    return PIDX_STORE_PARAM_SUBJECT;
                break;
            case 'p':
                switch(s[3]) {
                default:
                    break;
                case 'p':
                    switch(s[4]) {
                    default:
                        break;
                    case '-':
                        switch(s[5]) {
                        default:
                            break;
                        case 'p':
                            switch(s[6]) {
                            default:
                                break;
                            case 'r':
                                if (strcmp("ivinfo", s + 7) == 0)
                                    return PIDX_KDF_PARAM_X942_SUPP_PRIVINFO;
                                break;
                            case 'u':
                                if (strcmp("binfo", s + 7) == 0)
                                    return PIDX_KDF_PARAM_X942_SUPP_PUBINFO;
                            }
                        }
                    }
                }
            }
        }
        break;
    case 't':
        switch(s[1]) {
        default:
            break;
        case 'a':
            switch(s[2]) {
            default:
                break;
            case 'g':
                switch(s[3]) {
                default:
                    break;
                case 'l':
                    if (strcmp("en", s + 4) == 0)
                        return PIDX_CIPHER_PARAM_AEAD_TAGLEN;
                    break;
                case '\0':
                    return PIDX_CIPHER_PARAM_AEAD_TAG;
                }
            }
            break;
        case 'e':
            switch(s[2]) {
            default:
                break;
            case 's':
                switch(s[3]) {
                default:
                    break;
                case 't':
                    switch(s[4]) {
                    default:
                        break;
                    case '_':
                        switch(s[5]) {
                        default:
                            break;
                        case 'e':
                            if (strcmp("ntropy", s + 6) == 0)
                                return PIDX_RAND_PARAM_TEST_ENTROPY;
                            break;
                        case 'n':
                            if (strcmp("once", s + 6) == 0)
                                return PIDX_RAND_PARAM_TEST_NONCE;
                        }
                    }
                }
            }
            break;
        case 'h':
            if (strcmp("reads", s + 2) == 0)
                return PIDX_KDF_PARAM_THREADS;
            break;
        case 'l':
            switch(s[2]) {
            default:
                break;
            case 's':
                switch(s[3]) {
                default:
                    break;
                case '-':
                    switch(s[4]) {
                    default:
                        break;
                    case 'c':
                        if (strcmp("lient-version", s + 5) == 0)
                            return PIDX_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION;
                        break;
                    case 'd':
                        if (strcmp("ata-size", s + 5) == 0)
                            return PIDX_MAC_PARAM_TLS_DATA_SIZE;
                        break;
                    case 'g':
                        switch(s[5]) {
                        default:
                            break;
                        case 'r':
                            switch(s[6]) {
                            default:
                                break;
                            case 'o':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'u':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 'p':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case '-':
                                            switch(s[10]) {
                                            default:
                                                break;
                                            case 'a':
                                                if (strcmp("lg", s + 11) == 0)
                                                    return PIDX_CAPABILITY_TLS_GROUP_ALG;
                                                break;
                                            case 'i':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case 'd':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case '\0':
                                                        return PIDX_CAPABILITY_TLS_GROUP_ID;
                                                    }
                                                    break;
                                                case 's':
                                                    if (strcmp("-kem", s + 12) == 0)
                                                        return PIDX_CAPABILITY_TLS_GROUP_IS_KEM;
                                                }
                                                break;
                                            case 'n':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case 'a':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case 'm':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case 'e':
                                                            switch(s[14]) {
                                                            default:
                                                                break;
                                                            case '-':
                                                                if (strcmp("internal", s + 15) == 0)
                                                                    return PIDX_CAPABILITY_TLS_GROUP_NAME_INTERNAL;
                                                                break;
                                                            case '\0':
                                                                return PIDX_CAPABILITY_TLS_GROUP_NAME;
                                                            }
                                                        }
                                                    }
                                                }
                                                break;
                                            case 's':
                                                if (strcmp("ec-bits", s + 11) == 0)
                                                    return PIDX_CAPABILITY_TLS_GROUP_SECURITY_BITS;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        break;
                    case 'm':
                        switch(s[5]) {
                        default:
                            break;
                        case 'a':
                            switch(s[6]) {
                            default:
                                break;
                            case 'c':
                                switch(s[7]) {
                                default:
                                    break;
                                case '-':
                                    if (strcmp("size", s + 8) == 0)
                                        return PIDX_CIPHER_PARAM_TLS_MAC_SIZE;
                                    break;
                                case '\0':
                                    return PIDX_CIPHER_PARAM_TLS_MAC;
                                }
                                break;
                            case 'x':
                                switch(s[7]) {
                                default:
                                    break;
                                case '-':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 'd':
                                        if (strcmp("tls", s + 9) == 0)
                                            return PIDX_CAPABILITY_TLS_GROUP_MAX_DTLS;
                                        break;
                                    case 't':
                                        if (strcmp("ls", s + 9) == 0)
                                            return PIDX_CAPABILITY_TLS_GROUP_MAX_TLS;
                                    }
                                }
                            }
                            break;
                        case 'i':
                            switch(s[6]) {
                            default:
                                break;
                            case 'n':
                                switch(s[7]) {
                                default:
                                    break;
                                case '-':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 'd':
                                        if (strcmp("tls", s + 9) == 0)
                                            return PIDX_CAPABILITY_TLS_GROUP_MIN_DTLS;
                                        break;
                                    case 't':
                                        if (strcmp("ls", s + 9) == 0)
                                            return PIDX_CAPABILITY_TLS_GROUP_MIN_TLS;
                                    }
                                }
                            }
                            break;
                        case 'u':
                            if (strcmp("lti", s + 6) == 0)
                                return PIDX_CIPHER_PARAM_TLS1_MULTIBLOCK;
                        }
                        break;
                    case 'n':
                        if (strcmp("egotiated-version", s + 5) == 0)
                            return PIDX_ASYM_CIPHER_PARAM_TLS_NEGOTIATED_VERSION;
                        break;
                    case 's':
                        switch(s[5]) {
                        default:
                            break;
                        case 'i':
                            switch(s[6]) {
                            default:
                                break;
                            case 'g':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'a':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 'l':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case 'g':
                                            switch(s[10]) {
                                            default:
                                                break;
                                            case '-':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case 'c':
                                                    if (strcmp("ode-point", s + 12) == 0)
                                                        return PIDX_CAPABILITY_TLS_SIGALG_CODE_POINT;
                                                    break;
                                                case 'h':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case 'a':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case 's':
                                                            switch(s[14]) {
                                                            default:
                                                                break;
                                                            case 'h':
                                                                switch(s[15]) {
                                                                default:
                                                                    break;
                                                                case '-':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case 'n':
                                                                        if (strcmp("ame", s + 17) == 0)
                                                                            return PIDX_CAPABILITY_TLS_SIGALG_HASH_NAME;
                                                                        break;
                                                                    case 'o':
                                                                        if (strcmp("id", s + 17) == 0)
                                                                            return PIDX_CAPABILITY_TLS_SIGALG_HASH_OID;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                    break;
                                                case 'i':
                                                    if (strcmp("ana-name", s + 12) == 0)
                                                        return PIDX_CAPABILITY_TLS_SIGALG_IANA_NAME;
                                                    break;
                                                case 'k':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case 'e':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case 'y':
                                                            switch(s[14]) {
                                                            default:
                                                                break;
                                                            case 't':
                                                                switch(s[15]) {
                                                                default:
                                                                    break;
                                                                case 'y':
                                                                    switch(s[16]) {
                                                                    default:
                                                                        break;
                                                                    case 'p':
                                                                        switch(s[17]) {
                                                                        default:
                                                                            break;
                                                                        case 'e':
                                                                            switch(s[18]) {
                                                                            default:
                                                                                break;
                                                                            case '-':
                                                                                if (strcmp("oid", s + 19) == 0)
                                                                                    return PIDX_CAPABILITY_TLS_SIGALG_KEYTYPE_OID;
                                                                                break;
                                                                            case '\0':
                                                                                return PIDX_CAPABILITY_TLS_SIGALG_KEYTYPE;
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                    break;
                                                case 'n':
                                                    if (strcmp("ame", s + 12) == 0)
                                                        return PIDX_CAPABILITY_TLS_SIGALG_NAME;
                                                    break;
                                                case 'o':
                                                    if (strcmp("id", s + 12) == 0)
                                                        return PIDX_CAPABILITY_TLS_SIGALG_OID;
                                                    break;
                                                case 's':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case 'e':
                                                        if (strcmp("c-bits", s + 13) == 0)
                                                            return PIDX_CAPABILITY_TLS_SIGALG_SECURITY_BITS;
                                                        break;
                                                    case 'i':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case 'g':
                                                            switch(s[14]) {
                                                            default:
                                                                break;
                                                            case '-':
                                                                switch(s[15]) {
                                                                default:
                                                                    break;
                                                                case 'n':
                                                                    if (strcmp("ame", s + 16) == 0)
                                                                        return PIDX_CAPABILITY_TLS_SIGALG_SIG_NAME;
                                                                    break;
                                                                case 'o':
                                                                    if (strcmp("id", s + 16) == 0)
                                                                        return PIDX_CAPABILITY_TLS_SIGALG_SIG_OID;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        break;
                    case 'v':
                        if (strcmp("ersion", s + 5) == 0)
                            return PIDX_CIPHER_PARAM_TLS_VERSION;
                    }
                    break;
                case '1':
                    switch(s[4]) {
                    default:
                        break;
                    case '-':
                        if (strcmp("prf-ems-check", s + 5) == 0)
                            return PIDX_PROV_PARAM_TLS1_PRF_EMS_CHECK;
                        break;
                    case 'm':
                        switch(s[5]) {
                        default:
                            break;
                        case 'u':
                            switch(s[6]) {
                            default:
                                break;
                            case 'l':
                                switch(s[7]) {
                                default:
                                    break;
                                case 't':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case 'i':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case '_':
                                            switch(s[10]) {
                                            default:
                                                break;
                                            case 'a':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case 'a':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case 'd':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case 'p':
                                                            if (strcmp("acklen", s + 14) == 0)
                                                                return PIDX_CIPHER_PARAM_TLS1_MULTIBLOCK_AAD_PACKLEN;
                                                            break;
                                                        case '\0':
                                                            return PIDX_CIPHER_PARAM_TLS1_MULTIBLOCK_AAD;
                                                        }
                                                    }
                                                }
                                                break;
                                            case 'e':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case 'n':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case 'c':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case 'i':
                                                            if (strcmp("n", s + 14) == 0)
                                                                return PIDX_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC_IN;
                                                            break;
                                                        case 'l':
                                                            if (strcmp("en", s + 14) == 0)
                                                                return PIDX_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC_LEN;
                                                            break;
                                                        case '\0':
                                                            return PIDX_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC;
                                                        }
                                                    }
                                                }
                                                break;
                                            case 'i':
                                                if (strcmp("nterleave", s + 11) == 0)
                                                    return PIDX_CIPHER_PARAM_TLS1_MULTIBLOCK_INTERLEAVE;
                                                break;
                                            case 'm':
                                                switch(s[11]) {
                                                default:
                                                    break;
                                                case 'a':
                                                    switch(s[12]) {
                                                    default:
                                                        break;
                                                    case 'x':
                                                        switch(s[13]) {
                                                        default:
                                                            break;
                                                        case 'b':
                                                            if (strcmp("ufsz", s + 14) == 0)
                                                                return PIDX_CIPHER_PARAM_TLS1_MULTIBLOCK_MAX_BUFSIZE;
                                                            break;
                                                        case 's':
                                                            if (strcmp("ndfrag", s + 14) == 0)
                                                                return PIDX_CIPHER_PARAM_TLS1_MULTIBLOCK_MAX_SEND_FRAGMENT;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 'a':
                    switch(s[4]) {
                    default:
                        break;
                    case 'a':
                        switch(s[5]) {
                        default:
                            break;
                        case 'd':
                            switch(s[6]) {
                            default:
                                break;
                            case 'p':
                                if (strcmp("ad", s + 7) == 0)
                                    return PIDX_CIPHER_PARAM_AEAD_TLS1_AAD_PAD;
                                break;
                            case '\0':
                                return PIDX_CIPHER_PARAM_AEAD_TLS1_AAD;
                            }
                        }
                    }
                    break;
                case 'i':
                    switch(s[4]) {
                    default:
                        break;
                    case 'v':
                        switch(s[5]) {
                        default:
                            break;
                        case 'f':
                            if (strcmp("ixed", s + 6) == 0)
                                return PIDX_CIPHER_PARAM_AEAD_TLS1_IV_FIXED;
                            break;
                        case 'g':
                            if (strcmp("en", s + 6) == 0)
                                return PIDX_CIPHER_PARAM_AEAD_TLS1_GET_IV_GEN;
                            break;
                        case 'i':
                            if (strcmp("nv", s + 6) == 0)
                                return PIDX_CIPHER_PARAM_AEAD_TLS1_SET_IV_INV;
                        }
                    }
                    break;
                case 't':
                    if (strcmp("ree", s + 4) == 0)
                        return PIDX_LIBSSL_RECORD_LAYER_PARAM_TLSTREE;
                }
            }
            break;
        case 'p':
            switch(s[2]) {
            default:
                break;
            case '\0':
                return PIDX_PKEY_PARAM_EC_CHAR2_TP_BASIS;
            }
            break;
        case 'y':
            if (strcmp("pe", s + 2) == 0)
                return PIDX_OBJECT_PARAM_TYPE;
        }
        break;
    case 'u':
        switch(s[1]) {
        default:
            break;
        case 'k':
            if (strcmp("m", s + 2) == 0)
                return PIDX_KDF_PARAM_UKM;
            break;
        case 'p':
            if (strcmp("dated-iv", s + 2) == 0)
                return PIDX_CIPHER_PARAM_UPDATED_IV;
            break;
        case 's':
            switch(s[2]) {
            default:
                break;
            case 'e':
                switch(s[3]) {
                default:
                    break;
                case '-':
                    switch(s[4]) {
                    default:
                        break;
                    case 'b':
                        if (strcmp("its", s + 5) == 0)
                            return PIDX_CIPHER_PARAM_USE_BITS;
                        break;
                    case 'c':
                        if (strcmp("ofactor-flag", s + 5) == 0)
                            return PIDX_PKEY_PARAM_USE_COFACTOR_FLAG;
                        break;
                    case 'k':
                        if (strcmp("eybits", s + 5) == 0)
                            return PIDX_KDF_PARAM_X942_USE_KEYBITS;
                        break;
                    case 'l':
                        switch(s[5]) {
                        default:
                            break;
                        case '\0':
                            return PIDX_KDF_PARAM_KBKDF_USE_L;
                        }
                        break;
                    case 's':
                        if (strcmp("eparator", s + 5) == 0)
                            return PIDX_KDF_PARAM_KBKDF_USE_SEPARATOR;
                    }
                    break;
                case '_':
                    switch(s[4]) {
                    default:
                        break;
                    case 'd':
                        if (strcmp("erivation_function", s + 5) == 0)
                            return PIDX_DRBG_PARAM_USE_DF;
                        break;
                    case 'e':
                        if (strcmp("tm", s + 5) == 0)
                            return PIDX_LIBSSL_RECORD_LAYER_PARAM_USE_ETM;
                    }
                }
            }
        }
        break;
    case 'v':
        switch(s[1]) {
        default:
            break;
        case 'a':
            switch(s[2]) {
            default:
                break;
            case 'l':
                switch(s[3]) {
                default:
                    break;
                case 'i':
                    switch(s[4]) {
                    default:
                        break;
                    case 'd':
                        switch(s[5]) {
                        default:
                            break;
                        case 'a':
                            switch(s[6]) {
                            default:
                                break;
                            case 't':
                                switch(s[7]) {
                                default:
                                    break;
                                case 'e':
                                    switch(s[8]) {
                                    default:
                                        break;
                                    case '-':
                                        switch(s[9]) {
                                        default:
                                            break;
                                        case 'g':
                                            switch(s[10]) {
                                            default:
                                                break;
                                            case '\0':
                                                return PIDX_PKEY_PARAM_FFC_VALIDATE_G;
                                            }
                                            break;
                                        case 'l':
                                            if (strcmp("egacy", s + 10) == 0)
                                                return PIDX_PKEY_PARAM_FFC_VALIDATE_LEGACY;
                                            break;
                                        case 'p':
                                            if (strcmp("q", s + 10) == 0)
                                                return PIDX_PKEY_PARAM_FFC_VALIDATE_PQ;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            break;
        case 'e':
            if (strcmp("rsion", s + 2) == 0)
                return PIDX_PROV_PARAM_VERSION;
        }
        break;
    case 'x':
        switch(s[1]) {
        default:
            break;
        case 'c':
            if (strcmp("ghash", s + 2) == 0)
                return PIDX_KDF_PARAM_SSHKDF_XCGHASH;
            break;
        case 'o':
            switch(s[2]) {
            default:
                break;
            case 'f':
                switch(s[3]) {
                default:
                    break;
                case 'l':
                    if (strcmp("en", s + 4) == 0)
                        return PIDX_DIGEST_PARAM_XOFLEN;
                    break;
                case '\0':
                    return PIDX_MAC_PARAM_XOF;
                }
            }
            break;
        case 'p':
            switch(s[2]) {
            default:
                break;
            case '1':
                switch(s[3]) {
                default:
                    break;
                case '\0':
                    return PIDX_PKEY_PARAM_RSA_TEST_XP1;
                }
                break;
            case '2':
                switch(s[3]) {
                default:
                    break;
                case '\0':
                    return PIDX_PKEY_PARAM_RSA_TEST_XP2;
                }
                break;
            case '\0':
                return PIDX_PKEY_PARAM_RSA_TEST_XP;
            }
            break;
        case 'q':
            switch(s[2]) {
            default:
                break;
            case '1':
                switch(s[3]) {
                default:
                    break;
                case '\0':
                    return PIDX_PKEY_PARAM_RSA_TEST_XQ1;
                }
                break;
            case '2':
                switch(s[3]) {
                default:
                    break;
                case '\0':
                    return PIDX_PKEY_PARAM_RSA_TEST_XQ2;
                }
                break;
            case '\0':
                return PIDX_PKEY_PARAM_RSA_TEST_XQ;
            }
            break;
        case 't':
            if (strcmp("s_standard", s + 2) == 0)
                return PIDX_CIPHER_PARAM_XTS_STANDARD;
        }
    }
    return -1;
}

/* End of TRIE */
