<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<!-- Copyright Aleksey Gurtovoy 2006. Distributed under the Boost -->
<!-- Software License, Version 1.0. (See accompanying -->
<!-- file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt) -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="Docutils 0.3.6: http://docutils.sourceforge.net/" />
<title>THE BOOST MPL LIBRARY</title>
<meta name="copyright" content="Copyright ©  <PERSON><PERSON><PERSON> and <PERSON>, 2002-2004." />
<link rel="stylesheet" href="style.css" type="text/css" />
</head>
<body class="docframe">
<a class="reference" href="http://www.boost.org" target="_top">
    <img align="left" alt="www.boost.org" src="../../../boost.png"/>
</a>
<h1 class="title">THE BOOST MPL LIBRARY</h1>
<table class="docinfo" frame="void" rules="none">
<col class="docinfo-name" />
<col class="docinfo-content" />
<tbody valign="top">
<tr><th class="docinfo-name">Copyright:</th>
<td>Copyright ©  Aleksey Gurtovoy and David Abrahams, 2002-2004.</td></tr>
<tr class="field"><th class="docinfo-name">License:</th><td class="field-body">Distributed under the Boost Software License, Version 1.0. (See 
accompanying file <tt class="literal"><span class="pre">LICENSE_1_0.txt</span></tt> or copy at 
<a class="reference" href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)</td>
</tr>
</tbody>
</table>
<div class="document" id="the-boost-mpl-library">
<p>The Boost.MPL library is a general-purpose, high-level C++ 
template metaprogramming framework of compile-time algorithms, sequences and 
metafunctions. It provides a conceptual foundation and an extensive
set of powerful and coherent tools that make doing explict metaprogramming 
in C++ as easy and enjoyable as possible within the current language.</p>
<p>There are several places to start when getting familiar with the library,
depending on what you know about metaprogramming in general and
C++ template metaprogramming in particular.  Starting with
Boost version 1.32, the MPL comes with both an <a class="reference" href="./tutorial/tutorial-metafunctions.html">in-depth tutorial</a> on its 
fundamental concepts and an annotated <a class="reference" href="./tutorial/reference-manual.html">reference manual</a> covering all concepts 
and components in the library. While the tutorial
only uncovers a bit of what there is to C++ metaprogramming and the MPL,
it's a perfect place to start if you are only somewhat familiar with the 
subject. At the very least, after you're done with it, you will be able to put
the rest of the stuff that appears here into context. The reference manual
is <em>the</em> resource for you if you plan — or are already doing — some actual
development with the library, but a casual reading is still allowed and likely
to bring many insights on the framework's organization and underlying
ideas. Finally, the top-level sections in this document that you 
see highlighted below might also offer something of interest.</p>
<p>So, welcome, and happy metaprogramming!</p>
<dl>
<dt><strong>Important</strong>:</dt>
<dd>There have been some major interface changes in the library since the last 
public release, many of which are <em>not</em> backward compatible. If you are 
a seasoned MPL user, be prepared to have to modify your code after 
upgrading. Please refer to the <a class="reference" href="./tutorial/changes-in-boost-1-32-0.html">1.32 release changelog</a>
for the detailed information about the changes.</dd>
</dl>
<!-- .. section-numbering:: -->
<ul class="toc simple" id="outline">
<li><a class="reference" href="./tutorial/tutorial-metafunctions.html" id="id40" name="id40">Tutorial: Metafunctions and Higher-Order Metaprogramming</a></li>
<li><a class="reference" href="./tutorial/reference-manual.html" id="id61" name="id61">Reference Manual</a></li>
<li><a class="reference" href="./tutorial/changelog-history.html" id="id62" name="id62">Changelog &amp; History</a></li>
<li><a class="reference" href="./tutorial/technical-details.html" id="id70" name="id70">Technical Details</a></li>
<li><a class="reference" href="./tutorial/resources.html" id="id77" name="id77">Resources</a></li>
<li><a class="reference" href="./tutorial/acknowledgements.html" id="id78" name="id78">Acknowledgements</a></li>
<li><a class="reference" href="./tutorial/tutorial_toc.html">Full TOC</a></li>
</ul>
</div>
<hr class="footer" />
<div class="footer">
Generated on: 2004-11-15 12:20 UTC.
Generated by <a class="reference" href="http://docutils.sourceforge.net/" target="_top">Docutils</a> from <a class="reference" href="http://docutils.sourceforge.net/rst.html" target="_top">reStructuredText</a> source.
</div>
</body>
</html>
