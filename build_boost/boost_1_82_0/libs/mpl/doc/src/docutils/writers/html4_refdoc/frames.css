/*
Copyright <PERSON><PERSON><PERSON> Gurtovoy 2004-2008

Distributed under the Boost Software License, Version 1.0. 
(See accompanying file LICENSE_1_0.txt or copy at 
http://www.boost.org/LICENSE_1_0.txt)
*/

@import url(default.css);

div.section h1
{
    margin-top: 0pt;
    margin-bottom: 0pt;
}


div.section h3
{
    margin-bottom: 0pt;
    padding-bottom: 0pt;

/*
    padding-left: 1pt;
    border-style: none none solid none ;
    border-width: 2px;
    border-color: #f0a0a0;
*/
}

p
{
    margin-top: 7pt;
    padding-top: 0pt;
}

pre.literal-block
{
    border-style: none none none solid;
    border-width: 1px;
    border-color: black;
    padding-top: 1pt;
    padding-bottom: 1pt;
    padding-left: 1em;
    margin-top: 10pt;
    margin-left: 0pt;
    background-color: #f5f5f5;
}

td pre.literal-block
{
    border-style: none;
    margin-top: 0pt;
    padding-left: 1pt;
}

tt.literal {
    background-color: #f5f5f5;
}

body.docframe {
    background: #fffff5 url(manual.png) no-repeat top right fixed;
    margin-right: 25pt;
}

/*
span.navigation-group {
    background-color: #f0f5ff;
}
*/

table
{
    border: solid 1px; 
    border-collapse: collapse;
}

table td
{
    margin-top: 0pt;
    margin-bottom: 0pt;
    padding-top: 2pt; 
    padding-bottom: 3pt; 
}

a.ref-subsection-title
{
    text-decoration: none;
    color: black;
}

table.table
{
    border: solid 1px black; 
    border-collapse: collapse;
}

table.table td
, table.table th
{
    border: solid 1px black; 
}

table.wrapper
{
    border: 0px; 
    width: 100%;
    margin-top: 0px;
    margin-left: 0px;
    margin-bottom: 0px;
}

table.wrapper td
{
    padding-left: 0px;
    padding-right: 0px;
    padding-bottom: 0px;
    vertical-align: middle;
}

table.wrapper td.right
{
    padding-left: 0px;
    text-align: right;
}

table.wrapper td.right img
{
    float: right;
    border-width: 0px;
}

/*
a:link,
a:visited
{
    color: #505050;
}

sup a:link,
sup a:visited,
a.interlink:link,
a.interlink:visited
{
    color: #505050;
    text-decoration: none;
}
*/

a.subsection-title
{
    color: black;
    text-decoration: none;
}

 a.identifier
,a.header
{
    color: black;
    text-decoration: none;
}

 a.identifier:hover
,a.header:hover
{
/*    color: #0000c0;*/
    background-color: #eeeeee;
}


hr.navigation-bar-separator {
    width: 100%;
    clear: both;
}

span.navigation-bar {
    float: left;
}

span.page-location {
    float: right;
}

body.tocframe ul.auto-toc,
ul.auto-toc {
    list-style-type: none;
    margin-left: 0pt;
}

ul.auto-toc li li,
body.tocframe ul.auto-toc li li {
    list-style-type: none;
    margin-left: 15pt;
}

body.tocframe ul.toc,
ul.toc {
    margin-left: 0pt;
}

body.tocframe ul.toc li,
ul.toc li {
    list-style-type: circle;
    margin-left: 15pt;
}
