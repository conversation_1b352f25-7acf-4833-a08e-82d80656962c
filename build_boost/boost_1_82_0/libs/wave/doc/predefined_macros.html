<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Predefined Macros</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link href="theme/style.css" rel="stylesheet" type="text/css">
</head>

<body>
<table width="100%" border="0" cellspacing="2" background="theme/bkd2.gif">
  <tr> 
    <td width="21"> <h1></h1></td>
    <td width="885"> <font face="Verdana, Arial, Helvetica, sans-serif"><b><font size="6">Predefined 
      Macros </font></b></font></td>
    <td width="96"><a href="http://www.boost.org"><img src="theme/wave.gif" width="93" height="68" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr> 
    <td width="10"></td>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="class_reference_inptpolcy.html"><img src="theme/l_arr.gif" width="20" height="19" border="0"></a></td>
    <td width="30"><a href="macro_expansion_process.html"><img src="theme/r_arr.gif" border="0"></a></td>
  </tr>
</table>
<p dir="ltr">The following table lists the macros, which are predefined by the 
  <tt>Wave</tt> library. Some of these (all except the <code class="keyword">__LINE__</code>, 
  <code class="keyword">__FILE__</code>, <code class="keyword">__BASE_FILE__</code>, <code class="keyword">__DATE__</code>, 
  <code class="keyword">__TIME__</code>, <code class="keyword">__STDC__, __INCLUDE_LEVEL__</code> 
  and <code class="keyword">__cplusplus</code> macros) may be undefined from the command line of the driver executable 
  (<a href="wave_driver.html">see</a> the -U option) or through the function <a href="class_reference_context.html#remove_macro_definition">remove_macro_definition()</a>.</p>
<table width="90%" border="0" align="center">
  <tr> 
    <td colspan="3" class="table_title"><b>Summary of predefined macros</b></td>
  </tr>
  <tr class="table_title"> 
    <td width="28%"><b>Name</b></td>
    <td width="62%"><p><code><font face="Verdana, Arial, Helvetica, sans-serif">Value</font></code></p></td>
    <td width="10%">Undefinable</td>
  </tr>
  <tr> 
    <td width="28%" class="table_cells"><code>__STDC__</code></td>
    <td width="62%" class="table_cells"><p> 1 (a decimal constant)</p></td>
    <td width="10%" class="table_cells"><p>yes</p></td>
  </tr>
  <tr> 
    <td width="28%" class="table_cells"><code>__cplusplus</code></td>
    <td width="62%" class="table_cells"> <p>199711L (a decimal constant), this 
        is defined in C++ mode only (C99 mode is off)<br>
        In the C++0x mode this decimal constant is guaranteed to be larger than 
        199711L (the concrete value is to be defined by the C++ committee).</p></td>
    <td width="10%" class="table_cells"><p>no</p></td>
  </tr>
  <tr> 
    <td class="table_cells"><code>__LINE__</code></td>
    <td class="table_cells"><p>The line number of the current source line (a decimal 
        constant)</p></td>
    <td class="table_cells"><p>no</p></td>
  </tr>
  <tr>
    <td class="table_cells"><code>__FILE__</code></td>
    <td class="table_cells"><p>The presumed name of the source file (a character string literal)</p></td>
    <td class="table_cells"><p>no</p></td>
  </tr>
  <tr>
    <td class="table_cells"><code>__BASE_FILE__</code></td>
    <td class="table_cells"><p> This macro expands to the name of the main input file (a character string literal). This is the source file that was specified during construction of the <a href="class_reference_context.html"><tt>wave::context</tt></a> template.</p></td>
    <td class="table_cells"><p>no</p></td>
  </tr>
  <tr> 
    <td class="table_cells"><code>__DATE__</code></td>
    <td class="table_cells"><p>The date of translation of the source file (a character 
        string literal of the form<br>
        &quot;Mmm dd yyyy&quot;, where the names of the months are the same as 
        those generated by the asctime function, and the first character of dd 
        is a space character if the value is less than 10). </p></td>
    <td class="table_cells"><p>no</p></td>
  </tr>
  <tr> 
    <td class="table_cells"><code>__TIME__</code></td>
    <td class="table_cells"><p>The time of translation of the source file (a character 
        string literal of the form &quot;hh:mm:ss&quot;<br>
        as in the time generated by the asctime function).</p></td>
    <td class="table_cells"><p>no</p></td>
  </tr>
  <tr> 
    <td class="table_cells"><code>__INCLUDE_LEVEL__</code></td>
    <td class="table_cells"><p>A decimal integer constant that represents the 
        depth of nesting in include <br>
        files. The value of this macro is incremented on every <tt>#include</tt> 
        directive <br>
        and decremented at every end of file. </p></td>
    <td class="table_cells"><p>no</p></td>
  </tr>
  <tr> 
    <td class="table_cells"><code>__WAVE__</code><br> <code>__SPIRIT_PP__</code></td>
    <td class="table_cells"><p>The version of the driver program (a hexadecinal 
        constant of the form 0xMmrr, where <span class="literal">'M'</span> is 
        the major version number, <span class="literal">'m'</span> the minor version 
        number and <span class="literal">'rr'</span> the release number).</p></td>
    <td class="table_cells"><p>yes</p></td>
  </tr>
  <tr> 
    <td class="table_cells"><code>__WAVE_VERSION__</code><br> <code>__SPIRIT_PP_VERSION__</code></td>
    <td class="table_cells"><p>The full version number of the driver program (a 
        hexadecimal constant of the form 0xMmrrbbbb, where <span class="literal">'M'</span> 
        is the major version number, <span class="literal">'m'</span> the minor 
        version number, <span class="literal">'rr'</span> the release number and 
        <span class="literal">'bbbb'</span> the build number).</p></td>
    <td class="table_cells"><p>yes</p></td>
  </tr>
  <tr> 
    <td class="table_cells"><code>__WAVE_VERSION_STR__</code><br> <code>__SPIRIT_PP_VERSION_STR__</code></td>
    <td class="table_cells"><p>The full version number of the driver program (a 
        character string literal of the form <span class="string">&quot;M.m.rr.bbbb&quot;</span>, 
        where <span class="literal">'M'</span> is the major version number, <span class="literal">'m'</span> 
        the minor version number, <span class="literal">'rr'</span> the release 
        number and <span class="literal">'bbbb'</span> the build number).</p></td>
    <td class="table_cells"><p>yes</p></td>
  </tr>
  <tr>
    <td class="table_cells"><code>__STDC_VERSION__</code></td>
    <td class="table_cells"><p>199901L (a decimal constant), this is defined in C99 and C++0x modes only</p></td>
    <td class="table_cells"><p>yes</p></td>
  </tr>
  <tr>
    <td class="table_cells"><code>__STDC_HOSTED__</code></td>
    <td class="table_cells"><p>0L (a decimal constant), this is defined in C99 and C++0x modes only</p></td>
    <td class="table_cells"><p>yes</p></td>
  </tr>
  <tr> 
    <td class="table_cells"><code>__WAVE_HAS_VARIADICS__</code></td>
    <td class="table_cells"><p>1 (a decimal constant), this is defined in C++ 
        mode only if variadics and placemarkers are enabled, and it is defined 
        in the C99, C++0x, and C++2A modes</p></td>
    <td class="table_cells"><p>no</p></td>
  </tr>
  <tr>
    <td class="table_cells"><code>__WAVE_CONFIG__</code></td>
    <td class="table_cells"><p>The configuration information used during the compilation of the Wave library. This is a hexadecimal constant, where each of the bits represents a certain configuration option enabled/disabled during the compilation. You can query this contant using the following macro constants available in the <tt>wave_config_constant.hpp</tt> file:</p>
      <blockquote>
              <p> <tt>BOOST_WAVE_SUPPORT_VARIADICS_PLACEMARKERS_CONFIG</tt> (value: <span class="literal">0x1</span>) <br>
              Variadics and placemarkers are supported</p>
        <p><tt>BOOST_WAVE_SUPPORT_PRAGMA_ONCE_CONFIG</tt> (value: <span class="literal">0x02</span>)<br>
          <span class="preprocessor">#pragma once</span> (<span class="keyword">_Pragma(once))</span> is supported </p>
          <p><tt>BOOST_WAVE_SUPPORT_MS_EXTENSIONS_CONFIG</tt> (value: <span class="literal">0x04</span>)<br>MS Extensions are supported </p>
          <p><tt>BOOST_WAVE_PREPROCESS_PRAGMA_BODY_CONFIG</tt> (value: <span class="literal">0x08</span>)<br>
          The bodies of <span class="preprocessor">#pragma</span> statements (operator <span class="keyword">_Pragma</span>) will be preprocessed
          <p><tt>BOOST_WAVE_USE_STRICT_LEXER_CONFIG</tt> (value: <span class="literal">0x10</span>)<br>
            The C/C++ lexers recognize the 
      strict C99/C++ basic source character set. If it is not defined or defined 
    to zero, the lexers recognize the <span class="string">'$'</span> character as part of identifiers.</p>
      </blockquote>
      <p><br>
        </p></td>
    <td class="table_cells"><p>yes</p></td>
  </tr>
</table>
<br>
<table border="0">
  <tr> 
    <td width="10"></td>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="class_reference_inptpolcy.html"><img src="theme/l_arr.gif" width="20" height="19" border="0"></a></td>
    <td width="30"><a href="macro_expansion_process.html"><img src="theme/r_arr.gif" border="0"></a></td>
  </tr>
</table>
<hr size="1">
<p class="copyright">Copyright &copy; 2003-2011 Hartmut Kaiser<br>
  <br>
  <font size="2">Distributed under the Boost Software License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt) </font> </p>
<span class="updated"></span>
<p class="copyright"><span class="updated">Last updated: 
  <!-- #BeginDate format:fcAm1m -->Sunday, January 9, 2011  16:12<!-- #EndDate -->
  </span></p>
</body>
</html>
