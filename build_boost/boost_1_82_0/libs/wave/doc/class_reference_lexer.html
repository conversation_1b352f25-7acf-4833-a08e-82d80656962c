<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>The Lexer Interface</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link href="theme/style.css" rel="stylesheet" type="text/css">
</head>

<body>
<table width="100%" border="0" cellspacing="2" background="theme/bkd2.gif">
  <tr> 
    <td width="21"> <h1></h1></td>
    <td width="885"> <font face="Verdana, Arial, Helvetica, sans-serif"><b><font size="6">The 
      Lexer Iterator Interface</font></b></font></td>
    <td width="96"><a href="http://www.boost.org"><img src="theme/wave.gif" width="93" height="68" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr> 
    <td width="10"></td>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="class_reference_ctxpolicy.html"><img src="theme/l_arr.gif" width="20" height="19" border="0"></a></td>
    <td width="30"><a href="class_reference_tokentype.html"><img src="theme/r_arr.gif" border="0"></a></td>
  </tr>
</table>
<blockquote>
  <p><a href="class_reference_lexer.html#introduction">Introduction</a><br>
    <a href="class_reference_lexer.html#header_synopsis">Wave Lexer 
    synopsis</a><br>
    <a href="class_reference_lexer.html#public_typedefs">Public Typedefs</a><br>
    <a href="class_reference_lexer.html#member_functions">Member functions</a></p>
</blockquote>
<h2><b><a name="introduction"></a>Introduction</b></h2>
<p>Every lexer, which should be used in conjunction with the <tt>Wave</tt> library, has to return tokens formed from the input stream. These tokens should conform to the synopsis described in the topic <a href="class_reference_tokentype.html">The Token Type</a>. The lexer type should expose an interface which conforms at least to a <tt>forward_iterator</tt> (in the sense defined by the the C++ Standard) returning the token type. The code sample below does not show the definition of this forward iterator interface because this is highly implementation defined.</p>
<h2><a name="header_synopsis"></a>Wave lexer  
  synopsis (header: <a href="http://svn.boost.org/trac/boost/browser/trunk/boost/wave/cpplexer/cpp_lex_interface.hpp">wave/cpplexer/cpp_lexer_interface.hpp</a>)</h2>
<pre>    <span class="keyword">struct</span> lex_iterator
    {
        <span class="keyword">typedef</span> boost::wave::lex_token&lt;&gt; <a href="class_reference_lexer.html#public_typedefs">token_type</a>;

<span class="comment">    // Every lex_iterator should implement at least a complete 
    // forward_iterator interface (not shown here)
</span><span class="keyword">        typedef</span> std::forward_iterator_tag iterator_category;

<span class="comment">    // additional requirements
</span>        <a href="class_reference_lexer.html#member_functions">lex_iterator</a>();

        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> Iterator&gt;
        <a href="class_reference_lexer.html#constructor">lex_iterator</a>(Iterator <span class="keyword">const</span> &amp;first, Iterator <span class="keyword">const</span> &amp;last
            <span class="keyword">typename</span> token_type::position_type <span class="keyword">const</span> &amp;pos, 
            boost::wave::language_support language)
    };

</pre>
<p>Please note, that the <tt>lex_iterator</tt> defined in the library header <a href="http://svn.boost.org/trac/boost/browser/trunk/boost/wave/cpplexer/cpp_lex_interface.hpp">wave/cpplexer/cpp_lexer_interface.hpp</a> actually is a template class taking the token type to use as its template parameter. This is omitted in the synopsis above because it is an implementation detail of the  Re2C lexer provided as part of the Wave library.</p>
<p>If you want to use Wave in conjunction with your own lexing component this will have to conform to the interface described above only. </p>
<h2><a name="public_typedefs" id="public_typedefs"></a>Public Typedefs</h2>
<p>Besides the typedefs mandated for a <tt>forward_iterator</tt> by the C++ standard every lexer to be used with the <tt>Wave</tt> library should define the following typedefs: </p>
<table width="90%" border="0" align="center">
  <tr>
    <td colspan="2" class="table_title"><b>Public typedefs defined by the <tt>boost::wave::context</tt> class </b></td>
  </tr>
  <tr>
    <td width="32%" class="table_cells"><code>token_type</code></td>
    <td width="68%" class="table_cells"><p>The <tt>token</tt> type returned by the lexer. This is type is used as the return value of the main iterators provided by the <tt>boost::wave::context</tt> object too. </p></td>
  </tr>
</table>
<h2><a name="member_functions"></a>Member functions</h2>
<p>Besides the functions, which should be provided for <tt>forward_iterators</tt> as mandated by the C++ Standard, every lexer must implement the following functions to be used with the <tt>Wave</tt> library: </p>
<p><a name="constructor" id="constructor"></a><b>constructor</b></p>
<pre>    lex_iterator();</pre>
<blockquote>
  <p>The default constructor should construct a lexer iterator, which may be used as the end iterator of the provided iterator range.</p>
</blockquote>
<pre>
    <span class="keyword">template</span> &lt;<span class="keyword">typename</span> Iterator&gt;
    lex_iterator(Iterator <span class="keyword">const</span> &amp;first, Iterator <span class="keyword">const</span> &amp;last,
        <span class="keyword">typename</span> token_type::position_type <span class="keyword">const</span> &amp;pos, 
        boost::wave::language_support language);</pre>
<blockquote>
  <p>The second constructor should construct a lexer iterator, which may be used as a iterator traversing over the token sequence, generated by the lexer class.</p>
  <p>The pair of iterators <tt>first</tt> and <tt>last</tt> should represent the input stream to be tokenized by the given lexer class. </p>
  <p>The parameter <tt>pos</tt> contains the initial position information to be used for token generation.</p>
  <p>The parameter <tt>language</tt> controls the required mode  with which the lexer should be initialised. </p>
</blockquote>
<table border="0">
  <tr> 
    <td width="10"></td>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="class_reference_ctxpolicy.html"><img src="theme/l_arr.gif" width="20" height="19" border="0"></a></td>
    <td width="30"><a href="class_reference_tokentype.html"><img src="theme/r_arr.gif" border="0"></a></td>
  </tr>
</table>
<hr size="1">
<p class="copyright">Copyright &copy; 2003-2011 Hartmut Kaiser<br>
  <br>
<font size="2">Distributed under the Boost Software License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt) </font> </p>
<p class="copyright"><span class="updated">Last updated: 
  <!-- #BeginDate format:fcAm1m -->Sunday, October 12, 2008  20:15<!-- #EndDate -->
  </span>
</p>
</body>
</html>

