# Boost.Wave

Branch   | Appveyor (MSVC) | GitHub Actions (gcc, clang) |
---------|-----------------|-----------------------------|
Master   | [![AppVeyor](https://ci.appveyor.com/api/projects/status/3v74vuhk8dwt2wr9/branch/master?svg=true)](https://ci.appveyor.com/project/jefftrull/wave/branch/master) | [![GitHub Actions](https://github.com/boostorg/wave/actions/workflows/ci.yml/badge.svg?branch=master)](https://github.com/boostorg/wave/actions?query=branch%3Amaster) |
Develop   | [![AppVeyor](https://ci.appveyor.com/api/projects/status/3v74vuhk8dwt2wr9/branch/develop?svg=true)](https://ci.appveyor.com/project/jefftrull/wave/branch/develop) | [![GitHub Actions](https://github.com/boostorg/wave/actions/workflows/ci.yml/badge.svg?branch=develop)](https://github.com/boostorg/wave/actions?query=branch%3Adevelop) |

The Wave C++ preprocessor library is a Standards conformant implementation of the mandated C99/C++ preprocessor functionality packed behind a simple to use interface, which integrates well with the well known idioms of the Standard Template Library (STL).
