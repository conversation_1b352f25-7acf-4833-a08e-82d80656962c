[/
 / Copyright (c) 2021-2023 <PERSON><PERSON>
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:changelog Changelog]

[heading Boost 1.82]

* Due to changes in Boost libraries used in Boost.Atomic, support for C++03 is deprecated. C++11 will become the minimum starting with Boost.Atomic 1.84.
* When compiled for x86 targets supporting AVX, vector instructions are now used for 128-bit atomic loads and stores.
* For 32-bit x86 targets, 64-bit atomic loads and stores with `memory_order_seq_cst` semantics will now issue a memory ordering instruction.
* Removed `atomic<T>::storage()` accessors and `atomic<T>::storage_type` types that were deprecated in Boost.Atomic 1.73. Users are recommended to use `atomic<T>::value()` and `atomic<T>::value_type` instead.

[heading Boost 1.80]

* Improved portability of endianness checks on ARM, AArch32 and AArch64 targets. ([github_issue 59])
* Fixed compilation with MSVC 14.0 (Visual Studio 2015) in C++17 mode. ([github_issue 61])

[heading Boost 1.79]

* Fixed compilation for Universal Windows Platform (UWP). ([github_issue 54])
* Added `BOOST_ATOMIC_NO_DARWIN_ULOCK` configuration macro. The macto affects compilation on Darwin systems and disables `ulock`-based implementation of waiting and notifying operations. This may be useful to comply with Apple App Store requirements. ([github_issue 55])

[heading Boost 1.78]

* Use process-local futex operations on Android for non-IPC waiting and notifying operations.
* Added support for Linux targets that only define `SYS_futex_time64` syscall, such as riscv32.
* Added a workaround for incorrect result of `std::alignment_of` on clang 8 for 64-bit types on 32-bit x86 targets.
* Added a ulock backend for waiting and notifying operations on Darwin systems since Mac OS 10.12, iOS 10.0, tvOS 10.0 or watchOS 3.0. The backend supports native 32-bit process-local waiting and notifying operations, and since Mac OS 10.15, iOS 13.0, tvOS 13.0 or watchOS 6.0 - also 64-bit process-local operations and 32 and 64-bit inter-process operations.
* On Windows, corrected discrepancy between [^['atomic-type]::always_has_native_wait_notify] and the corresponding capability macros when targeting Windows 8 or later. The library will now directly use `WaitOnAddress` and related APIs from public headers and therefore require user to link with `synchronization.lib` if the user requires Windows 8 or later by defining `BOOST_USE_WINAPI_VERSION`, `_WIN32_WINNT` or similar macros. The library is linked automatically on compilers that support auto-linking (e.g. MSVC).
* Added support for types with padding bits, except unions, on compilers that provide a way to clear the padding bits. This feature is supported by gcc 11 and MSVC 14.2 (compiler version 19.27) and newer, as well as other compilers supporting similar intrinsics. On compilers that don't allow to clear the padding bits, types with padding are still generally not supported with the exception of 80-bit `long double` on x86 targets. A new `BOOST_ATOMIC_NO_CLEAR_PADDING` capability macro is defined to indicate when clearing the padding is not supported.
* Initializing constructors of `atomic_ref` and `ipc_atomic_ref` no longer use atomic instructions to clear the padding bits in the referenced object. This reduces the cost of the atomic reference construction. This is considered safe because clearing the padding does not issue writes to the bytes that contribute to the object value. However, some thread safety checking software may falsely detect this as a data race.
* Initializing constructors of `atomic` and `ipc_atomic` are now `constexpr` for enums, classes and floating point types. For classes and floating point types, the constructors are `constexpr` if the compiler supports `constexpr` `std::bit_cast`, the type has no padding bytes and no padding is required to implement native atomic operations (i.e., for [^atomic<['T]>], the object of type [^['T]] fits exactly in the internal storage of the atomic).
* In accordance with C++20, default constructors of `atomic` and `ipc_atomic` now perform value initialization of the contained object. For types without a user-defined default constructor, this means the default-constructed atomic will be zero-initialized.
* Added a workaround for compilation error on AIX caused by the assembler tool not supporting numeric labels. ([github_pr 50])
* Added a workaround for compilation error with Visual Studio 2015 prior to Update 3. ([github_issue 52])

[heading Boost 1.77]

* Added `make_atomic_ref` and `make_ipc_atomic_ref` factory functions for constructing atomic reference objects.
* Added C++17 template argument deduction guides for `atomic_ref` and `ipc_atomic_ref` to allow omitting template arguments when they can be deduced from constructor arguments.

[heading Boost 1.76]

* Fixed compilation with MSVC for ARM. ([github_pr 49])

[heading Boost 1.75]

* Implemented SSE2 and SSE4.1 versions of address lookup algorithm, which is used in the internal lock pool implementation. This may improve performance of waiting and notifying operations in heavily contended cases.
* Fixed a possible compilation error on AArch64 targets caused by incorrect instructions generated for bitwise (logical) operations with immediate constants. ([github_issue 41])

[heading Boost 1.74]

* Added missing `const` qualifiers to some operations in `atomic_ref`.
* Added support for `yield` instruction on ARMv8-A. The instruction is used internally in spin loops to reduce CPU power consumption.
* Added support for C++20 [link atomic.interface.interface_wait_notify_ops waiting and notifying operations]. The implementation includes generic backend that involves the internal lock pool, as well as specialized backends for Windows, Linux, FreeBSD, DragonFly BSD and NetBSD. Atomic types provide a new method `has_native_wait_notify`, a static boolean constant `always_has_native_wait_notify` and a set of capability macros that allow to detect if the implementation supports native waiting and notifying operations for a given type.
* Changed internal representation of `atomic_flag` to use 32-bit storage. This allows for more efficient waiting and notifying operations on `atomic_flag` on some platforms.
* Added support for build-time configuration of the internal lock pool size. The user can define the `BOOST_ATOMIC_LOCK_POOL_SIZE_LOG2` macro to specify binary logarithm of the size of the lock pool. The default value is 8, meaning that the size of the lock pool is 256, up from 64 used in the previous release.
* Added support for a new set of atomic types dedicated for [link atomic.interface.interface_ipc inter-process communication]: `ipc_atomic_flag`, `ipc_atomic` and `ipc_atomic_ref`. Users are recommended to port their code using non-IPC types for inter-process communication to the new types. The new types provide the same set of operations as their non-IPC counterparts, with the following differences:
  * Most operations have an added precondition that `is_lock_free` returns `true` for the given atomic object. The library will issue a compile time error if this precondition is known to be not satisfied at compile time.
  * All provided operations are address-free, meaning that the atomic object (in case of `ipc_atomic_ref` - the referenced object) may be located in process-shared memory or mapped into the same process at multiple different addresses.
  * The new `has_native_wait_notify` operation and `always_has_native_wait_notify` constant indicate support for native inter-process waiting and notifying operations. When that support is not present, the operations are implemented with a busy loop, which is less efficient, but still is address-free. A separate set of capability macros is also provided to indicate this support.
* Added new `atomic_unsigned_lock_free` and `atomic_signed_lock_free` types introduced in C++20. The types indicate the atomic object type for an unsigned or signed integer, respectively, that is lock-free and preferably has native support for waiting and notifying operations.
* Added new gcc assembler backends for ARMv8-A (for both AArch32 and AArch64). The new backends are used to implement operations not supported by compiler intrinsics (including 128-bit operations on AArch64) and can also be used when compiler intrinsics are not available. Both little and big endian targets are supported. AArch64 backend supports extensions defined in ARMv8.1 and ARMv8.3.
* Added support for big endian targets in the legacy ARM backend based on gcc assembler blocks (this backend is used on ARMv7 and older targets). Previously, the backend assumed little endian memory layout, which is significant for 64-bit operations.
* Improved performance of seq_cst stores and thread fences on x86 by using `lock`-prefixed instructions instead of `mfence`. This means that the operations no longer affect non-temporal stores, which was also not guaranteed before. Use specialized instructions and intrinsics to order non-temporal memory accesses.
* Fixed capability macros for 80-bit `long double` on x86 targets not indicating lock-free operations even if 128-bit atomic operations were available.
* Fixed compilation of gcc asm blocks on Alpha targets.
* In the gcc `__sync*` intrinsics backend, fixed that store and load operations of large objects (larger than a pointer size) could be non-atomic. The implementation currently assumes that small objects can be stored with a single instruction atomically on all modern architectures.

[heading Boost 1.73]

* Implemented C++20 `atomic_ref`. See [link atomic.interface.interface_atomic_ref docs] and especially the [link atomic.interface.interface_atomic_ref.caveats caveats] section.
* Implemented `atomic_flag::test` operation, which was introduced in C++20.
* `atomic<T>` should now take into account alignment requirements of `T`, which makes a difference if those requirements are higher than that of the internal storage of `atomic`.
* Added static asserts enforcing the requirements on the value type `T` used with `atomic` and `atomic_ref`. This should prohibit invalid types from being used as atomics.
* Improved internal lock pool implementation. The pool is larger, and lock selection accounts for atomic object alignment, which should reduce the potential of thread contention.
* Fixed incorrect x86 code generated for `bit_test_and_*` operations on 8 and 16-bit arguments. Other architectures are not affected.
* Fixed a possible unaligned memory access in `compare_exchange_*` operations, if alignment requirements of `value_type` are less than that of the internal storage of `atomic`.
* `boost/atomic/atomic.hpp` no longer includes `boost/atomic/atomic_flag.hpp` and `boost/atomic/fences.hpp` and only defines the `boost::atomic` class template and related typedefs. Include the other headers explicitly or use `boost/atomic.hpp` to include all parts of Boost.Atomic.
* The `atomic<T>::storage()` accessor and associated `atomic<T>::storage_type` type are deprecated. Instead, users are advised to use `atomic<T>::value()` and `atomic<T>::value_type`, respectively. Users can define `BOOST_ATOMIC_SILENCE_STORAGE_DEPRECATION` to disable deprecation warnings for the time of transition. The deprecated pieces will be removed in a future release.
* Removed support for `BOOST_ATOMIC_DETAIL_HIGHLIGHT_OP_AND_TEST`. This macro was used as a helper for transition to the updated returned values of `*_and_test` operations in Boost.Atomic 1.67, which was released 2 years before 1.73.

[heading Boost 1.72]

* Added a workaround for `__float128` not being considered as a floating point type by some versions of libstdc++.
* Improved compatibility with clang-win compiler.

[heading Boost 1.67]

* [*Breaking change:] Changed the result of the `(op)_and_test` operations added in Boost 1.66 to the opposite - the functions now return `true` if the operation result is non-zero. This is consistent with other `test` methods in Boost.Atomic and the C++ standard library. Users can define `BOOST_ATOMIC_DETAIL_HIGHLIGHT_OP_AND_TEST` when compiling their code to emit warnings on every use of the changed functions. This way users can locate the code that needs to be updated. ([github_issue 11])
* Update for C++2a. On C++11 compilers that support scoped enums, the `memory_order` enumeration is now scoped and contains constants with shorter names like `acquire`, `release` or `seq_cst` (i.e. users can use `memory_order::acquire` instead of `memory_order_acquire`). The old constants are also provided for backward compatibility. ([@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2016/p0439r0.html P0439R0])
* Update for C++2a. Added experimental support for atomic operations on floating point types. In addition to general operations, `add`, `sub`, `negate` operations and their `fetch_(op)` and `opaque_(op)` versions are supported. Lock-free property can be tested with the new macros `BOOST_ATOMIC_FLOAT/DOUBLE/LONG_DOUBLE_LOCK_FREE`. The support for floating point types is optional and can be disabled by defining `BOOST_ATOMIC_NO_FLOATING_POINT`. ([@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2017/p0020r6.html P0020R6])
* Added new experimental operations:
  * `negate_and_test` and `complement_and_test` which perform negation or bitwise complement and return `true` if the result is not zero.
  * `add`, `sub`, `negate`, `bitwise_and`, `bitwise_or`, `bitwise_xor`, `bitwise_complement` operations which perform the operation and return its result.
* For generic `atomic<T>` specialization, the default constructor is now trivial if `T`'s default constructor is.
* The internal implementation of `atomic<T>` has been updated to avoid undefined behavior that stems from signed integer overflows. As required by the C++ standard, the library uses two's complement representation of signed integers internally and accroding rules of overflow. Currently, the library requires the native signed integer types to also use two's complement representation (but no defined overflow semantics).
* Improved Clang support. In particular, fixed DCAS not being lock-free and fixed possible incorrect code generated on 32-bit x86.
* Improved MinGW support. For gcc versions up to 4.6, fixed compilation of DCAS on x86.
* In x86 PIE code, asm blocks now preserve `ebx` value.

[heading Boost 1.66]

* Implemented a set of experimental extended atomic operations for integral types:
  * `fetch_negate`, `fetch_complement` - atomically replaces the value with a negated or binary complemented value and returns the original value
  * `opaque_<op>` - equivalent to `fetch_<op>` except that it doesn't return the original value
  * `<op>_and_test` - atomically applies `<op>` and returns `true` if the result is zero. *Note:* The result of these operations will change to the opposite in Boost 1.67. The code that uses these functions will need to be updated.
  * `bit_test_and_set`, `bit_test_and_reset`, `bit_test_and_complement` - atomically sets, resets or complements the specified bit and returns the original value of the bit
* Following C++17 ([@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2017/p0558r1.pdf P0558R1]), arithmetic operations for pointers to non-object types are no longer provided.
* Also following C++17 exposed `atomic<T>::value_type` and `atomic<T>::difference_type` member typedefs, where applicable, to the user's code.
* Improved compatibility with gcc 7. In particular, using 128-bit operations on x86-64 should no longer require linking with libatomic (the compiler-supplied library).

[heading Boost 1.64]

* Fixed possible incorrect code generation in 64-bit atomic operations on 32-bit x86 with gcc versions older than 4.7 and compatible compilers.

[heading Boost 1.63]

* Added the static constant `atomic<T>::is_always_lock_free` for conformance with C++17. The constant indicates that the given specialization always provides lock-free implementation of atomic operations.
* Improved support of Oracle Studio on x86 targets. 

[*Post-release notes:]

* Using 64-bit atomic operations on 32-bit x86 with gcc versions older than 4.7 and compatible compilers can result in generation of incorrect code. This problem is fixed in [@https://github.com/boostorg/atomic/commit/a67cc1b055cf09f371e2eca544884634a1ccc886 this] commit.

[heading Boost 1.62]

* Improved support for Oracle Studio and SPARC. The library now provides native atomic operations on SPARCv8+.

[heading Boost 1.60]

* Enforced proper alignment of `atomic<>` storage. This should fix possible issues on platforms that support atomic operations on data units larger than the native word size. This may also change binary layout of user's data structures that have `atomic<>` members.
* Fixed compilation for PowerPC with IBM XL C++ compiler. Corrected memory barriers in PowerPC assembler.
* Fixed compilation with MSVC-8 for ARM.
* Fixed compilation with gcc 4.4 for x86-64, when 128-bit atomic operations were used. ([ticket 10994])
* Optimized some gcc assembler blocks for x86/x86-64 to reduce the number of used registers. This may require binutils 2.10 or later.

[heading Boost 1.56]

* The library has been redesigned. Besides internal refactoring, various bugs were fixed, including incorrect values of feature test macros and integer overflow handling.
* Changed values of the `memory_order` enumeration. The concrete values are not part of the interface, but this change may potentially break ABI, if the enum is used in user's interfaces.
* Implemented support for 128-bit atomic operations on Windows x64 with MSVC. The library assumes presence of the `cmpxchg16b` instruction in the target CPUs. Some early AMD CPUs don't support this instruction. To target those define the `BOOST_ATOMIC_NO_CMPXCHG16B` macro.
* Implemented experimental support for Windows ARM target with MSVC.
* Implemented experimental support for DEC Alpha target with GCC.
* Improved support for ARMv6 and later with GCC. Implemented all atomic operations as assembler blocks instead of CAS-based loops. 64-bit operations are supported with ARMv7.
* Implemented optional support for the `BOOST_ATOMIC_FLAG_INIT` macro and static initialization of `atomic_flag`. ([ticket 8158])
* Fixed compilation for SPARCv9 target. ([ticket 9446])
* Fixed compilation for PowerPC target. ([ticket 9447])
* Fixed several compatibility problems with Clang on x86 and x86-64. ([ticket 9610], [ticket 9842])
* Removed specialized code for Windows on IA64 platform.

[heading Boost 1.55]

* Added support for 64-bit atomic operations on x86 target for GCC, MSVC and compatible compilers. The support is enabled when it is known at compile time that the target CPU supports required instructions.
* Added support for 128-bit atomic operations on x86-64 target for GCC and compatible compilers. The support is enabled when it is known at compile time that the target CPU supports required instructions. The support can be tested for with the new `BOOST_ATOMIC_INT128_LOCK_FREE` macro.
* Added a more efficient implementation of `atomic<>` based on GCC `__atomic*` intrinsics available since GCC 4.7.
* Added support for more ARM v7 CPUs, improved detection of Thumb 2.
* Added support for x32 (i.e. 64-bit x86 with 32-bit pointers) target on GCC and compatible compilers.
* Removed dependency on Boost.Thread.
* Internal lock pool now includes proper padding and alignment to avoid false sharing.
* Fixed compilation with Intel compiler on Windows. Removed internal macro duplication when compiled on Windows.
* Some code refactoring to use C++11 features when available.

[heading Boost 1.53]

* Initial Boost release with [*Boost.Atomic].

[endsect]
