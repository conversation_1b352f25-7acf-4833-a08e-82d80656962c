<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Spirit Repository 0.2</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="index.html" title="Spirit Repository 0.2">
<link rel="next" href="spirit_repository/preface.html" title="Preface">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav"><a accesskey="n" href="spirit_repository/preface.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a></div>
<div class="article">
<div class="titlepage">
<div>
<div><h2 class="title">
<a name="spirit_repository"></a>Spirit Repository 0.2</h2></div>
<div><div class="authorgroup">
<div class="author"><h3 class="author">
<span class="firstname">Joel</span> <span class="surname">de Guzman</span>
</h3></div>
<div class="author"><h3 class="author">
<span class="firstname">Hartmut</span> <span class="surname">Kaiser</span>
</h3></div>
</div></div>
<div><p class="copyright">Copyright © 2001-2011 Joel de Guzman, Hartmut Kaiser</p></div>
<div><div class="legalnotice">
<a name="spirit_repository.legal"></a><p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></div>
</div>
<hr>
</div>
<div class="toc">
<p><b>Table of Contents</b></p>
<dl class="toc">
<dt><span class="section"><a href="spirit_repository/preface.html">Preface</a></span></dt>
<dt><span class="section"><a href="spirit_repository/what_s_new.html">What's New</a></span></dt>
<dd><dl><dt><span class="section"><a href="spirit_repository/what_s_new/spirit_2_5_1.html">Spirit V2.5.1</a></span></dt></dl></dd>
<dt><span class="section"><a href="spirit_repository/qi_components.html">Qi Components</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="spirit_repository/qi_components/primitive.html">Qi Parser
      Primitives</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="spirit_repository/qi_components/primitive/advance.html">Qi
        advance Parser</a></span></dt>
<dt><span class="section"><a href="spirit_repository/qi_components/primitive/flush_multi_pass.html">Qi
        flush_multi_pass parser</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="spirit_repository/qi_components/directives.html">Qi Parser
      Directives</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="spirit_repository/qi_components/directives/confix.html">Qi
        Confix Parser Directive</a></span></dt>
<dt><span class="section"><a href="spirit_repository/qi_components/directives/distinct.html">Qi
        Distinct Parser Directive</a></span></dt>
<dt><span class="section"><a href="spirit_repository/qi_components/directives/kwd.html">Qi
        Keyword Parser Directive </a></span></dt>
<dt><span class="section"><a href="spirit_repository/qi_components/directives/seek.html">Qi
        Seek Parser Directive </a></span></dt>
</dl></dd>
<dt><span class="section"><a href="spirit_repository/qi_components/nonterminal.html">Qi Parser
      Non-terminals</a></span></dt>
<dd><dl><dt><span class="section"><a href="spirit_repository/qi_components/nonterminal/subrule.html">Qi
        subrules</a></span></dt></dl></dd>
<dt><span class="section"><a href="spirit_repository/qi_components/operators.html">Qi Parser
      Operators</a></span></dt>
<dd><dl><dt><span class="section"><a href="spirit_repository/qi_components/operators/keyword_list.html">Keyword
        List Operator</a></span></dt></dl></dd>
</dl></dd>
<dt><span class="section"><a href="spirit_repository/karma_components.html">Karma Components</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="spirit_repository/karma_components/directives.html">Karma
      Generator Directives</a></span></dt>
<dd><dl><dt><span class="section"><a href="spirit_repository/karma_components/directives/karma_confix_generator.html">Karma
        Confix Generator</a></span></dt></dl></dd>
<dt><span class="section"><a href="spirit_repository/karma_components/nonterminal.html">Karma
      Generator Non-terminals</a></span></dt>
<dd><dl><dt><span class="section"><a href="spirit_repository/karma_components/nonterminal/subrule.html">Karma
        subrules</a></span></dt></dl></dd>
</dl></dd>
<dt><span class="section"><a href="spirit_repository/acknowledgments.html">Acknowledgments</a></span></dt>
</dl>
</div>
</div>
<div class="copyright-footer"></div>
<hr>
<div class="spirit-nav"><a accesskey="n" href="spirit_repository/preface.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a></div>
</body>
</html>
