<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Acknowledgments</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Spirit Repository 0.2">
<link rel="up" href="../index.html" title="Spirit Repository 0.2">
<link rel="prev" href="karma_components/nonterminal/subrule.html" title="Karma subrules">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="karma_components/nonterminal/subrule.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="spirit_repository.acknowledgments"></a><a class="link" href="acknowledgments.html" title="Acknowledgments">Acknowledgments</a>
</h2></div></div></div>
<p>
      The <a href="http://boost-spirit.com" target="_top">Spirit</a> repository is the result
      of the contributions of active members of the Spirit community. We would like
      to express our thanks to all who directly contributed and to everybody directly
      or indirectly involved in the discussions, which led to the creation of the
      parser and generator components.
    </p>
<p>
      The following people have directly contributed code to this repository:
    </p>
<p>
      <span class="bold"><strong>Aaron Graham</strong></span> wrote the <a class="link" href="qi_components/primitive/advance.html" title="Qi advance Parser"><code class="computeroutput"><span class="identifier">advance</span></code></a> parser component, which allows
      the parser to skip (advance) through a specified number of iterations without
      performing unnecessary work.
    </p>
<p>
      <span class="bold"><strong>Chris Hoeppler</strong></span> submitted the <a class="link" href="qi_components/directives/confix.html" title="Qi Confix Parser Directive"><code class="computeroutput"><span class="identifier">confix</span></code></a> parser directive allowing to
      embed a parser (the subject) inside an opening (the prefix) and a closing sequence
      (the suffix).
    </p>
<p>
      <span class="bold"><strong>Francois Barel</strong></span> contributed the <a class="link" href="qi_components/nonterminal/subrule.html" title="Qi subrules"><code class="computeroutput"><span class="identifier">subrule</span></code></a> parser and <a class="link" href="karma_components/nonterminal/subrule.html" title="Karma subrules"><code class="computeroutput"><span class="identifier">subrule</span></code></a> generator components, allowing
      to create a named parser or generator, and to refer to it by name. These components
      are in fact fully static versions of the corresponding <code class="computeroutput"><span class="identifier">rule</span></code>
      component.
    </p>
<p>
      <span class="bold"><strong>Thomas Bernard</strong></span> contributed the <a class="link" href="qi_components/operators/keyword_list.html" title="Keyword List Operator">keyword_list</a>
      and <a class="link" href="qi_components/directives/kwd.html" title="Qi Keyword Parser Directive"><code class="computeroutput"><span class="identifier">kwd</span><span class="special">()[]</span></code></a>
      parser components, allowing to define keyword parsers.
    </p>
</div>
<div class="copyright-footer">Copyright © 2001-2011 Joel de Guzman, Hartmut Kaiser<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="karma_components/nonterminal/subrule.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
</body>
</html>
