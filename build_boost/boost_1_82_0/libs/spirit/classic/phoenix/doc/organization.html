<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Organization</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="polymorphic_functions.html">
<link rel="next" href="actors.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Organization</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="polymorphic_functions.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="actors.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
The framework is organized in five (5) layers.</p>
<code><pre>
                 <span class=special>+-----------+
                 |  </span><span class=identifier>binders  </span><span class=special>|
                 +-----------+-----------+------------+
                 | </span><span class=identifier>functions </span><span class=special>| </span><span class=identifier>operators </span><span class=special>| </span><span class=identifier>statements </span><span class=special>|
    +------------+-----------+-----------+------------+
    | </span><span class=identifier>primitives </span><span class=special>|             </span><span class=identifier>composite              </span><span class=special>|
    +------------+------------------------------------+
    |                     </span><span class=identifier>actor                       </span><span class=special>|
    +-------------------------------------------------+
    |                     </span><span class=identifier>tuples                      </span><span class=special>|
    +-------------------------------------------------+
</span></pre></code>
<p>
The lowest level is the tuples library. Knowledge of tuples is not at all required in order to use the framework. In a nutshell, this small sub-library provides a mechanism for bundling heterogeneous types together. This is an implementation detail. Yet, in itself, it is quite useful in other applications as well. A more detailed explanation will be given later.</p>
<p>
Actors are the main concept behind the framework. Lazy functions are abstracted as actors which are actually polymorphic functors. There are only 2 kinds of actors:</p>
<ol><li>primitives</li><li>composites.</li></ol><p>
Composites are composed of zero or more actors. Each actor in a composite can again be another composite. Primitives are atomic units and are not decomposable.</p>
<p>
(lazy) functions, (lazy) operators and (lazy) statements are built on top of composites. To put it more accurately, a lazy function (lazy operators and statements are just specialized forms of lazy functions) has two stages:</p>
<ol><li>(lazy) partial evaluation </li><li>final evaluation </li></ol><p>
The first stage is handled by a set of generator functions, generator functors and generator operator overloads. These are your front ends (in the client's perspective). These generators create the actors that can be passed on just like any other function pointer or functor object. The second stage, the actual function call, can be invoked or executed anytime just like any other function. These are the back-ends (often, the final invocation is never actually seen by the client).</p>
<p>
Binders, built on top of functions, create lazy functions from simple monomorphic (STL like) functors, function pointers, member function pointers or member variable pointers for deferred evaluation (variables are accessed through a function call that returns a reference to the data. These binders are built on top of (lazy) functions.</p>
<p>
The framework's architecture is completely orthogonal. The relationship between the layers is totally acyclic. Lower layers do not depend nor know the existence of higher layers. Modules in a layer do not depend on other modules in the same layer. This means for example that the client can completely discard binders if she does not need it; or perhaps take out lazy-operators and lazy-statements and just use lazy-functions, which is desireable in a pure FP application.</p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="polymorphic_functions.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="actors.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
