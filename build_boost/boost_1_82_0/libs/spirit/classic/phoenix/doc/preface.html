<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Preface</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="next" href="introduction.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Preface</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><img src="theme/l_arr_disabled.gif" border="0"></td>
    <td width="20"><a href="introduction.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<blockquote><p><i>Functional programming is so called because a program consists entirely of functions. The main program itself is written as a function which receives the program's input as its argument and delivers the program's output as its result. Typically the main function is defined in terms of other functions, which in turn are defined in terms of still more functions until at the bottom level the functions are language primitives.</i></p></blockquote><blockquote><p><b><i>John Hughes</i></b>-- <i>Why Functional Programming Matters</i></p></blockquote><a name="influences_and_related_work"></a><h2>Influences and Related Work</h2><p>
The design and implementation of Phoenix is highly influenced by <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> by Yannis Smaragdakis and Brian McNamara and the (<a href="http://www.boost.org">
Boost</a> Lambda Library) <a href="http://www.boost.org/libs/lambda/doc/index.html">
BLL</a> by Jaakko J�rvi and Gary Powell. Phoenix is a blend of <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> and <a href="http://www.boost.org/libs/lambda/doc/index.html">
BLL</a> using the implementation techniques used in the <a href="http://spirit.sourceforge.net">
Spirit</a> inline parser.</p>
<p>
Is Phoenix better than <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> or <a href="http://www.boost.org/libs/lambda/doc/index.html">
BLL</a>? Well, there are concepts found in Phoenix that are not found in either library. <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> has rank-2 polymorphic functions (<a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> jargon) which Phoenix also has, <a href="http://www.boost.org/libs/lambda/doc/index.html">
BLL</a> has syntactic sugared operators which <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> lack, that Phoenix also has.</p>
<p>
Phoenix inherits <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a>'s rank-2 polymorphic functions. Rank-2 polymorphic functions are higher order functions that can accept polymorphic arguments. <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> is the first library to enable higher order polymorphic functions. Before <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a>, polymorphic functions couldn't be used as arguments to other functions.</p>
<p>
What really motivated the author to write Phoenix is the lack of access to a true stack-frame with local variables (closures) in all C++ FP libraries in existence so far. When emulating functions in the form of functors, the most basic ingredient is missing: local variables and a stack. Current FP libraries emulate closures using state variables in functors. In more evolved FP applications, this &quot;poor man's closure&quot; is simply inadequate.</p>
<p>
Perhaps <a href="http://www.boost.org/libs/lambda/doc/index.html">
BLL</a> does not need this at all since unnamed lambda functions cannot call itself anyway; at least not directly. <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> arguably does not need this since it is purely functional without side-effects, thus there is no need to destructively write to a variable. The highly recursive nature of the <a href="http://spirit.sourceforge.net">
Spirit</a> framework from which Phoenix is a derivative work necessitated true reentrant closures. Later on, Phoenix will inherit the <a href="http://spirit.sourceforge.net">
Spirit</a> framework's true closures which implement access to true hardware stack based local variables.</p>
<p>
Phoenix is also extremely modular by design. One can extract and use only a small subset of the full framework, literally tearing the framework into small pieces, without fear that the pieces won't work anymore. For instance, one can use only the <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> style programming layer with rank-2 polymorphic functions without the sugared operators.</p>
<p>
Emphasis is given to make Phoenix much more portable to current generation C++ compilers such as Borland and MSVC. Borland for example chokes on both <a href="http://www.boost.org/libs/lambda/doc/index.html">
BLL</a> and <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> code. Forget MSVC support in <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> and <a href="http://www.boost.org/libs/lambda/doc/index.html">
BLL</a>. On the other hand, although Phoenix is not yet ported to MSVC, Phoenix uses the same tried and true implementation techniques used by the <a href="http://spirit.sourceforge.net">
Spirit</a> framework. Since <a href="http://spirit.sourceforge.net">
Spirit</a> has been ported to MSVC by Bruce Florman (v1.1) and by Raghav Satish (v1.3), it is very likely that Phoenix will also be ported to MSVC.</p>
<p>
Finally, and most importantly though, Phoenix is intended, hopefully, to be much more easier to use. The focus of Phoenix (and <a href="http://spirit.sourceforge.net">
Spirit</a> for that matter), is the typical practicing programmer in the field rather than the gurus and high priests. Think of Phoenix as the C++ FP library for the rest of us <img src="theme/smiley.gif"></img></p>
<a name="how_to_use_this_manual"></a><h2>How to use this manual</h2><p>
The Phoenix framework is organized in logical modules. This documentation provides a user's guide and reference for each module in the framework. A simple and clear code example is worth a hundred lines of documentation; therefore, the user's guide is presented with abundant examples annotated and explained in step-wise manner. The user's guide is based on examples. Lots of them.</p>
<p>
As much as possible, forward information (i.e. citing a specific piece of information that has not yet been discussed) is avoided in the user's manual portion of each module. In many cases, though, it is unavoidable that advanced but related topics not be interspersed with the normal flow of discussion. To alleviate this problem, topics categorized as &quot;advanced&quot; may be skipped at first reading.</p>
<p>
Some icons are used to mark certain topics indicative of their relevance. These icons precede some text to indicate:</p>
<table width="90%" border="0" align="center">  <tr>
  <td class="table_title" colspan="12">
Icons  </td>
  </tr>
<tr><tr><td class="table_cells"><img src="theme/note.gif"></img></td><td class="table_cells"><b>Note</b></td><td class="table_cells">Information provided is moderately important and should be noted by the reader.</td></tr><td class="table_cells"><img src="theme/alert.gif"></img></td><td class="table_cells"><b>Alert</b></td><td class="table_cells">Information provided is of utmost importance.</td></tr><td class="table_cells"><img src="theme/lens.gif"></img></td><td class="table_cells"><b>Detail</b></td><td class="table_cells">Information provided is auxiliary but will give the reader a deeper insight into a specific topic. May be skipped.</td></tr></tr><td class="table_cells"><img src="theme/bulb.gif"></img></td><td class="table_cells"><b>Tip</b></td><td class="table_cells"> A potentially useful and helpful piece of information.</td></tr></table>
<p>
This documentation is automatically generated by <a href="http://spirit.sourceforge.net">
Spirit</a> QuickDoc documentation tool. QuickDoc is part of the <a href="http://spirit.sourceforge.net">
Spirit</a> distribution .</p>
<a name="support"></a><h2>Support</h2><p>
Please direct all questions to <a href="http://spirit.sourceforge.net">
Spirit</a>'s mailing list. You can subscribe to the <a href="https://lists.sourceforge.net/lists/listinfo/spirit-general">
Spirit Mailing List</a>. The mailing list has a searchable archive. A search link to this archive is provided in <a href="http://spirit.sourceforge.net">
Spirit</a>'s home page. You may also read and post messages to the mailing list through an <a href="news://news.gmane.org/gmane.comp.spirit.general">
NNTP news portal</a> (thanks to www.gmane.org). The news group mirrors the mailing list. Here are two links to the archives: via <a href="http://dir.gmane.org/gmane.comp.parsers.spirit.general">
gmane</a>, via <a href="http://sourceforge.net/mailarchive/forum.php?forum_id=1595">
geocrawler</a>.</p>
<p>
<b><i>To my dear daughter Phoenix</i></b><br><b>Joel de Guzman</b><br>September 2002<br></p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><img src="theme/l_arr_disabled.gif" border="0"></td>
    <td width="20"><a href="introduction.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
