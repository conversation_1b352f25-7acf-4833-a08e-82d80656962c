<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>References</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="wrap_up.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>References</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="wrap_up.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><img src="theme/r_arr_disabled.gif" border="0"></td>
   </tr>
</table>
<p>
Why Functional Programming Matters, John Hughes, 1989.<br> Available online at <a href="https://doi.org/10.1093/comjnl/32.2.98">
https://doi.org/10.1093/comjnl/32.2.98</a>.</p>
<p>
<a href="http://www.boost.org">
Boost</a>.Lambda library, Jaakko Jarvi, 1999-2002 Jaakko J�rvi, Gary Powell.<br> Available online at <a href="http://www.boost.org/libs/lambda/">
http://www.boost.org/libs/lambda/</a>.</p>
<p>
Functional Programming in C++ using the <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> Library: a short article introducing <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a>, Brian McNamara and Yannis Smaragdakis, April 2001. Available online at <a href="https://people.cs.umass.edu/~yannis/fc++/">
https://people.cs.umass.edu/~yannis/fc++/</a>.</p>
<p>
Side-effects and partial function application in C++, Jaakko Jarvi and Gary Powell, 2001.<br> Available online at <a href="https://parasol.tamu.edu/~jarvi/papers/mpool01.pdf">
https://parasol.tamu.edu/~jarvi/papers/mpool01.pdf</a>.</p>
<p>
<a href="http://spirit.sourceforge.net">
Spirit</a> Version 1.2, Joel de Guzman, Nov 2001.<br> Available online at <a href="http://spirit.sourceforge.net/index.php?doc=docs/v1_2/index.html">
http://spirit.sourceforge.net/index.php?doc=docs/v1_2/index.html</a>.</p>
<p>
Generic Programming Redesign of Patterns, Proceedings of the 5th European Conference on Pattern Languages of Programs, (EuroPLoP'2000) Irsee, Germany, July 2000.<br> Available online at <a href="http://www.coldewey.com/europlop2000/papers/geraud%2Bduret.zip">
http://www.coldewey.com/europlop2000/papers/geraud%2Bduret.zip</a>.</p>
<p>
A Gentle Introduction to <a href="http://www.haskell.org">
Haskell</a>, Paul Hudak, John Peterson and Joseph Fasel, 1999.<br> Available online at <a href="http://www.haskell.org/tutorial/">
http://www.haskell.org/tutorial/</a></p>
<p>
Large scale software design, John Lackos, ISBN 0201633620, Addison-Wesley, July 1996.</p>
<p>
Design Patterns, Elements of Reusable Object-Oriented Software, Erich Gamma, Richard Helm, Ralph Jhonson, and John Vlissides, Addison-Wesley, 1995.</p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="wrap_up.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><img src="theme/r_arr_disabled.gif" border="0"></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
