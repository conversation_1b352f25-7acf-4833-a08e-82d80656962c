<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Polymorphic functions</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="place_holders.html">
<link rel="next" href="organization.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Polymorphic functions</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="place_holders.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="organization.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
We've seen the examples and we are already aware that lazy functions are polymorphic. This is important and is reiterated over and over again. Monomorphic functions are passe and simply lacks the horse power in this day and age of generic programming.</p>
<p>
The framework provides facilities for defining truly polymorphic functions (in <a href="https://people.cs.umass.edu/~yannis/fc++/">
FC++</a> jargon, these are called rank-2 polymorphic functoids). For instance, the plus example above can apply to integers, floating points, user defined complex numbers or even strings. Example:</p>
<code><pre>
        <span class=identifier>add</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>, </span><span class=identifier>arg2</span><span class=special>)(</span><span class=identifier>std</span><span class=special>::</span><span class=identifier>string</span><span class=special>(</span><span class=string>&quot;Hello&quot;</span><span class=special>), </span><span class=string>&quot; World&quot;</span><span class=special>)
</span></pre></code>
<p>
evaluates to std::string(&quot;Hello World&quot;). The observant reader might notice that this function call in fact takes in heterogeneous arguments of types arg1 = std::string and arg2 = char const*. add still works in this context precisely because the C++ standard library allows the expression a + b where a is a std::string and b is a char const*.</p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="place_holders.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="organization.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
