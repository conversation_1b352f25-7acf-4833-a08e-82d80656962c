<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Composites</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="variables.html">
<link rel="next" href="functions.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Composites</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="variables.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="functions.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
Actors may be combined in a multitude of ways to form composites. Composites are actors that are composed of zero or more actors. Composition is hierarchical. An element of the composite can be a primitive or again another composite. The flexibility to arbitrarily compose hierarchical structures allows us to form intricate constructions that model complex functions, statements and expressions.</p>
<p>
A composite is more or less a tuple of 0..N actors plus an operation object (some specialized composites have implied operations, i.e. the composite itself implies the operation). The composite class is declared informally as:</p>
<code><pre>
    <span class=keyword>template </span><span class=special>&lt;
        </span><span class=keyword>typename </span><span class=identifier>OperationT</span><span class=special>,
        </span><span class=keyword>typename </span><span class=identifier>A0 </span><span class=special>= </span><span class=identifier>nil_t</span><span class=special>,
        </span><span class=keyword>typename </span><span class=identifier>A1 </span><span class=special>= </span><span class=identifier>nil_t</span><span class=special>,
        </span><span class=keyword>typename </span><span class=identifier>A2 </span><span class=special>= </span><span class=identifier>nil_t</span><span class=special>,
         ...
        </span><span class=keyword>typename </span><span class=identifier>AN </span><span class=special>= </span><span class=identifier>nil_t
    </span><span class=special>&gt;
    </span><span class=keyword>struct </span><span class=identifier>composite </span><span class=special>{

        </span><span class=identifier>OperationT </span><span class=identifier>op</span><span class=special>;              //  </span><span class=identifier>operation
        </span><span class=identifier>A0 </span><span class=identifier>a0</span><span class=special>; </span><span class=identifier>A1 </span><span class=identifier>a1</span><span class=special>; ... </span><span class=identifier>AN </span><span class=identifier>an</span><span class=special>;    //  </span><span class=identifier>actors
    </span><span class=special>};
</span></pre></code>
<p>
This can be recursive. As mentioned, each of the actors A0..AN can in turn be another composite since a composite is itself an actor superclass and conforms to its expected conceptual interface. Composite specializations are provided to handle different numbers of actors from zero (0) to a predefined maximum.</p>
<p>
Except for specialized composites, like the actor and unlike the primitives, the composite is a protocol class. A composite does not really know how to perform anything. The actual operation is handled by its actors and finally its operation 'op'. After it has received the arguments passed in by the actor (see actor), all of the arguments are broadcasted to all of the composite's actors for preprocessing. Each of the composite's actors in turn returns a result. These results are then transfered to the composite's operation 'op'.</p>
<p>
If this may seem confusing at first, don't fret. Further details will be provided later for those who are inclined to learn more about the framework inside out. However, such information is not at all required to use the framework. After all, composites are not created directly. Instead, some facilities are provided for the generation of composites. These generators are the front-ends. We have seen the var(x), the val(x) and the const(x). These are really generators that create primitives. Likewise, we also have generators that create composites.</p>
<p>
Just think of composites as your backbone. You don't really have to scrutinize it to use it; it simply works. The composite is indeed the backbone of the Phoenix framework.</p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="variables.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="functions.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
