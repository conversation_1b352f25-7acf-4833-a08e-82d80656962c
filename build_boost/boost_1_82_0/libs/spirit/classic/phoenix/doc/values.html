<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Values</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="arguments.html">
<link rel="next" href="variables.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Values</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="arguments.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="variables.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
Whenever we see a constant in a curryable-function such as the plus above, an actor&lt;value&lt;T&gt; &gt; (where T is the type of the constant) is, by default, automatically created for us. For instance, the example plus above is actually equivalent to:</p>
<code><pre>
    <span class=identifier>plus</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>, </span><span class=identifier>actor</span><span class=special>&lt;</span><span class=identifier>value</span><span class=special>&lt;</span><span class=keyword>int</span><span class=special>&gt; &gt;(</span><span class=identifier>value</span><span class=special>&lt;</span><span class=keyword>int</span><span class=special>&gt;(</span><span class=number>6</span><span class=special>)))
</span></pre></code>
<p>
A nifty shortcut is the val(v) utility function. The expression above is also equivalent to:</p>
<code><pre>
    <span class=identifier>plus</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>, </span><span class=identifier>val</span><span class=special>(</span><span class=number>6</span><span class=special>))
</span></pre></code>
<p>
actor&lt;value&lt;int&gt; &gt;(value&lt;int&gt;(6)) is implicitly created behind the scenes, so there's really no need to explicitly type everything but:</p>
<code><pre>
    <span class=identifier>plus</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>, </span><span class=number>6</span><span class=special>)
</span></pre></code>
<p>
There are situations though, as we'll see later on, where we might want to explicitly write val(x).</p>
<p>
Like arguments, values are also actors. As such, values can be evaluated through the actor's operator(). Such invocation gives the value's identity. Example:</p>
<code><pre>
    <span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>val</span><span class=special>(</span><span class=number>3</span><span class=special>)() &lt;&lt; </span><span class=identifier>val</span><span class=special>(</span><span class=string>&quot;Hello World&quot;</span><span class=special>)();

    </span><span class=identifier>prints </span><span class=identifier>out </span><span class=string>&quot;3 Hello World&quot;</span><span class=special>.
</span></pre></code>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="arguments.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="variables.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
