<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Functions</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="composites.html">
<link rel="next" href="operators.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Functions</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="composites.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="operators.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<a name="lazy_functions"></a><h2>Lazy functions</h2><p>
This class provides a mechanism for lazily evaluating functions. Syntactically, a lazy function looks like an ordinary C/C++ function. The function call looks familiar and feels the same as ordinary C++ functions. However, unlike ordinary functions, the actual function execution is deferred. For example here are sample factorial function calls:</p>
<code><pre>
    <span class=identifier>factorial</span><span class=special>(</span><span class=number>4</span><span class=special>)
    </span><span class=identifier>factorial</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>)
    </span><span class=identifier>factorial</span><span class=special>(</span><span class=identifier>arg1 </span><span class=special>* </span><span class=number>6 </span><span class=special>/ </span><span class=identifier>factorial</span><span class=special>(</span><span class=identifier>var</span><span class=special>(</span><span class=identifier>i</span><span class=special>)))
</span></pre></code>
<p>
These functions are automatically lazily bound unlike ordinary function pointers or functor objects that need to be explicitly bound through the bind function (see binders).</p>
<p>
A lazy function works in conjunction with a user defined functor (as usual with a member operator()). Only special forms of functor objects are allowed. This is required to enable true polymorphism (STL style monomorphic functors and function pointers can still be used through the bind facility (see binders)).</p>
<p>
This special functor is expected to have a nested template class result&lt;T0...TN&gt; (where N is the number of arguments of its member operator()). The nested template class result should have a typedef 'type' that reflects the return type of its member operator(). This is essentially a type computer that answers the metaprogramming question &quot;Given arguments of type T0...TN, what will be the functor operator()'s return type?&quot;.</p>
<p>
There is a special case for functors that accept no arguments. Such nullary functors are only required to define a typedef result_type that reflects the return type of its operator().</p>
<p>
Here's an example of a simple functor that computes the factorial of a number:</p>
<code><pre>
    <span class=keyword>struct </span><span class=identifier>factorial_impl </span><span class=special>{

        </span><span class=keyword>template </span><span class=special>&lt;</span><span class=keyword>typename </span><span class=identifier>Arg</span><span class=special>&gt;
        </span><span class=keyword>struct </span><span class=identifier>result </span><span class=special>{ </span><span class=keyword>typedef </span><span class=identifier>Arg </span><span class=identifier>type</span><span class=special>; };

        </span><span class=keyword>template </span><span class=special>&lt;</span><span class=keyword>typename </span><span class=identifier>Arg</span><span class=special>&gt;
        </span><span class=identifier>Arg </span><span class=keyword>operator</span><span class=special>()(</span><span class=identifier>Arg </span><span class=identifier>n</span><span class=special>) </span><span class=keyword>const
        </span><span class=special>{ </span><span class=keyword>return </span><span class=special>(</span><span class=identifier>n </span><span class=special>&lt;= </span><span class=number>0</span><span class=special>) ? </span><span class=number>1 </span><span class=special>: </span><span class=identifier>n </span><span class=special>* </span><span class=keyword>this</span><span class=special>-&gt;</span><span class=keyword>operator</span><span class=special>()(</span><span class=identifier>n</span><span class=special>-</span><span class=number>1</span><span class=special>); }
    };
</span></pre></code>
<p>
As can be seen, the functor is polymorphic. Its arguments and return type are not fixed to a particular type. The example above for example, can handle any type as long as it can carry out the required operations (i.e. &lt;=, * and -).</p>
<p>
We can now declare and instantiate a lazy 'factorial' function:</p>
<code><pre>
    <span class=identifier>function</span><span class=special>&lt;</span><span class=identifier>factorial_impl</span><span class=special>&gt; </span><span class=identifier>factorial</span><span class=special>;
</span></pre></code>
<p>
Invoking a lazy function 'factorial' does not immediately execute the functor factorial_impl. Instead, a composite object is created and returned to the caller. Example:</p>
<code><pre>
    <span class=identifier>factorial</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>)
</span></pre></code>
<p>
does nothing more than return a composite. A second function call will invoke the actual factorial function. Example:</p>
<code><pre>
    <span class=keyword>int </span><span class=identifier>i </span><span class=special>= </span><span class=number>4</span><span class=special>;
    </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>factorial</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>)(</span><span class=identifier>i</span><span class=special>);
</span></pre></code>
<p>
will print out &quot;24&quot;.</p>
<p>
Take note that in certain cases (e.g. for functors with state), an instance of the functor may be passed on to the constructor. Example:</p>
<code><pre>
    <span class=identifier>function</span><span class=special>&lt;</span><span class=identifier>factorial_impl</span><span class=special>&gt; </span><span class=identifier>factorial</span><span class=special>(</span><span class=identifier>ftor</span><span class=special>);
</span></pre></code>
<p>
where ftor is an instance of factorial_impl (this is not necessary in this case since factorial is a simple stateless functor). Take care though when using functors with state because the functors are taken in by value. It is best to keep the data manipulated by a functor outside the functor itself and keep a reference to this data inside the functor. Also, it is best to keep functors as small as possible.</p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="composites.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="operators.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
