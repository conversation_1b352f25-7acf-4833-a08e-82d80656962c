<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Statements</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="operators.html">
<link rel="next" href="binders.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Statements</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="operators.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="binders.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<a name="lazy_statements"></a><h2>Lazy statements</h2><p>
The primitives and composite building blocks presented before are sufficiently powerful to construct quite elaborate structures and facilities. We have presented lazy-functions and lazy-operators. How about lazy-statements? First, an appetizer:</p>
<p>
Print all odd-numbered contents of an STL container using std::for_each (sample4.cpp):</p>
<code><pre>
    <span class=identifier>for_each</span><span class=special>(</span><span class=identifier>c</span><span class=special>.</span><span class=identifier>begin</span><span class=special>(), </span><span class=identifier>c</span><span class=special>.</span><span class=identifier>end</span><span class=special>(),
        </span><span class=identifier>if_</span><span class=special>(</span><span class=identifier>arg1 </span><span class=special>% </span><span class=number>2 </span><span class=special>== </span><span class=number>1</span><span class=special>)
        [
            </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg1 </span><span class=special>&lt;&lt; </span><span class=literal>' '
        </span><span class=special>]
    );
</span></pre></code>
<p>
Huh? Is that valid C++? Read on...</p>
<p>
Yes, it is valid C++. The sample code above is as close as you can get to the syntax of C++. This stylized C++ syntax differs from actual C++ code. First, the if has a trailing underscore. Second, the block uses square brackets  instead of the familiar curly braces {}.</p>
<p>
Here are more examples with annotations. The code almost speaks for itself.</p>
<p>
<b>1) block statement:</b></p>
<code><pre>
    <span class=identifier>statement</span><span class=special>,
    </span><span class=identifier>statement</span><span class=special>,
    ....
    </span><span class=identifier>statement
</span></pre></code>
<p>
Basically, these are comma separated statements. Take note that unlike the C/C++ semicolon, the comma is a separator put *in-between* statements. This is like Pascal's semicolon separator, rather than C/C++'s semicolon terminator. For example:</p>
<code><pre>
    <span class=identifier>statement</span><span class=special>,
    </span><span class=identifier>statement</span><span class=special>,
    </span><span class=identifier>statement</span><span class=special>,     //  </span><span class=identifier>ERROR</span><span class=special>!
</span></pre></code>
<p>
Is an error. The last statement should not have a comma. Block statements can be grouped using the parentheses. Again, the last statement in a group should not have a trailing comma.</p>
<code><pre>
    <span class=identifier>statement</span><span class=special>,
    </span><span class=identifier>statement</span><span class=special>,
    (
        </span><span class=identifier>statement</span><span class=special>,
        </span><span class=identifier>statement
    </span><span class=special>),
    </span><span class=identifier>statement
</span></pre></code>
<p>
Outside the square brackets, block statements should be grouped. For example:</p>
<code><pre>
    <span class=identifier>for_each</span><span class=special>(</span><span class=identifier>c</span><span class=special>.</span><span class=identifier>begin</span><span class=special>(), </span><span class=identifier>c</span><span class=special>.</span><span class=identifier>end</span><span class=special>(),
        (
            </span><span class=identifier>do_this</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>),
            </span><span class=identifier>do_that</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>)
        )
    );
</span></pre></code>
<p>
<b>2) if_ statement:</b></p>
<p>
We have seen the if_ statement. The syntax is:</p>
<code><pre>
    <span class=identifier>if_</span><span class=special>(</span><span class=identifier>conditional_expression</span><span class=special>)
    [
        </span><span class=identifier>sequenced_statements
    </span><span class=special>]
</span></pre></code>
<p>
<b>3) if_ else_ statement:</b></p>
<p>
The syntax is</p>
<code><pre>
    <span class=identifier>if_</span><span class=special>(</span><span class=identifier>conditional_expression</span><span class=special>)
    [
        </span><span class=identifier>sequenced_statements
    </span><span class=special>]
    .</span><span class=identifier>else_
    </span><span class=special>[
        </span><span class=identifier>sequenced_statements
    </span><span class=special>]
</span></pre></code>
<p>
Take note that else has a prefix dot and a trailing underscore: .else_</p>
<p>
Example: This code prints out all the elements and appends &quot; &gt; 5&quot;, &quot; == 5&quot; or &quot; &lt; 5&quot; depending on the element's actual value:</p>
<code><pre>
    <span class=identifier>for_each</span><span class=special>(</span><span class=identifier>c</span><span class=special>.</span><span class=identifier>begin</span><span class=special>(), </span><span class=identifier>c</span><span class=special>.</span><span class=identifier>end</span><span class=special>(),
        </span><span class=identifier>if_</span><span class=special>(</span><span class=identifier>arg1 </span><span class=special>&gt; </span><span class=number>5</span><span class=special>)
        [
            </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg1 </span><span class=special>&lt;&lt; </span><span class=string>&quot; &gt; 5\n&quot;
        </span><span class=special>]
        .</span><span class=identifier>else_
        </span><span class=special>[
            </span><span class=identifier>if_</span><span class=special>(</span><span class=identifier>arg1 </span><span class=special>== </span><span class=number>5</span><span class=special>)
            [
                </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg1 </span><span class=special>&lt;&lt; </span><span class=string>&quot; == 5\n&quot;
            </span><span class=special>]
            .</span><span class=identifier>else_
            </span><span class=special>[
                </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg1 </span><span class=special>&lt;&lt; </span><span class=string>&quot; &lt; 5\n&quot;
            </span><span class=special>]
        ]
    );
</span></pre></code>
<p>
Notice how the if_ else_ statement is nested.</p>
<p>
<b>4) while_ statement:</b></p>
<p>
The syntax is:</p>
<code><pre>
    <span class=identifier>while_</span><span class=special>(</span><span class=identifier>conditional_expression</span><span class=special>)
    [
        </span><span class=identifier>sequenced_statements
    </span><span class=special>]
</span></pre></code>
<p>
Example: This code decrements each element until it reaches zero and prints out the number at each step. A newline terminates the printout of each value.</p>
<code><pre>
    <span class=identifier>for_each</span><span class=special>(</span><span class=identifier>c</span><span class=special>.</span><span class=identifier>begin</span><span class=special>(), </span><span class=identifier>c</span><span class=special>.</span><span class=identifier>end</span><span class=special>(),
        (
            </span><span class=identifier>while_</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>--)
            [
                </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg1 </span><span class=special>&lt;&lt; </span><span class=string>&quot;, &quot;
            </span><span class=special>],
            </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>val</span><span class=special>(</span><span class=string>&quot;\n&quot;</span><span class=special>)
        )
    );
</span></pre></code>
<p>
<b>5) do_ while_ statement:</b></p>
<p>
The syntax is:</p>
<code><pre>
    <span class=identifier>do_
    </span><span class=special>[
        </span><span class=identifier>sequenced_statements
    </span><span class=special>]
    .</span><span class=identifier>while_</span><span class=special>(</span><span class=identifier>conditional_expression</span><span class=special>)
</span></pre></code>
<p>
Again, take note that while has a prefix dot and a trailing underscore: .while_</p>
<p>
Example: This code is almost the same as the previous example above with a slight twist in logic.</p>
<code><pre>
    <span class=identifier>for_each</span><span class=special>(</span><span class=identifier>c</span><span class=special>.</span><span class=identifier>begin</span><span class=special>(), </span><span class=identifier>c</span><span class=special>.</span><span class=identifier>end</span><span class=special>(),
        (
            </span><span class=identifier>do_
            </span><span class=special>[
                </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg1 </span><span class=special>&lt;&lt; </span><span class=string>&quot;, &quot;
            </span><span class=special>]
            .</span><span class=identifier>while_</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>--),
            </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>val</span><span class=special>(</span><span class=string>&quot;\n&quot;</span><span class=special>)
        )
    );
</span></pre></code>
<p>
<b>6) for_ statement:</b></p>
<p>
The syntax is:</p>
<code><pre>
    <span class=identifier>for_</span><span class=special>(</span><span class=identifier>init_statement</span><span class=special>, </span><span class=identifier>conditional_expression</span><span class=special>, </span><span class=identifier>step_statement</span><span class=special>)
    [
        </span><span class=identifier>sequenced_statements
    </span><span class=special>]
</span></pre></code>
<p>
It is again almost similar to C++ for statement. Take note that the init_statement, conditional_expression and step_statement are separated by the comma instead of the semi- colon and each must be present (i.e. for_(,,) is invalid).</p>
<p>
Example: This code prints each element N times where N is the element's value. A newline terminates the printout of each value.</p>
<code><pre>
    <span class=keyword>int </span><span class=identifier>iii</span><span class=special>;
    </span><span class=identifier>for_each</span><span class=special>(</span><span class=identifier>c</span><span class=special>.</span><span class=identifier>begin</span><span class=special>(), </span><span class=identifier>c</span><span class=special>.</span><span class=identifier>end</span><span class=special>(),
        (
            </span><span class=identifier>for_</span><span class=special>(</span><span class=identifier>var</span><span class=special>(</span><span class=identifier>iii</span><span class=special>) = </span><span class=number>0</span><span class=special>, </span><span class=identifier>var</span><span class=special>(</span><span class=identifier>iii</span><span class=special>) &lt; </span><span class=identifier>arg1</span><span class=special>, ++</span><span class=identifier>var</span><span class=special>(</span><span class=identifier>iii</span><span class=special>))
            [
                </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg1 </span><span class=special>&lt;&lt; </span><span class=string>&quot;, &quot;
            </span><span class=special>],
            </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>val</span><span class=special>(</span><span class=string>&quot;\n&quot;</span><span class=special>)
        )
    );
</span></pre></code>
<p>
As before, all these are lazily evaluated. The result of such statements are in fact composites that are passed on to STL's for_each function. In the viewpoint of for_each, what was passed is just a functor, no more, no less.</p>
<table width="80%" border="0" align="center">
  <tr>
    <td class="note_box">
<img src="theme/note.gif"></img> Unlike lazy functions and lazy operators, lazy statements always return void.    </td>
  </tr>
</table>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="operators.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="binders.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
