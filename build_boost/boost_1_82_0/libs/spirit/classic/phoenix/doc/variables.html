<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Variables</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="values.html">
<link rel="next" href="composites.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Variables</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="values.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="composites.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
Values are immutable constants which cannot be modified at all. Attempting to do so will result in a compile time error. When we want the function to modify the parameter, we use a variable instead. For instance, imagine a curryable (lazy) function plus_assign:</p>
<code><pre>
    <span class=identifier>plus_assign</span><span class=special>(</span><span class=identifier>x</span><span class=special>, </span><span class=identifier>y</span><span class=special>) { </span><span class=identifier>x </span><span class=special>+= </span><span class=identifier>y</span><span class=special>; }
</span></pre></code>
<p>
Here, we want the first function argument x to be mutable. Obviously, we cannot write:</p>
<code><pre>
    <span class=identifier>plus_assign</span><span class=special>(</span><span class=number>1</span><span class=special>, </span><span class=number>2</span><span class=special>) // </span><span class=identifier>error </span><span class=identifier>first </span><span class=identifier>argument </span><span class=identifier>is </span><span class=identifier>immutable
</span></pre></code>
<p>
In C++, we can pass in a reference to a variable as the first argument in our example above. Yet, by default, the Phoenix framework forces arguments passed to curryable functions to be constant immutable values. To achieve our intent, we use the variable&lt;T&gt; class. This is similar to the value&lt;T&gt; class above but instead holds a reference to a variable instead. For example:</p>
<code><pre>
    <span class=keyword>int </span><span class=identifier>i_</span><span class=special>;
    </span><span class=identifier>actor</span><span class=special>&lt;</span><span class=identifier>variable</span><span class=special>&lt;</span><span class=keyword>int</span><span class=special>&gt; &gt; </span><span class=identifier>i </span><span class=special>= </span><span class=identifier>i_</span><span class=special>;
</span></pre></code>
<p>
now, we can use our actor&lt;variable&lt;int&gt; &gt; 'i' as argument to the plus_assign lazy function:</p>
<code><pre>
    <span class=identifier>plus_assign</span><span class=special>(</span><span class=identifier>i</span><span class=special>, </span><span class=number>2</span><span class=special>)
</span></pre></code>
<p>
A shortcut is the var(v) utility function. The expression above is also equivalent to:</p>
<code><pre>
    <span class=identifier>plus_assign</span><span class=special>(</span><span class=identifier>var</span><span class=special>(</span><span class=identifier>i_</span><span class=special>), </span><span class=number>2</span><span class=special>)
</span></pre></code>
<p>
Lazy variables are actors. As such, variables can be evaluated through the actor's operator(). Such invocation gives the variables's identity. Example:</p>
<code><pre>
    <span class=keyword>int </span><span class=identifier>i </span><span class=special>= </span><span class=number>3</span><span class=special>;
    </span><span class=keyword>char </span><span class=keyword>const</span><span class=special>* </span><span class=identifier>s </span><span class=special>= </span><span class=string>&quot;Hello World&quot;</span><span class=special>;
    </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>var</span><span class=special>(</span><span class=identifier>i</span><span class=special>)() &lt;&lt; </span><span class=identifier>var</span><span class=special>(</span><span class=identifier>s</span><span class=special>)();
</span></pre></code>
<p>
prints out &quot;3 Hello World&quot;</p>
<p>
Finally, another free function const(cv) may also be used. const(cv) creates an actor&lt;variable&lt;T const&amp;&gt; &gt; object where the data is referenced using a constant reference. This is similar to value&lt;T&gt; but when the data to be passed as argument to a function is heavy and expensive to copy by value, the const(cv) offers a low overhead alternative.</p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="values.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="composites.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
