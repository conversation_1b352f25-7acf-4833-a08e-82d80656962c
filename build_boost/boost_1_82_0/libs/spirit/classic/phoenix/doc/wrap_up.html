<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Wrap up</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="interfacing.html">
<link rel="next" href="references.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Wrap up</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="interfacing.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="references.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
Sooner or later more FP techniques become standard practice as people find the true value of this programming discipline outside the academe and into the mainstream. In as much as the structured programming of the 70s and object oriented programming in the 80s and generic programming in the 90s shaped our thoughts towards a more robust sense of software engineering, FP will certainly be a paradigm that will catapult us towards more powerful software design and engineering onward into the new millenium.</p>
<p>
Let me quote Doug Gregor of <a href="http://www.boost.org">
Boost</a>.org. About functional style programming libraries:</p>
<blockquote><p><i>They're gaining acceptance, but are somewhat stunted by the ubiquitousness of broken compilers. The C++ community is moving deeper into the so-called &quot;STL-style&quot; programming paradigm, which brings many aspects of functional programming into the fold. Look at, for instance, the <a href="http://spirit.sourceforge.net">
Spirit</a> parser to see how such function objects can be used to build Yacc-like grammars with semantic actions that can build abstract syntax trees on the fly. This type of functional composition is gaining momentum.</i></p></blockquote><p>
Indeed. Phoenix is another attempt to introduce more FP techniques into the mainstream. Not only is it a tool that will make life easier for the programmer. In its own right, the actual design of the framework itself is a model of true C++ FP in action. The framework is designed and structured in a strict but clear and well mannered FP sense. By all means, use the framework as a tool. But for those who want to learn more about FP in C++, don't stop there, I invite you to take a closer look at the design of the framework itself.</p>
<p>
The whole framework is rather small and comprises of only a couple of header files. There are no object files to link against. Unlike most FP libraries in C++, Phoenix is portable to more C++ compilers in existence. Currently it works on Borland 5.5.1, Comeau 4.24, G++ 2.95.2, G++ 3.03, G++ 3.1, Intel 5.0, Intel 6.0, Code Warrior 7.2 and perhaps soon, to MSVC.</p>
<p>
So there you have it. Have fun! See you in the FP world.</p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="interfacing.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="references.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
