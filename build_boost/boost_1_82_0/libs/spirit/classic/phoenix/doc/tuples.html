<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Tuples</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="inside_phoenix.html">
<link rel="next" href="actors_revisited.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Tuples</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="inside_phoenix.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="actors_revisited.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
Tuples are the most basic infrastructure that the framework builds with. This sub-library provides a mechanism to bundle objects of arbitrary types in a single structure. Tuples hold heterogeneous types up to a predefined maximum.</p>
<p>
Only the most basic functionality needed are provided. This is a straight-forward and extremely lean and mean library. Unlike other recursive list-like tuple implementations, this tuple library implementation uses simple structs similar to std::pair with specialization for 0 to N tuple elements, where N is a predefined constant. There are only 4 tuple operations to learn:</p>
<p>
1)  Construction</p>
<p>
Here are examples on how to construct tuples:</p>
<code><pre>
    <span class=keyword>typedef </span><span class=identifier>tuple</span><span class=special>&lt;</span><span class=keyword>int</span><span class=special>, </span><span class=keyword>char</span><span class=special>&gt; </span><span class=identifier>t1_t</span><span class=special>;
    </span><span class=keyword>typedef </span><span class=identifier>tuple</span><span class=special>&lt;</span><span class=keyword>int</span><span class=special>, </span><span class=identifier>std</span><span class=special>::</span><span class=identifier>string</span><span class=special>, </span><span class=keyword>double</span><span class=special>&gt; </span><span class=identifier>t2_t</span><span class=special>;

    //  </span><span class=keyword>this </span><span class=identifier>tuple </span><span class=identifier>has </span><span class=identifier>an </span><span class=keyword>int </span><span class=keyword>and </span><span class=keyword>char </span><span class=identifier>members
    </span><span class=identifier>t1_t </span><span class=identifier>t1</span><span class=special>(</span><span class=number>3</span><span class=special>, </span><span class=literal>'c'</span><span class=special>);

    //  </span><span class=keyword>this </span><span class=identifier>tuple </span><span class=identifier>has </span><span class=identifier>an </span><span class=keyword>int</span><span class=special>, </span><span class=identifier>std</span><span class=special>::</span><span class=identifier>string </span><span class=keyword>and </span><span class=keyword>double </span><span class=identifier>members
    </span><span class=identifier>t2_t </span><span class=identifier>t2</span><span class=special>(</span><span class=number>3</span><span class=special>, </span><span class=string>&quot;hello&quot;</span><span class=special>, </span><span class=number>3.14</span><span class=special>);
</span></pre></code>
<p>
2)  Member access</p>
<p>
A member in a tuple can be accessed using the tuple's  operator by specifying the Nth tuple_index. Here are some examples:</p>
<code><pre>
    <span class=identifier>tuple_index</span><span class=special>&lt;</span><span class=number>0</span><span class=special>&gt; </span><span class=identifier>ix0</span><span class=special>; // </span><span class=number>0</span><span class=identifier>th </span><span class=identifier>index </span><span class=special>== </span><span class=number>1</span><span class=identifier>st </span><span class=identifier>item
    </span><span class=identifier>tuple_index</span><span class=special>&lt;</span><span class=number>1</span><span class=special>&gt; </span><span class=identifier>ix1</span><span class=special>; // </span><span class=number>1</span><span class=identifier>st </span><span class=identifier>index </span><span class=special>== </span><span class=number>2</span><span class=identifier>nd </span><span class=identifier>item
    </span><span class=identifier>tuple_index</span><span class=special>&lt;</span><span class=number>2</span><span class=special>&gt; </span><span class=identifier>ix2</span><span class=special>; // </span><span class=number>2</span><span class=identifier>nd </span><span class=identifier>index </span><span class=special>== </span><span class=number>3</span><span class=identifier>rd </span><span class=identifier>item

    </span><span class=comment>//  Note zero based indexing. 0 = 1st item, 1 = 2nd item

    </span><span class=identifier>t1</span><span class=special>[</span><span class=identifier>ix0</span><span class=special>] = </span><span class=number>33</span><span class=special>;       // </span><span class=identifier>sets </span><span class=identifier>the </span><span class=keyword>int </span><span class=identifier>member </span><span class=identifier>of </span><span class=identifier>the </span><span class=identifier>tuple </span><span class=identifier>t1
    </span><span class=identifier>t2</span><span class=special>[</span><span class=identifier>ix2</span><span class=special>] = </span><span class=number>6e6</span><span class=special>;      // </span><span class=identifier>sets </span><span class=identifier>the </span><span class=keyword>double </span><span class=identifier>member </span><span class=identifier>of </span><span class=identifier>the </span><span class=identifier>tuple </span><span class=identifier>t2
    </span><span class=identifier>t1</span><span class=special>[</span><span class=identifier>ix1</span><span class=special>] = </span><span class=literal>'a'</span><span class=special>;      // </span><span class=identifier>sets </span><span class=identifier>the </span><span class=keyword>char </span><span class=identifier>member </span><span class=identifier>of </span><span class=identifier>the </span><span class=identifier>tuple </span><span class=identifier>t1
</span></pre></code>
<p>
Access to out of bound indexes returns a nil_t value.</p>
<p>
3)  Member type inquiry</p>
<p>
The type of an individual member can be queried. Example:</p>
<code><pre>
    <span class=identifier>tuple_element</span><span class=special>&lt;</span><span class=number>1</span><span class=special>, </span><span class=identifier>t2_t</span><span class=special>&gt;::</span><span class=identifier>type
</span></pre></code>
<p>
Refers to the type of the second member (again note zero based indexing, hence 0 = 1st item, 1 = 2nd item) of the tuple.</p>
<p>
Access to out of bound indexes returns a nil_t type.</p>
<p>
4)  Tuple length</p>
<p>
The number of elements in a tuple can be queried. Example:</p>
<code><pre>
    <span class=keyword>int </span><span class=identifier>n </span><span class=special>= </span><span class=identifier>t1</span><span class=special>.</span><span class=identifier>length</span><span class=special>;
</span></pre></code>
<p>
gets the number of elements in tuple t1.</p>
<p>
length is a static constant. Thus, TupleT::length also works. Example:</p>
<code><pre>
    <span class=keyword>int </span><span class=identifier>n </span><span class=special>= </span><span class=identifier>t1_t</span><span class=special>::</span><span class=identifier>length</span><span class=special>;
</span></pre></code>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="inside_phoenix.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="actors_revisited.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
