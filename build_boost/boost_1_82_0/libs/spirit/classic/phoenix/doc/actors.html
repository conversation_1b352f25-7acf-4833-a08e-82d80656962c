<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Actors</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="organization.html">
<link rel="next" href="primitives.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Actors</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="organization.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="primitives.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
Actors are functors. Actors are the main driving force behind the framework. An actor can accept 0 to N arguments (where N is a predefined maximum). In an abstract point of view, an actor is the metaphor of a function declaration. The actor has no function body at all, which means that it does not know how to perform any function at all.</p>
<table width="80%" border="0" align="center">
  <tr>
    <td class="note_box">
<img src="theme/note.gif"></img> an actor is the metaphor of a function declaration    </td>
  </tr>
</table>
<p>
The actor is a template class though, and its sole template parameter fills in the missing function body and does the actual function evaluation. The actor class derives from its template argument. Here's the simplified actor class declaration:</p>
<code><pre>
    <span class=keyword>template </span><span class=special>&lt;</span><span class=keyword>typename </span><span class=identifier>BaseT</span><span class=special>&gt;
    </span><span class=keyword>struct </span><span class=identifier>actor </span><span class=special>: </span><span class=keyword>public </span><span class=identifier>BaseT </span><span class=special>{ /*...*/ };
</span></pre></code>
<p>
To avoid being overwhelmed in details, the following is a brief overview of what an actor is. First, imagine an actor as a non- lazy function that accepts 0..N arguments:</p>
<code><pre>
    <span class=identifier>actor</span><span class=special>(</span><span class=identifier>a0</span><span class=special>, </span><span class=identifier>a1</span><span class=special>, ... </span><span class=identifier>aN</span><span class=special>)
</span></pre></code>
<p>
Not knowing what to do with the arguments passed in, the actor forwards the arguments received from the client (caller) onto its base class BaseT. It is the base class that does the actual operation, finally returning a result. In essence, the actor's base class is the metaphor of the function body. The sequence of events that transpire is outlined informally as follows:</p>
<p>
1)  actor is called, passing in N arguments:</p>
<p>
client --&gt; actor(a0, a1, ... aN)</p>
<p>
2)  actor forwards the arguments to its base:</p>
<p>
--&gt; actor's base(a0, a1, ... aN)</p>
<p>
3)  actor's base does some computation and returns a result back to the actor, and finally, the actor returns this back to the client:</p>
<p>
actor's base operation --&gt; return result --&gt; actor --&gt; client</p>
<table width="80%" border="0" align="center">
  <tr>
    <td class="note_box">
<img src="theme/note.gif"></img> In essence, the actor's base class is the metaphor of the function body    </td>
  </tr>
</table>
<p>
For further details, we shall see more in-depth information later as we move on to the more technical side of the framework.</p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="organization.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="primitives.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
