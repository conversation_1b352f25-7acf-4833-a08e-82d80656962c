<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Efficiency</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="lazy_construction.html">
<link rel="next" href="inside_phoenix.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Efficiency</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="lazy_construction.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="inside_phoenix.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
Now this is important. Operators that form expressions and statements, while truly expressive, should be used judiciously and sparingly. While aggressive compiler optimizations and inline code helps a lot to produce tighter and faster code, lazy operators and statements will always have more overhead compared to lazy- functions and bound simple functors especially when the logic gets to be quite complex. It is not only run-time code that hits a penalty, complex expressions involving lazy-operators and lazy- functions are also much more difficult to parse and compile by the host C++ compiler and results in much longer compile times.</p>
<table width="80%" border="0" align="center">
  <tr>
    <td class="note_box">
<img src="theme/bulb.gif"></img> <b>Lambda vs. Offline Functions</b><br><br>The best way to use the framework is to write generic off-line lazy functions (see functions) then call these functions lazily using straight-forward inline lazy-operators and lazy-statements.    </td>
  </tr>
</table>
<p>
While it is indeed satisfying to impress others with quite esoteric uses of operator overloading and generative programming as can be done by lazy-operators and lazy-statements, these tools are meant to be used for the right job. That said, caveat-emptor.</p>
<table width="80%" border="0" align="center">
  <tr>
    <td class="note_box">
<img src="theme/note.gif"></img> need benchmarks, benchmarks, and more benchmarks    </td>
  </tr>
</table>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="lazy_construction.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="inside_phoenix.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
