<html>
<head>
<!-- Generated by the Spirit (http://spirit.sf.net) QuickDoc -->
<title>Arguments</title>
<link rel="stylesheet" href="theme/style.css" type="text/css">
<link rel="prev" href="primitives.html">
<link rel="next" href="values.html">
</head>
<body>
<table width="100%" height="48" border="0" background="theme/bkd2.gif" cellspacing="2">
  <tr>
    <td width="10">
    </td>
    <td width="85%">
      <font size="6" face="Verdana, Arial, Helvetica, sans-serif"><b>Arguments</b></font>
    </td>
    <td width="112"><a href="http://spirit.sf.net"><img src="theme/spirit.gif" align="right" border="0"></a></td>
  </tr>
</table>
<br>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="primitives.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="values.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<p>
The most basic primitive is the argument placeholder. For the sake of explanation, we used the '?' in our introductory examples to represent unknown arguments or argument place holders. Later on, we introduced the notion of positional argument place holders.</p>
<p>
We use an object of a special class argument&lt;N&gt; to represent the Nth function argument. The argument placeholder acts as an imaginary data-bin where a function argument will be placed.</p>
<p>
There are a couple of predefined instances of argument&lt;N&gt; named arg1..argN (where N is a predefined maximum). When appropriate, we can of course define our own argument&lt;N&gt; names. For example:</p>
<code><pre>
    <span class=identifier>actor</span><span class=special>&lt;</span><span class=identifier>argument</span><span class=special>&lt;</span><span class=number>0</span><span class=special>&gt; &gt; </span><span class=identifier>first_param</span><span class=special>;    // </span><span class=identifier>note </span><span class=identifier>zero </span><span class=identifier>based </span><span class=identifier>index
</span></pre></code>
<p>
Take note that it should be wrapped inside an actor to be useful. first_param can now be used as a parameter to a lazy function:</p>
<code><pre>
    <span class=identifier>plus</span><span class=special>(</span><span class=identifier>first_param</span><span class=special>, </span><span class=number>6</span><span class=special>)
</span></pre></code>
<p>
which is equivalent to:</p>
<code><pre>
    <span class=identifier>plus</span><span class=special>(</span><span class=identifier>arg1</span><span class=special>, </span><span class=number>6</span><span class=special>)
</span></pre></code>
<p>
Here are some sample preset definitions of arg1..N</p>
<code><pre>
    <span class=identifier>actor</span><span class=special>&lt;</span><span class=identifier>argument</span><span class=special>&lt;</span><span class=number>0</span><span class=special>&gt; &gt; </span><span class=keyword>const </span><span class=identifier>arg1 </span><span class=special>= </span><span class=identifier>argument</span><span class=special>&lt;</span><span class=number>0</span><span class=special>&gt;();
    </span><span class=identifier>actor</span><span class=special>&lt;</span><span class=identifier>argument</span><span class=special>&lt;</span><span class=number>1</span><span class=special>&gt; &gt; </span><span class=keyword>const </span><span class=identifier>arg2 </span><span class=special>= </span><span class=identifier>argument</span><span class=special>&lt;</span><span class=number>1</span><span class=special>&gt;();
    </span><span class=identifier>actor</span><span class=special>&lt;</span><span class=identifier>argument</span><span class=special>&lt;</span><span class=number>2</span><span class=special>&gt; &gt; </span><span class=keyword>const </span><span class=identifier>arg3 </span><span class=special>= </span><span class=identifier>argument</span><span class=special>&lt;</span><span class=number>2</span><span class=special>&gt;();
    ...
    </span><span class=identifier>actor</span><span class=special>&lt;</span><span class=identifier>argument</span><span class=special>&lt;</span><span class=identifier>N</span><span class=special>&gt; &gt; </span><span class=keyword>const </span><span class=identifier>argN </span><span class=special>= </span><span class=identifier>argument</span><span class=special>&lt;</span><span class=identifier>N</span><span class=special>&gt;();
</span></pre></code>
<p>
An argument is in itself an actor base class. As such, arguments can be evaluated through the actor's operator(). An argument as an actor base class selects the Nth argument from the arguments passed in by the client (see actor).</p>
<p>
For example:</p>
<code><pre>
    <span class=keyword>char        </span><span class=identifier>c </span><span class=special>= </span><span class=literal>'A'</span><span class=special>;
    </span><span class=keyword>int         </span><span class=identifier>i </span><span class=special>= </span><span class=number>123</span><span class=special>;
    </span><span class=keyword>const </span><span class=keyword>char</span><span class=special>* </span><span class=identifier>s </span><span class=special>= </span><span class=string>&quot;Hello World&quot;</span><span class=special>;

    </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg1</span><span class=special>(</span><span class=identifier>c</span><span class=special>) &lt;&lt; </span><span class=literal>' '</span><span class=special>;     //  </span><span class=identifier>Get </span><span class=identifier>the </span><span class=number>1</span><span class=identifier>st </span><span class=identifier>argument </span><span class=identifier>of </span><span class=identifier>unnamed_f</span><span class=special>(</span><span class=identifier>c</span><span class=special>)
    </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg1</span><span class=special>(</span><span class=identifier>i</span><span class=special>, </span><span class=identifier>s</span><span class=special>) &lt;&lt; </span><span class=literal>' '</span><span class=special>;  //  </span><span class=identifier>Get </span><span class=identifier>the </span><span class=number>1</span><span class=identifier>st </span><span class=identifier>argument </span><span class=identifier>of </span><span class=identifier>unnamed_f</span><span class=special>(</span><span class=identifier>i</span><span class=special>, </span><span class=identifier>s</span><span class=special>)
    </span><span class=identifier>cout </span><span class=special>&lt;&lt; </span><span class=identifier>arg2</span><span class=special>(</span><span class=identifier>i</span><span class=special>, </span><span class=identifier>s</span><span class=special>) &lt;&lt; </span><span class=literal>' '</span><span class=special>;  //  </span><span class=identifier>Get </span><span class=identifier>the </span><span class=number>2</span><span class=identifier>nd </span><span class=identifier>argument </span><span class=identifier>of </span><span class=identifier>unnamed_f</span><span class=special>(</span><span class=identifier>i</span><span class=special>, </span><span class=identifier>s</span><span class=special>)
</span></pre></code>
<p>
will print out &quot;A 123 Hello World&quot;</p>
<table border="0">
  <tr>
    <td width="30"><a href="../index.html"><img src="theme/u_arr.gif" border="0"></a></td>
    <td width="30"><a href="primitives.html"><img src="theme/l_arr.gif" border="0"></a></td>
    <td width="20"><a href="values.html"><img src="theme/r_arr.gif" border="0"></a></td>
   </tr>
</table>
<br>
<hr size="1">
<p class="copyright">Copyright &copy; 2001-2002 Joel de Guzman<br>
  <br>
<font size="2">Use, modification and distribution is subject to the Boost Software
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt) </font> </p>
</body>
</html>
