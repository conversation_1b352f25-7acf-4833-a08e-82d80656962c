<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Binary Generators</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Spirit 2.59">
<link rel="up" href="../primitive_generators.html" title="Karma Generators">
<link rel="prev" href="stream.html" title="Stream Generators">
<link rel="next" href="auxiliary.html" title="Auxiliary Generators">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="stream.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../primitive_generators.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="auxiliary.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="spirit.karma.quick_reference.primitive_generators.binary"></a><a class="link" href="binary.html" title="Binary Generators">Binary
          Generators</a>
</h5></div></div></div>
<p>
            See here for more information about <a class="link" href="../../reference/binary.html" title="Binary Generators">Binary
            Generators</a>.
          </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Expression
                    </p>
                  </th>
<th>
                    <p>
                      Attribute
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
</tr></thead>
<tbody>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_native.html" title="Binary Native Endianness Generators"><code class="computeroutput"><span class="identifier">byte_</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      8 bits native endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate an 8 bit binary
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_native.html" title="Binary Native Endianness Generators"><code class="computeroutput"><span class="identifier">word</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      16 bits native endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate a 16 bit binary in native endian representation
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_big.html" title="Binary Big Endianness Generators"><code class="computeroutput"><span class="identifier">big_word</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      16 bits big endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate a 16 bit binary in big endian representation
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_little.html" title="Binary Little Endianness Generators"><code class="computeroutput"><span class="identifier">little_word</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      16 bits little endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate a 16 bit binary in little endian representation
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_native.html" title="Binary Native Endianness Generators"><code class="computeroutput"><span class="identifier">dword</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      32 bits native endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate a 32 bit binary in native endian representation
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_big.html" title="Binary Big Endianness Generators"><code class="computeroutput"><span class="identifier">big_dword</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      32 bits big endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate a 32 bit binary in big endian representation
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_little.html" title="Binary Little Endianness Generators"><code class="computeroutput"><span class="identifier">little_dword</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      32 bits little endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate a 32 bit binary in little endian representation
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_native.html" title="Binary Native Endianness Generators"><code class="computeroutput"><span class="identifier">qword</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      64 bits native endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate a 64 bit binary in native endian representation
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_big.html" title="Binary Big Endianness Generators"><code class="computeroutput"><span class="identifier">big_qword</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      64 bits big endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate a 64 bit binary in big endian representation
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../../reference/binary/binary_little.html" title="Binary Little Endianness Generators"><code class="computeroutput"><span class="identifier">little_qword</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      64 bits little endian
                    </p>
                  </td>
<td>
                    <p>
                      Generate a 64 bit binary in little endian representation
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">pad</span><span class="special">(</span><span class="identifier">num</span><span class="special">)</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">Unused</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Generate additional null bytes allowing to align generated
                      output with memory addresses divisible by <code class="computeroutput"><span class="identifier">num</span></code>.
                    </p>
                  </td>
</tr>
</tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2001-2011 Joel de Guzman, Hartmut Kaiser<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="stream.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../primitive_generators.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="auxiliary.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
