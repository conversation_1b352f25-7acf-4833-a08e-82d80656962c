<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Spirit V2.3</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Spirit 2.59">
<link rel="up" href="../what_s_new.html" title="What's New">
<link rel="prev" href="spirit_2_4.html" title="Spirit V2.4">
<link rel="next" href="spirit_2_2.html" title="Spirit V2.2">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="spirit_2_4.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../what_s_new.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="spirit_2_2.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="spirit.what_s_new.spirit_2_3"></a><a class="link" href="spirit_2_3.html" title="Spirit V2.3">Spirit V2.3</a>
</h3></div></div></div>
<h5>
<a name="spirit.what_s_new.spirit_2_3.h0"></a>
        <span class="phrase"><a name="spirit.what_s_new.spirit_2_3.what_s_changed_in__emphasis_spirit_qi__emphasis__and__emphasis_spirit_karma__emphasis__from_v2_2__boost_v1_42_0__to_v2_3__boost_v1_43_0_"></a></span><a class="link" href="spirit_2_3.html#spirit.what_s_new.spirit_2_3.what_s_changed_in__emphasis_spirit_qi__emphasis__and__emphasis_spirit_karma__emphasis__from_v2_2__boost_v1_42_0__to_v2_3__boost_v1_43_0_">What's
        changed in <span class="emphasis"><em>Spirit.Qi</em></span> and <span class="emphasis"><em>Spirit.Karma</em></span>
        from V2.2 (Boost V1.42.0) to V2.3 (Boost V1.43.0)</a>
      </h5>
<h5>
<a name="spirit.what_s_new.spirit_2_3.h1"></a>
        <span class="phrase"><a name="spirit.what_s_new.spirit_2_3.new_features"></a></span><a class="link" href="spirit_2_3.html#spirit.what_s_new.spirit_2_3.new_features">New
        Features</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            The customization point <code class="computeroutput"><span class="identifier">transform_attribute</span></code>
            now has to implement a third function: <code class="computeroutput"><span class="keyword">void</span>
            <span class="identifier">fail</span><span class="special">(</span><span class="identifier">Exposed</span><span class="special">&amp;)</span></code>,
            which normally will do nothing. This function will be called whenever
            the right hand side of the <code class="computeroutput"><span class="identifier">rule</span></code>
            (or the embedded parser of <code class="computeroutput"><span class="identifier">attr_cast</span></code>)
            fail parsing. This change affects <span class="emphasis"><em>Qi</em></span> only. See the
            description of the <a class="link" href="../advanced/customize/transform.html" title="Transform an Attribute to a Different Type (Qi and Karma)"><code class="computeroutput"><span class="identifier">traits</span><span class="special">::</span><span class="identifier">transform_attribute</span></code></a> for more
            details.
          </li>
<li class="listitem">
            Added support for attribute sequences created with <code class="computeroutput"><span class="identifier">BOOST_FUSION_ADAPT_CLASS</span></code>
            and <code class="computeroutput"><span class="identifier">BOOST_FUSION_ADAPT_CLASS_NAMED</span></code>.
            This support requires to include the new header file: <code class="computeroutput"><span class="preprocessor">#include</span>
            <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">spirit</span><span class="special">/</span><span class="identifier">include</span><span class="special">/</span><span class="identifier">support_adapt_class_attributes</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>.
          </li>
<li class="listitem">
            Added <code class="computeroutput"><span class="identifier">karma</span><span class="special">::</span><span class="identifier">ostream_iterator</span></code> as a counterpart to
            <code class="computeroutput"><span class="identifier">qi</span><span class="special">::</span><span class="identifier">istream_iterator</span></code> (see new header file:
            <code class="computeroutput"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">spirit</span><span class="special">/</span><span class="identifier">home</span><span class="special">/</span><span class="identifier">support</span><span class="special">/</span><span class="identifier">iterators</span><span class="special">/</span><span class="identifier">ostream_iterator</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>).
          </li>
<li class="listitem">
            Added <code class="computeroutput"><span class="identifier">qi</span><span class="special">::</span><span class="identifier">hold</span></code> allowing to make sure the embedded
            parser does not touch the passed attribute in case it fails parsing.
          </li>
<li class="listitem">
            Added <a class="link" href="../qi/reference/directive/no_skip.html" title="Parser Directive Inhibiting Skipping Without Pre-skip (no_skip[])"><code class="computeroutput"><span class="identifier">qi</span><span class="special">::</span><span class="identifier">no_skip</span></code></a> directive, which is
            equivalent to <code class="computeroutput"><span class="identifier">qi</span><span class="special">::</span></code><a class="link" href="../qi/reference/directive/lexeme.html" title="Parser Directive Inhibiting Skipping (lexeme[])"><code class="computeroutput"><span class="identifier">lexeme</span></code></a>,
            except that it does not pre-skip.
          </li>
<li class="listitem">
            Added <a class="link" href="../karma/reference/directive/delimit.html" title="Generator Directives Controlling Automatic Delimiting (verbatim[], no_delimit[], delimit[])"><code class="computeroutput"><span class="identifier">karma</span><span class="special">::</span><span class="identifier">no_delimit</span></code></a> directive, which
            is equivalent to <code class="computeroutput"><span class="identifier">karma</span><span class="special">::</span></code><a class="link" href="../karma/reference/directive/delimit.html" title="Generator Directives Controlling Automatic Delimiting (verbatim[], no_delimit[], delimit[])"><code class="computeroutput"><span class="identifier">verbatim</span></code></a>, except that it does
            not perform a post-delimiting step.
          </li>
<li class="listitem">
            Added a new input_iterator policy for the <code class="computeroutput"><span class="identifier">multi_pass</span></code>
            iterator framework (named <code class="computeroutput"><span class="identifier">buffering_input_iterator</span></code>)
            allowing to wrap underlying input iterators which do not store the last
            character read from the input (such as <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">istream_iterator</span></code>).
            This is now used as the default input policy.
          </li>
</ul></div>
<h5>
<a name="spirit.what_s_new.spirit_2_3.h2"></a>
        <span class="phrase"><a name="spirit.what_s_new.spirit_2_3.bug_fixes"></a></span><a class="link" href="spirit_2_3.html#spirit.what_s_new.spirit_2_3.bug_fixes">Bug
        Fixes</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Sequences (in <span class="emphasis"><em>Qi</em></span> and <span class="emphasis"><em>Karma</em></span>)
            may now have a component having no attribute even as their last element.
          </li>
<li class="listitem">
            Sequences (in <span class="emphasis"><em>Qi</em></span> and <span class="emphasis"><em>Karma</em></span>)
            can now take one element attribute sequences as their attribute.
          </li>
<li class="listitem">
            Constructs like <code class="computeroutput"><span class="identifier">karma</span><span class="special">::</span><span class="identifier">buffer</span><span class="special">[</span><span class="identifier">karma</span><span class="special">::</span><span class="identifier">buffer</span><span class="special">[...]]</span></code> don't result in performing double
            buffering anymore. The same is true if an alternative is wrapped into
            a <code class="computeroutput"><span class="identifier">karma</span><span class="special">::</span><span class="identifier">buffer</span><span class="special">[]</span></code>
            directive (as for instance: <code class="computeroutput"><span class="identifier">buffer</span><span class="special">[</span><span class="identifier">a</span><span class="special">]</span> <span class="special">|</span> <span class="identifier">b</span></code>).
          </li>
<li class="listitem">
            The <span class="emphasis"><em>Spirit.Karma</em></span> output iterator (which is used
            internally, but also is exposed when using the stream based API) is now
            properly copyable (thanks to Jonas Persson for reporting this issue).
          </li>
<li class="listitem">
            The default <code class="computeroutput"><span class="identifier">multi_pass</span></code>
            iterator is now usable with underlying input iterators which do not store
            the last character read from the input (such as <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">istream_iterator</span></code>).
            Thanks to Larry Evans and Peter Schueller for independently reporting
            this problem.
          </li>
<li class="listitem">
            The directive <code class="computeroutput"><span class="identifier">karma</span><span class="special">::</span><span class="identifier">omit</span><span class="special">[]</span></code>
            now does not accept an arbitrary attribute type anymore.
          </li>
<li class="listitem">
            The <span class="emphasis"><em>Spirit.Karma</em></span> predicates (the and-predicate and
            the not-predicate) and the directive <code class="computeroutput"><span class="identifier">karma</span><span class="special">::</span><span class="identifier">omit</span><span class="special">[]</span></code> now disable output altogether instead
            of intercepting the output into a buffer which got discarded as before.
          </li>
<li class="listitem">
            Fixed <code class="computeroutput"><span class="identifier">karma</span><span class="special">::</span><span class="identifier">rule</span></code> to properly handles optional attributes.
          </li>
</ul></div>
<h5>
<a name="spirit.what_s_new.spirit_2_3.h3"></a>
        <span class="phrase"><a name="spirit.what_s_new.spirit_2_3.what_s_changed_in__emphasis_spirit_lex__emphasis__from_v2_2__boost_v1_42_0__to_v2_3__boost_v1_43_0_"></a></span><a class="link" href="spirit_2_3.html#spirit.what_s_new.spirit_2_3.what_s_changed_in__emphasis_spirit_lex__emphasis__from_v2_2__boost_v1_42_0__to_v2_3__boost_v1_43_0_">What's
        changed in <span class="emphasis"><em>Spirit.Lex</em></span> from V2.2 (Boost V1.42.0) to V2.3
        (Boost V1.43.0)</a>
      </h5>
<h5>
<a name="spirit.what_s_new.spirit_2_3.h4"></a>
        <span class="phrase"><a name="spirit.what_s_new.spirit_2_3.new_lexer_features"></a></span><a class="link" href="spirit_2_3.html#spirit.what_s_new.spirit_2_3.new_lexer_features">New
        Lexer Features</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            The library does not minimize the generated lexer tables for dynamic
            lexers by default anymore. The generated tables will now be minimized
            for static lexers only.
          </li>
<li class="listitem">
            The function <code class="computeroutput"><span class="identifier">lexer</span><span class="special">&lt;&gt;::</span><span class="identifier">init_dfa</span><span class="special">()</span></code>
            now takes a single boolean parameter (which defaults to <code class="computeroutput"><span class="keyword">false</span></code>) allowing to force minimization
            of the generated lexer tables.
          </li>
</ul></div>
</div>
<div class="copyright-footer">Copyright © 2001-2011 Joel de Guzman, Hartmut Kaiser<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="spirit_2_4.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../what_s_new.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="spirit_2_2.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
