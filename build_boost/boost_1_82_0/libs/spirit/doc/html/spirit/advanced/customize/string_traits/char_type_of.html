<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Determine the Character Type of a String</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Spirit 2.59">
<link rel="up" href="../string_traits.html" title="Extract a C-Style String to Generate Output from a String Type (Karma)">
<link rel="prev" href="is_char.html" title="Determine if a Type is a Character">
<link rel="next" href="extract_c_string.html" title="Get a C-style String from a String Type">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_char.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../string_traits.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="extract_c_string.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="spirit.advanced.customize.string_traits.char_type_of"></a><a class="link" href="char_type_of.html" title="Determine the Character Type of a String">Determine
          the Character Type of a String</a>
</h5></div></div></div>
<h6>
<a name="spirit.advanced.customize.string_traits.char_type_of.h0"></a>
            <span class="phrase"><a name="spirit.advanced.customize.string_traits.char_type_of.char_type_of"></a></span><a class="link" href="char_type_of.html#spirit.advanced.customize.string_traits.char_type_of.char_type_of">char_type_of</a>
          </h6>
<p>
            This customization point is an MPL metafunction which returns the character
            type of a given string type. <code class="computeroutput"><span class="identifier">char_type_of</span></code>
            handles user-defined types such as std::string, as well as C-style strings.
          </p>
<h6>
<a name="spirit.advanced.customize.string_traits.char_type_of.h1"></a>
            <span class="phrase"><a name="spirit.advanced.customize.string_traits.char_type_of.module_headers"></a></span><a class="link" href="char_type_of.html#spirit.advanced.customize.string_traits.char_type_of.module_headers">Module
            Headers</a>
          </h6>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">spirit</span><span class="special">/</span><span class="identifier">home</span><span class="special">/</span><span class="identifier">support</span><span class="special">/</span><span class="identifier">string_traits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<p>
            Also, see <a class="link" href="../../../structure/include.html" title="Include">Include Structure</a>.
          </p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
              This header file does not need to be included directly by any user
              program as it is normally included by other Spirit header files relying
              on its content.
            </p></td></tr>
</table></div>
<h6>
<a name="spirit.advanced.customize.string_traits.char_type_of.h2"></a>
            <span class="phrase"><a name="spirit.advanced.customize.string_traits.char_type_of.namespace"></a></span><a class="link" href="char_type_of.html#spirit.advanced.customize.string_traits.char_type_of.namespace">Namespace</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup><col></colgroup>
<thead><tr><th>
                    <p>
                      Name
                    </p>
                  </th></tr></thead>
<tbody><tr><td>
                    <p>
                      <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">spirit</span><span class="special">::</span><span class="identifier">traits</span></code>
                    </p>
                  </td></tr></tbody>
</table></div>
<h6>
<a name="spirit.advanced.customize.string_traits.char_type_of.h3"></a>
            <span class="phrase"><a name="spirit.advanced.customize.string_traits.char_type_of.synopsis"></a></span><a class="link" href="char_type_of.html#spirit.advanced.customize.string_traits.char_type_of.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">char_type_of</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="special">&lt;</span><span class="identifier">unspecified</span><span class="special">&gt;</span> <span class="identifier">type</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<h6>
<a name="spirit.advanced.customize.string_traits.char_type_of.h4"></a>
            <span class="phrase"><a name="spirit.advanced.customize.string_traits.char_type_of.template_parameters"></a></span><a class="link" href="char_type_of.html#spirit.advanced.customize.string_traits.char_type_of.template_parameters">Template
            parameters</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Parameter
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
<th>
                    <p>
                      Default
                    </p>
                  </th>
</tr></thead>
<tbody><tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">T</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      A string type.
                    </p>
                  </td>
<td>
                    <p>
                      none
                    </p>
                  </td>
</tr></tbody>
</table></div>
<div class="variablelist">
<p class="title"><b>Notation</b></p>
<dl class="variablelist">
<dt><span class="term"><code class="computeroutput"><span class="identifier">T</span></code></span></dt>
<dd><p>
                  An arbitrary type.
                </p></dd>
<dt><span class="term"><code class="computeroutput"><span class="identifier">N</span></code></span></dt>
<dd><p>
                  An arbitrary integral constant.
                </p></dd>
<dt><span class="term"><code class="computeroutput"><span class="identifier">Char</span></code></span></dt>
<dd><p>
                  A character type.
                </p></dd>
<dt><span class="term"><code class="computeroutput"><span class="identifier">Traits</span></code></span></dt>
<dd><p>
                  A character traits type.
                </p></dd>
<dt><span class="term"><code class="computeroutput"><span class="identifier">Allocator</span></code></span></dt>
<dd><p>
                  A standard allocator type.
                </p></dd>
</dl>
</div>
<h6>
<a name="spirit.advanced.customize.string_traits.char_type_of.h5"></a>
            <span class="phrase"><a name="spirit.advanced.customize.string_traits.char_type_of.expression_semantics"></a></span><a class="link" href="char_type_of.html#spirit.advanced.customize.string_traits.char_type_of.expression_semantics">Expression
            Semantics</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Expression
                    </p>
                  </th>
<th>
                    <p>
                      Semantics
                    </p>
                  </th>
</tr></thead>
<tbody><tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">char_type_of</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">type</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      The character type of the string type <code class="computeroutput"><span class="identifier">T</span></code>.
                    </p>
                  </td>
</tr></tbody>
</table></div>
<h6>
<a name="spirit.advanced.customize.string_traits.char_type_of.h6"></a>
            <span class="phrase"><a name="spirit.advanced.customize.string_traits.char_type_of.predefined_specializations"></a></span><a class="link" href="char_type_of.html#spirit.advanced.customize.string_traits.char_type_of.predefined_specializations">Predefined
            Specializations</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Type
                    </p>
                  </th>
<th>
                    <p>
                      Semantics
                    </p>
                  </th>
</tr></thead>
<tbody>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">const</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="identifier">char_type_of</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">char</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">char</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">wchar_t</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">wchar_t</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">char</span> <span class="keyword">const</span><span class="special">*</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">char</span> <span class="keyword">const</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">wchar_t</span> <span class="keyword">const</span><span class="special">*</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">wchar_t</span> <span class="keyword">const</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">char</span><span class="special">*</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">char</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">wchar_t</span><span class="special">*</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">wchar_t</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">char</span><span class="special">[</span><span class="identifier">N</span><span class="special">]</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">char</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">wchar_t</span><span class="special">[</span><span class="identifier">N</span><span class="special">]</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">wchar_t</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">char</span> <span class="keyword">const</span><span class="special">[</span><span class="identifier">N</span><span class="special">]</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">char</span> <span class="keyword">const</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">wchar_t</span> <span class="keyword">const</span><span class="special">[</span><span class="identifier">N</span><span class="special">]</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">wchar_t</span> <span class="keyword">const</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">char</span><span class="special">(&amp;)[</span><span class="identifier">N</span><span class="special">]</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">char</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">wchar_t</span><span class="special">(&amp;)[</span><span class="identifier">N</span><span class="special">]</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">wchar_t</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">char</span> <span class="keyword">const</span><span class="special">(&amp;)[</span><span class="identifier">N</span><span class="special">]</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">char</span> <span class="keyword">const</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">wchar_t</span> <span class="keyword">const</span><span class="special">(&amp;)[</span><span class="identifier">N</span><span class="special">]</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="keyword">wchar_t</span> <span class="keyword">const</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span><span class="identifier">Char</span><span class="special">,</span>
                      <span class="identifier">Traits</span><span class="special">,</span>
                      <span class="identifier">Allocator</span><span class="special">&gt;</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Returns <code class="computeroutput"><span class="identifier">Char</span></code>.
                    </p>
                  </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="spirit.advanced.customize.string_traits.char_type_of.h7"></a>
            <span class="phrase"><a name="spirit.advanced.customize.string_traits.char_type_of.when_to_implement"></a></span><a class="link" href="char_type_of.html#spirit.advanced.customize.string_traits.char_type_of.when_to_implement">When
            to implement</a>
          </h6>
<p>
            This customization point needs to be implemented whenever <a class="link" href="../is_string.html" title="Determine if a Type Should be Treated as a String (Qi and Karma)"><code class="computeroutput"><span class="identifier">traits</span><span class="special">::</span><span class="identifier">is_string</span></code></a> is implemented.
          </p>
<h6>
<a name="spirit.advanced.customize.string_traits.char_type_of.h8"></a>
            <span class="phrase"><a name="spirit.advanced.customize.string_traits.char_type_of.related_attribute_customization_points"></a></span><a class="link" href="char_type_of.html#spirit.advanced.customize.string_traits.char_type_of.related_attribute_customization_points">Related
            Attribute Customization Points</a>
          </h6>
<p>
            If this customization point is implemented, the following other customization
            points need to be implemented as well.
          </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Name
                    </p>
                  </th>
<th>
                    <p>
                      When to implement
                    </p>
                  </th>
</tr></thead>
<tbody>
<tr>
<td>
                    <p>
                      <a class="link" href="is_char.html" title="Determine if a Type is a Character"><code class="computeroutput"><span class="identifier">traits</span><span class="special">::</span><span class="identifier">is_char</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      For string types whose underlying character type is not <code class="computeroutput"><span class="keyword">char</span></code> or <code class="computeroutput"><span class="keyword">wchar_t</span></code>,
                      <code class="computeroutput"><span class="identifier">is_char</span></code> must
                      be implemented.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="../is_string.html" title="Determine if a Type Should be Treated as a String (Qi and Karma)"><code class="computeroutput"><span class="identifier">traits</span><span class="special">::</span><span class="identifier">is_string</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      Whenever <code class="computeroutput"><span class="identifier">char_type_of</span></code>
                      is implemented.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <a class="link" href="extract_c_string.html" title="Get a C-style String from a String Type"><code class="computeroutput"><span class="identifier">traits</span><span class="special">::</span><span class="identifier">extract_c_string</span></code></a>
                    </p>
                  </td>
<td>
                    <p>
                      Whenever <code class="computeroutput"><span class="identifier">char_type_of</span></code>
                      is implemented.
                    </p>
                  </td>
</tr>
</tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2001-2011 Joel de Guzman, Hartmut Kaiser<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_char.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../string_traits.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="extract_c_string.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
