#==============================================================================
#   Copyright (c) 2021 Nikita Kniazev
#
#   Use, modification and distribution is subject to the Boost Software
#   License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
#   http://www.boost.org/LICENSE_1_0.txt)
#==============================================================================

import feature ;

if ! [ feature.valid known-warnings ] {
    feature.feature known-warnings : hide show : incidental propagated ;
}

local 9.0-14.0 = 9.0 10.0 11.0 12.0 14.0 ;

project spirit
    : requirements
        # Warnings that we have not managed to fix yet
        <known-warnings>hide,<toolset>msvc:<cxxflags>-wd4244  # conversion from 'T' to 'U', possible loss of data
        <known-warnings>hide,<toolset>msvc:<cxxflags>-wd4365  # conversion from 'T' to 'U', signed/unsigned mismatch
        <known-warnings>hide,<toolset>msvc:<cxxflags>-wd4459  # declaration of 'varname' hides global declaration

        # Enable additional useful warnings
        <warnings>extra,<toolset>gcc:<cxxflags>-Wshadow-local
        <warnings>extra,<toolset>clang-linux:<cxxflags>-Wshadow-field-in-constructor
        <warnings>extra,<toolset>clang-linux:<cxxflags>-Wshadow-uncaptured-local
        <warnings>extra,<toolset>clang-linux:<cxxflags>-Wundefined-reinterpret-cast
        <warnings>extra,<toolset>msvc:<cxxflags>-w44555  # result of expression not used

        # Broken or very high false-positive rate
        <known-warnings>hide,<toolset>gcc:<cxxflags>-Wno-maybe-uninitialized  # Too many pesky false-positives
        <known-warnings>hide,<toolset>msvc-$(9.0-14.0):<cxxflags>-wd4100  # unreferenced formal parameter
        <known-warnings>hide,<toolset>msvc-$(9.0-14.0):<cxxflags>-wd4512  # assignment operator could not be generated
        <known-warnings>hide,<toolset>msvc-$(9.0-14.0):<cxxflags>-wd4714  # function 'x' marked as __forceinline not inlined
    ;
