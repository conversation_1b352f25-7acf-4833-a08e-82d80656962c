# Maintainer

This library is currently maintained by [<PERSON>](mailto:<EMAIL>) with generous support 
from the C++ Alliance.

# Build Status

Branch  | Status
--------|-------
develop | [![CI](https://github.com/boostorg/property_tree/actions/workflows/ci.yml/badge.svg?branch=develop)](https://github.com/boostorg/property_tree/actions/workflows/ci.yml)
master  | [![CI](https://github.com/boostorg/property_tree/actions/workflows/ci.yml/badge.svg?branch=master)](https://github.com/boostorg/property_tree/actions/workflows/ci.yml)

# Licence

This software is distributed under the [Boost Software License, Version 1.0](http://www.boost.org/LICENSE_1_0.txt).

# Original Work

This library is the work of <PERSON><PERSON> and <PERSON><br/> 

Copyright (C) 2002-2006 <PERSON><PERSON><br/>
Copyright (C) 2009 <PERSON>
