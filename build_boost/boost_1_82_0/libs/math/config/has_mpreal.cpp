//  Copyright <PERSON> 2008.
//  Copyright <PERSON> 2009
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifdef _MSC_VER
#  pragma warning (disable : 4127) // conditional expression is constant
#  pragma warning (disable : 4800) // 'int' : forcing value to bool 'true' or 'false' (performance warning)
#  pragma warning (disable : 4512) // assignment operator could not be generated
#endif

#include <mpreal.h>

