<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Use in template code</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../tutorial.html" title="Tutorial">
<link rel="prev" href="non_templ.html" title="Use in non-template code">
<link rel="next" href="user_def.html" title="Use With User-Defined Types">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="non_templ.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../tutorial.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="user_def.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.tutorial.templ"></a><a class="link" href="templ.html" title="Use in template code">Use in template code</a>
</h3></div></div></div>
<p>
        When using the constants inside a function template, we need to ensure that
        we use a constant of the correct precision for our template parameters. We
        can do this by calling the function-template versions, <code class="computeroutput"><span class="identifier">pi</span><span class="special">&lt;</span><span class="identifier">FPType</span><span class="special">&gt;()</span></code>, of the constants like this:
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">constants</span><span class="special">/</span><span class="identifier">constants</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Real</span><span class="special">&gt;</span>
<span class="identifier">Real</span> <span class="identifier">area</span><span class="special">(</span><span class="identifier">Real</span> <span class="identifier">r</span><span class="special">)</span>
<span class="special">{</span>
   <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">constants</span><span class="special">;</span>
   <span class="keyword">return</span> <span class="identifier">pi</span><span class="special">&lt;</span><span class="identifier">Real</span><span class="special">&gt;()</span> <span class="special">*</span> <span class="identifier">r</span> <span class="special">*</span> <span class="identifier">r</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
        Although this syntax is a little less "cute" than the non-template
        version, the code is no less efficient (at least for the built-in types
        <code class="computeroutput"><span class="keyword">float</span></code>, <code class="computeroutput"><span class="keyword">double</span></code>
        and <code class="computeroutput"><span class="keyword">long</span> <span class="keyword">double</span></code>)
        : the function template versions of the constants are simple inline functions
        that return a constant of the correct precision for the type used. In addition,
        these functions are declared <code class="computeroutput"><span class="identifier">constexp</span></code>
        for those compilers that support this, allowing the result to be used in
        constant-expressions provided the template argument is a literal type.
      </p>
<div class="tip"><table border="0" summary="Tip">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Tip]" src="../../../../../../doc/src/images/tip.png"></td>
<th align="left">Tip</th>
</tr>
<tr><td align="left" valign="top"><p>
          Keep in mind the difference between the variable version, just <code class="computeroutput"><span class="identifier">pi</span></code>, and the template-function version:
          the template-function requires both a &lt;<em class="replaceable"><code>floating-point-type</code></em>&gt;
          and function call <code class="computeroutput"><span class="special">()</span></code> brackets,
          for example: <code class="computeroutput"><span class="identifier">pi</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;()</span></code>.
          You cannot write <code class="computeroutput"><span class="keyword">double</span> <span class="identifier">p</span>
          <span class="special">=</span> <span class="identifier">pi</span><span class="special">&lt;&gt;()</span></code>, nor <code class="computeroutput"><span class="keyword">double</span>
          <span class="identifier">p</span> <span class="special">=</span>
          <span class="identifier">pi</span><span class="special">()</span></code>.
        </p></td></tr>
</table></div>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top">
<p>
          You can always use <span class="bold"><strong>both</strong></span> variable and template-function
          versions <span class="bold"><strong>provided calls are fully qualified</strong></span>,
          for example:
        </p>
<pre class="programlisting"><span class="keyword">double</span> <span class="identifier">my_pi1</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">constants</span><span class="special">::</span><span class="identifier">pi</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;();</span>
<span class="keyword">double</span> <span class="identifier">my_pi2</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">double_constants</span><span class="special">::</span><span class="identifier">pi</span><span class="special">;</span>
</pre>
</td></tr>
</table></div>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top">
<p>
          It may be tempting to simply define
        </p>
<pre class="programlisting"><span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">double_constants</span><span class="special">;</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">constants</span><span class="special">;</span>
</pre>
<p>
          but if you do define two namespaces, this will, of course, create ambiguity!
        </p>
<pre class="programlisting"><span class="keyword">double</span> <span class="identifier">my_pi</span> <span class="special">=</span> <span class="identifier">pi</span><span class="special">();</span> <span class="comment">// error C2872: 'pi' : ambiguous symbol</span>
<span class="keyword">double</span> <span class="identifier">my_pi2</span> <span class="special">=</span> <span class="identifier">pi</span><span class="special">;</span> <span class="comment">// Context does not allow for disambiguation of overloaded function</span>
</pre>
<p>
          Although the mistake above is fairly obvious, it is also not too difficult
          to do this accidentally, or worse, create it in someone elses code.
        </p>
<p>
          Therefore is it prudent to avoid this risk by <span class="bold"><strong>localising
          the scope of such definitions</strong></span>, as shown above.
        </p>
</td></tr>
</table></div>
<div class="tip"><table border="0" summary="Tip">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Tip]" src="../../../../../../doc/src/images/tip.png"></td>
<th align="left">Tip</th>
</tr>
<tr><td align="left" valign="top">
<p>
          Be very careful with the type provided as parameter. For example, providing
          an <span class="bold"><strong>integer</strong></span> instead of a floating-point
          type can be disastrous (a C++ feature).
        </p>
<pre class="programlisting"><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Area = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">area</span><span class="special">(</span><span class="number">2</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// Area = 12!!!</span></pre>
<p>
          You should get a compiler warning
        </p>
<pre class="programlisting">warning : 'return' : conversion from 'double' to 'int', possible loss of data
</pre>
<p>
          Failure to heed this warning can lead to very wrong answers!
        </p>
<p>
          You can also avoid this by being explicit about the type of <code class="computeroutput"><span class="identifier">Area</span></code>.
        </p>
<pre class="programlisting"><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Area = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">area</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">2</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// Area = 12.566371</span></pre>
</td></tr>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="non_templ.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../tutorial.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="user_def.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
