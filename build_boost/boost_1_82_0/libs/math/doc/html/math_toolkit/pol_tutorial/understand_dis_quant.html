<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Understanding Quantiles of Discrete Distributions</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../pol_tutorial.html" title="Policy Tutorial">
<link rel="prev" href="user_def_err_pol.html" title="Calling User Defined Error Handlers">
<link rel="next" href="../pol_ref.html" title="Policy Reference">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="user_def_err_pol.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../pol_tutorial.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../pol_ref.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.pol_tutorial.understand_dis_quant"></a><a class="link" href="understand_dis_quant.html" title="Understanding Quantiles of Discrete Distributions">Understanding
      Quantiles of Discrete Distributions</a>
</h3></div></div></div>
<p>
        Discrete distributions present us with a problem when calculating the quantile:
        we are starting from a continuous real-valued variable - the probability
        - but the result (the value of the random variable) should really be discrete.
      </p>
<p>
        Consider for example a Binomial distribution, with a sample size of 50, and
        a success fraction of 0.5. There are a variety of ways we can plot a discrete
        distribution, but if we plot the PDF as a step-function then it looks something
        like this:
      </p>
<p>
        <span class="inlinemediaobject"><img src="../../../graphs/binomial_pdf.png"></span>
      </p>
<p>
        Now lets suppose that the user asks for a the quantile that corresponds to
        a probability of 0.05, if we zoom in on the CDF for that region here's what
        we see:
      </p>
<p>
        <span class="inlinemediaobject"><img src="../../../graphs/binomial_quantile_1.png"></span>
      </p>
<p>
        As can be seen there is no random variable that corresponds to a probability
        of exactly 0.05, so we're left with two choices as shown in the figure:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            We could round the result down to 18.
          </li>
<li class="listitem">
            We could round the result up to 19.
          </li>
</ul></div>
<p>
        In fact there's actually a third choice as well: we could "pretend"
        that the distribution was continuous and return a real valued result: in
        this case we would calculate a result of approximately 18.701 (this accurately
        reflects the fact that the result is nearer to 19 than 18).
      </p>
<p>
        By using policies we can offer any of the above as options, but that still
        leaves the question: <span class="emphasis"><em>What is actually the right thing to do?</em></span>
      </p>
<p>
        And in particular: <span class="emphasis"><em>What policy should we use by default?</em></span>
      </p>
<p>
        In coming to an answer we should realise that:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Calculating an integer result is often much faster than calculating a
            real-valued result: in fact in our tests it was up to 20 times faster.
          </li>
<li class="listitem">
            Normally people calculate quantiles so that they can perform a test of
            some kind: <span class="emphasis"><em>"If the random variable is less than N then
            we can reject our null-hypothesis with 90% confidence."</em></span>
          </li>
</ul></div>
<p>
        So there is a genuine benefit to calculating an integer result as well as
        it being "the right thing to do" from a philosophical point of
        view. What's more if someone asks for a quantile at 0.05, then we can normally
        assume that they are asking for <span class="emphasis"><em><span class="bold"><strong>at least</strong></span>
        95% of the probability to the right of the value chosen, and <span class="bold"><strong>no
        more than</strong></span> 5% of the probability to the left of the value chosen.</em></span>
      </p>
<p>
        In the above binomial example we would therefore round the result down to
        18.
      </p>
<p>
        The converse applies to upper-quantiles: If the probability is greater than
        0.5 we would want to round the quantile up, <span class="emphasis"><em>so that <span class="bold"><strong>at
        least</strong></span> the requested probability is to the left of the value returned,
        and <span class="bold"><strong>no more than</strong></span> 1 - the requested probability
        is to the right of the value returned.</em></span>
      </p>
<p>
        Likewise for two-sided intervals, we would round lower quantiles down, and
        upper quantiles up. This ensures that we have <span class="emphasis"><em>at least the requested
        probability in the central region</em></span> and <span class="emphasis"><em>no more than 1
        minus the requested probability in the tail areas.</em></span>
      </p>
<p>
        For example, taking our 50 sample binomial distribution with a success fraction
        of 0.5, if we wanted a two sided 90% confidence interval, then we would ask
        for the 0.05 and 0.95 quantiles with the results <span class="emphasis"><em>rounded outwards</em></span>
        so that <span class="emphasis"><em>at least 90% of the probability</em></span> is in the central
        area:
      </p>
<p>
        <span class="inlinemediaobject"><img src="../../../graphs/binomial_pdf_3.png"></span>
      </p>
<p>
        So far so good, but there is in fact a trap waiting for the unwary here:
      </p>
<pre class="programlisting"><span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binomial</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.05</span><span class="special">);</span>
</pre>
<p>
        returns 18 as the result, which is what we would expect from the graph above,
        and indeed there is no x greater than 18 for which:
      </p>
<pre class="programlisting"><span class="identifier">cdf</span><span class="special">(</span><span class="identifier">binomial</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">&lt;=</span> <span class="number">0.05</span><span class="special">;</span>
</pre>
<p>
        However:
      </p>
<pre class="programlisting"><span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binomial</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.95</span><span class="special">);</span>
</pre>
<p>
        returns 31, and indeed while there is no x less than 31 for which:
      </p>
<pre class="programlisting"><span class="identifier">cdf</span><span class="special">(</span><span class="identifier">binomial</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">&gt;=</span> <span class="number">0.95</span><span class="special">;</span>
</pre>
<p>
        We might naively expect that for this symmetrical distribution the result
        would be 32 (since 32 = 50 - 18), but we need to remember that the cdf of
        the binomial is <span class="emphasis"><em>inclusive</em></span> of the random variable. So
        while the left tail area <span class="emphasis"><em>includes</em></span> the quantile returned,
        the right tail area always excludes an upper quantile value: since that "belongs"
        to the central area.
      </p>
<p>
        Look at the graph above to see what's going on here: the lower quantile of
        18 belongs to the left tail, so any value &lt;= 18 is in the left tail. The
        upper quantile of 31 on the other hand belongs to the central area, so the
        tail area actually starts at 32, so any value &gt; 31 is in the right tail.
      </p>
<p>
        Therefore if U and L are the upper and lower quantiles respectively, then
        a random variable X is in the tail area - where we would reject the null
        hypothesis if:
      </p>
<pre class="programlisting"><span class="identifier">X</span> <span class="special">&lt;=</span> <span class="identifier">L</span> <span class="special">||</span> <span class="identifier">X</span> <span class="special">&gt;</span> <span class="identifier">U</span>
</pre>
<p>
        And the a variable X is inside the central region if:
      </p>
<pre class="programlisting"><span class="identifier">L</span> <span class="special">&lt;</span> <span class="identifier">X</span> <span class="special">&lt;=</span> <span class="identifier">U</span>
</pre>
<p>
        The moral here is to <span class="emphasis"><em>always be very careful with your comparisons
        when dealing with a discrete distribution</em></span>, and if in doubt, <span class="emphasis"><em>base
        your comparisons on CDF's instead</em></span>.
      </p>
<h5>
<a name="math_toolkit.pol_tutorial.understand_dis_quant.h0"></a>
        <span class="phrase"><a name="math_toolkit.pol_tutorial.understand_dis_quant.other_rounding_policies_are_avai"></a></span><a class="link" href="understand_dis_quant.html#math_toolkit.pol_tutorial.understand_dis_quant.other_rounding_policies_are_avai">Other
        Rounding Policies are Available</a>
      </h5>
<p>
        As you would expect from a section on policies, you won't be surprised to
        know that other rounding options are available:
      </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">integer_round_outwards</span></dt>
<dd>
<p>
              This is the default policy as described above: lower quantiles are
              rounded down (probability &lt; 0.5), and upper quantiles (probability
              &gt; 0.5) are rounded up.
            </p>
<p>
              This gives <span class="emphasis"><em>no more than</em></span> the requested probability
              in the tails, and <span class="emphasis"><em>at least</em></span> the requested probability
              in the central area.
            </p>
</dd>
<dt><span class="term">integer_round_inwards</span></dt>
<dd>
<p>
              This is the exact opposite of the default policy: lower quantiles are
              rounded up (probability &lt; 0.5), and upper quantiles (probability
              &gt; 0.5) are rounded down.
            </p>
<p>
              This gives <span class="emphasis"><em>at least</em></span> the requested probability
              in the tails, and <span class="emphasis"><em>no more than</em></span> the requested probability
              in the central area.
            </p>
</dd>
<dt><span class="term">integer_round_down</span></dt>
<dd><p>
              This policy will always round the result down no matter whether it
              is an upper or lower quantile
            </p></dd>
<dt><span class="term">integer_round_up</span></dt>
<dd><p>
              This policy will always round the result up no matter whether it is
              an upper or lower quantile
            </p></dd>
<dt><span class="term">integer_round_nearest</span></dt>
<dd><p>
              This policy will always round the result to the nearest integer no
              matter whether it is an upper or lower quantile
            </p></dd>
<dt><span class="term">real</span></dt>
<dd><p>
              This policy will return a real valued result for the quantile of a
              discrete distribution: this is generally much slower than finding an
              integer result but does allow for more sophisticated rounding policies.
            </p></dd>
</dl>
</div>
<p>
        To understand how the rounding policies for the discrete distributions can
        be used, we'll use the 50-sample binomial distribution with a success fraction
        of 0.5 once again, and calculate all the possible quantiles at 0.05 and 0.95.
      </p>
<p>
        Begin by including the needed headers (and some using statements for conciseness):
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span><span class="special">;</span> <span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">left</span><span class="special">;</span> <span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">fixed</span><span class="special">;</span> <span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">right</span><span class="special">;</span> <span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">scientific</span><span class="special">;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iomanip</span><span class="special">&gt;</span>
<span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">setw</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">setprecision</span><span class="special">;</span>

<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">distributions</span><span class="special">/</span><span class="identifier">binomial</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<p>
        Next we'll bring the needed declarations into scope, and define distribution
        types for all the available rounding policies:
      </p>
<pre class="programlisting"><span class="comment">// Avoid </span>
<span class="comment">// using namespace std; // and </span>
<span class="comment">// using namespace boost::math;</span>
<span class="comment">// to avoid potential ambiguity of names, like binomial.</span>
<span class="comment">// using namespace boost::math::policies; is small risk, but</span>
<span class="comment">// the necessary items are brought into scope thus:</span>

<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">binomial_distribution</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">policy</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">discrete_quantile</span><span class="special">;</span>

<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">integer_round_outwards</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">integer_round_down</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">integer_round_up</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">integer_round_nearest</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">integer_round_inwards</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">real</span><span class="special">;</span>

<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">binomial_distribution</span><span class="special">;</span> <span class="comment">// Not std::binomial_distribution.</span>

<span class="keyword">typedef</span> <span class="identifier">binomial_distribution</span><span class="special">&lt;</span>
            <span class="keyword">double</span><span class="special">,</span>
            <span class="identifier">policy</span><span class="special">&lt;</span><span class="identifier">discrete_quantile</span><span class="special">&lt;</span><span class="identifier">integer_round_outwards</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span>
        <span class="identifier">binom_round_outwards</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">binomial_distribution</span><span class="special">&lt;</span>
            <span class="keyword">double</span><span class="special">,</span>
            <span class="identifier">policy</span><span class="special">&lt;</span><span class="identifier">discrete_quantile</span><span class="special">&lt;</span><span class="identifier">integer_round_inwards</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span>
        <span class="identifier">binom_round_inwards</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">binomial_distribution</span><span class="special">&lt;</span>
            <span class="keyword">double</span><span class="special">,</span>
            <span class="identifier">policy</span><span class="special">&lt;</span><span class="identifier">discrete_quantile</span><span class="special">&lt;</span><span class="identifier">integer_round_down</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span>
        <span class="identifier">binom_round_down</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">binomial_distribution</span><span class="special">&lt;</span>
            <span class="keyword">double</span><span class="special">,</span>
            <span class="identifier">policy</span><span class="special">&lt;</span><span class="identifier">discrete_quantile</span><span class="special">&lt;</span><span class="identifier">integer_round_up</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span>
        <span class="identifier">binom_round_up</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">binomial_distribution</span><span class="special">&lt;</span>
            <span class="keyword">double</span><span class="special">,</span>
            <span class="identifier">policy</span><span class="special">&lt;</span><span class="identifier">discrete_quantile</span><span class="special">&lt;</span><span class="identifier">integer_round_nearest</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span>
        <span class="identifier">binom_round_nearest</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">binomial_distribution</span><span class="special">&lt;</span>
            <span class="keyword">double</span><span class="special">,</span>
            <span class="identifier">policy</span><span class="special">&lt;</span><span class="identifier">discrete_quantile</span><span class="special">&lt;</span><span class="identifier">real</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span>
        <span class="identifier">binom_real_quantile</span><span class="special">;</span>
</pre>
<p>
        Now let's set to work calling those quantiles:
      </p>
<pre class="programlisting"><span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span>
      <span class="string">"Testing rounding policies for a 50 sample binomial distribution,\n"</span>
      <span class="string">"with a success fraction of 0.5.\n\n"</span>
      <span class="string">"Lower quantiles are calculated at p = 0.05\n\n"</span>
      <span class="string">"Upper quantiles at p = 0.95.\n\n"</span><span class="special">;</span>

   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">25</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="string">"Policy"</span><span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="string">"Lower Quantile"</span> <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="string">"Upper Quantile"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

   <span class="comment">// Test integer_round_outwards:</span>
   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">25</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="string">"integer_round_outwards"</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_outwards</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.05</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_outwards</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.95</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

   <span class="comment">// Test integer_round_inwards:</span>
   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">25</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="string">"integer_round_inwards"</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_inwards</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.05</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_inwards</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.95</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

   <span class="comment">// Test integer_round_down:</span>
   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">25</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="string">"integer_round_down"</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_down</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.05</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_down</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.95</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

   <span class="comment">// Test integer_round_up:</span>
   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">25</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="string">"integer_round_up"</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_up</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.05</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_up</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.95</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

   <span class="comment">// Test integer_round_nearest:</span>
   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">25</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="string">"integer_round_nearest"</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_nearest</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.05</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_round_nearest</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.95</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

   <span class="comment">// Test real:</span>
   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">25</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="string">"real"</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_real_quantile</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.05</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">setw</span><span class="special">(</span><span class="number">18</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">right</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">binom_real_quantile</span><span class="special">(</span><span class="number">50</span><span class="special">,</span> <span class="number">0.5</span><span class="special">),</span> <span class="number">0.95</span><span class="special">)</span>
      <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
<span class="special">}</span> <span class="comment">// int main()</span>
</pre>
<p>
        Which produces the program output:
      </p>
<pre class="programlisting">  policy_eg_10.vcxproj -&gt; J:\Cpp\MathToolkit\test\Math_test\Release\policy_eg_10.exe
  Testing rounding policies for a 50 sample binomial distribution,
  with a success fraction of 0.5.

  Lower quantiles are calculated at p = 0.05

  Upper quantiles at p = 0.95.

                     Policy    Lower Quantile    Upper Quantile
     integer_round_outwards                18                31
      integer_round_inwards                19                30
         integer_round_down                18                30
           integer_round_up                19                31
      integer_round_nearest                19                30
                       real            18.701            30.299
</pre>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="user_def_err_pol.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../pol_tutorial.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../pol_ref.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
