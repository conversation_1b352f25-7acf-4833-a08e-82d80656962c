<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Spherical Hankel Functions</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../hankel.html" title="Hankel Functions">
<link rel="prev" href="cyl_hankel.html" title="Cyclic Hankel Functions">
<link rel="next" href="../airy.html" title="Airy Functions">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cyl_hankel.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../hankel.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../airy.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.hankel.sph_hankel"></a><a class="link" href="sph_hankel.html" title="Spherical Hankel Functions">Spherical Hankel Functions</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.hankel.sph_hankel.h0"></a>
        <span class="phrase"><a name="math_toolkit.hankel.sph_hankel.synopsis"></a></span><a class="link" href="sph_hankel.html#math_toolkit.hankel.sph_hankel.synopsis">Synopsis</a>
      </h5>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a><span class="special">&gt;</span> <span class="identifier">sph_hankel_1</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a><span class="special">&gt;</span> <span class="identifier">sph_hankel_1</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a><span class="special">&gt;</span> <span class="identifier">sph_hankel_2</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a><span class="special">&gt;</span> <span class="identifier">sph_hankel_2</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<h5>
<a name="math_toolkit.hankel.sph_hankel.h1"></a>
        <span class="phrase"><a name="math_toolkit.hankel.sph_hankel.description"></a></span><a class="link" href="sph_hankel.html#math_toolkit.hankel.sph_hankel.description">Description</a>
      </h5>
<p>
        The functions <a class="link" href="sph_hankel.html" title="Spherical Hankel Functions">sph_hankel_1</a>
        and <a class="link" href="sph_hankel.html" title="Spherical Hankel Functions">sph_hankel_2</a> return
        the result of the <a href="http://dlmf.nist.gov/10.47#P1" target="_top">spherical Hankel
        functions</a> of the first and second kind respectively:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/hankel4.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/hankel5.svg"></span>

        </p></blockquote></div>
<p>
        The return type of these functions is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a> when T1 and T2 are different types.
        The functions are also optimised for the relatively common case that T1 is
        an integer.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<p>
        Note that while the arguments to these functions are real values, the results
        are complex. That means that the functions can only be instantiated on types
        <code class="computeroutput"><span class="keyword">float</span></code>, <code class="computeroutput"><span class="keyword">double</span></code>
        and <code class="computeroutput"><span class="keyword">long</span> <span class="keyword">double</span></code>.
        The functions have also been extended to operate over the whole range of
        <span class="emphasis"><em>v</em></span> and <span class="emphasis"><em>x</em></span> (unlike <a class="link" href="../bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds">cyl_bessel_j</a>
        and <a class="link" href="../bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds">cyl_neumann</a>).
      </p>
<h5>
<a name="math_toolkit.hankel.sph_hankel.h2"></a>
        <span class="phrase"><a name="math_toolkit.hankel.sph_hankel.testing"></a></span><a class="link" href="sph_hankel.html#math_toolkit.hankel.sph_hankel.testing">Testing</a>
      </h5>
<p>
        There are just a few spot tests to exercise all the special case handling
        - the bulk of the testing is done on the Bessel functions upon which these
        are based.
      </p>
<h5>
<a name="math_toolkit.hankel.sph_hankel.h3"></a>
        <span class="phrase"><a name="math_toolkit.hankel.sph_hankel.accuracy"></a></span><a class="link" href="sph_hankel.html#math_toolkit.hankel.sph_hankel.accuracy">Accuracy</a>
      </h5>
<p>
        Refer to <a class="link" href="../bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds">cyl_bessel_j</a>
        and <a class="link" href="../bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds">cyl_neumann</a>.
      </p>
<h5>
<a name="math_toolkit.hankel.sph_hankel.h4"></a>
        <span class="phrase"><a name="math_toolkit.hankel.sph_hankel.implementation"></a></span><a class="link" href="sph_hankel.html#math_toolkit.hankel.sph_hankel.implementation">Implementation</a>
      </h5>
<p>
        These functions are trivially implemented in terms of <a class="link" href="cyl_hankel.html" title="Cyclic Hankel Functions">cyl_hankel_1</a>
        and <a class="link" href="cyl_hankel.html" title="Cyclic Hankel Functions">cyl_hankel_2</a>.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cyl_hankel.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../hankel.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../airy.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
