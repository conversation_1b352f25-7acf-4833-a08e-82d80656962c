<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title><PERSON><PERSON><PERSON> (and Associated) Polynomials</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../sf_poly.html" title="Polynomials">
<link rel="prev" href="legendre_stieltjes.html" title="Legendre-Stieltjes Polynomials">
<link rel="next" href="hermite.html" title="Hermite Polynomials">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="legendre_stieltjes.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../sf_poly.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="hermite.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.sf_poly.laguerre"></a><a class="link" href="laguerre.html" title="Laguerre (and Associated) Polynomials">Laguerre (and Associated)
      Polynomials</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.sf_poly.laguerre.h0"></a>
        <span class="phrase"><a name="math_toolkit.sf_poly.laguerre.synopsis"></a></span><a class="link" href="laguerre.html#math_toolkit.sf_poly.laguerre.synopsis">Synopsis</a>
      </h5>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">laguerre</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="identifier">T</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="identifier">T</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">T</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">T</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre_next</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">Ln</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">Lnm1</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre_next</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">Ln</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">Lnm1</span><span class="special">);</span>


<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<h5>
<a name="math_toolkit.sf_poly.laguerre.h1"></a>
        <span class="phrase"><a name="math_toolkit.sf_poly.laguerre.description"></a></span><a class="link" href="laguerre.html#math_toolkit.sf_poly.laguerre.description">Description</a>
      </h5>
<p>
        The return type of these functions is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a>: note than when there is a single
        template argument the result is the same type as that argument or <code class="computeroutput"><span class="keyword">double</span></code> if the template argument is an integer
        type.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="identifier">T</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="identifier">T</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<p>
        Returns the value of the Laguerre Polynomial of order <span class="emphasis"><em>n</em></span>
        at point <span class="emphasis"><em>x</em></span>:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/laguerre_0.svg"></span>

        </p></blockquote></div>
<p>
        The following graph illustrates the behaviour of the first few Laguerre Polynomials:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/laguerre.svg" align="middle"></span>

        </p></blockquote></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">T</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">T</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<p>
        Returns the Associated Laguerre polynomial of degree <span class="emphasis"><em>n</em></span>
        and order <span class="emphasis"><em>m</em></span> at point <span class="emphasis"><em>x</em></span>:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/laguerre_1.svg"></span>

        </p></blockquote></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre_next</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">Ln</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">Lnm1</span><span class="special">);</span>
</pre>
<p>
        Implements the three term recurrence relation for the Laguerre polynomials,
        this function can be used to create a sequence of values evaluated at the
        same <span class="emphasis"><em>x</em></span>, and for rising <span class="emphasis"><em>n</em></span>.
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/laguerre_2.svg"></span>

        </p></blockquote></div>
<p>
        For example we could produce a vector of the first 10 polynomial values using:
      </p>
<pre class="programlisting"><span class="keyword">double</span> <span class="identifier">x</span> <span class="special">=</span> <span class="number">0.5</span><span class="special">;</span>  <span class="comment">// Abscissa value</span>
<span class="identifier">vector</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="identifier">v</span><span class="special">;</span>
<span class="identifier">v</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">laguerre</span><span class="special">(</span><span class="number">0</span><span class="special">,</span> <span class="identifier">x</span><span class="special">)).</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">laguerre</span><span class="special">(</span><span class="number">1</span><span class="special">,</span> <span class="identifier">x</span><span class="special">));</span>
<span class="keyword">for</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span> <span class="special">=</span> <span class="number">1</span><span class="special">;</span> <span class="identifier">l</span> <span class="special">&lt;</span> <span class="number">10</span><span class="special">;</span> <span class="special">++</span><span class="identifier">l</span><span class="special">)</span>
   <span class="identifier">v</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">laguerre_next</span><span class="special">(</span><span class="identifier">l</span><span class="special">,</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">v</span><span class="special">[</span><span class="identifier">l</span><span class="special">],</span> <span class="identifier">v</span><span class="special">[</span><span class="identifier">l</span><span class="special">-</span><span class="number">1</span><span class="special">]));</span>
</pre>
<p>
        Formally the arguments are:
      </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">n</span></dt>
<dd><p>
              The degree <span class="emphasis"><em>n</em></span> of the last polynomial calculated.
            </p></dd>
<dt><span class="term">x</span></dt>
<dd><p>
              The abscissa value
            </p></dd>
<dt><span class="term">Ln</span></dt>
<dd><p>
              The value of the polynomial evaluated at degree <span class="emphasis"><em>n</em></span>.
            </p></dd>
<dt><span class="term">Lnm1</span></dt>
<dd><p>
              The value of the polynomial evaluated at degree <span class="emphasis"><em>n-1</em></span>.
            </p></dd>
</dl>
</div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">laguerre_next</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">Ln</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">Lnm1</span><span class="special">);</span>
</pre>
<p>
        Implements the three term recurrence relation for the Associated Laguerre
        polynomials, this function can be used to create a sequence of values evaluated
        at the same <span class="emphasis"><em>x</em></span>, and for rising degree <span class="emphasis"><em>n</em></span>.
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/laguerre_3.svg"></span>

        </p></blockquote></div>
<p>
        For example we could produce a vector of the first 10 polynomial values using:
      </p>
<pre class="programlisting"><span class="keyword">double</span> <span class="identifier">x</span> <span class="special">=</span> <span class="number">0.5</span><span class="special">;</span>  <span class="comment">// Abscissa value</span>
<span class="keyword">int</span> <span class="identifier">m</span> <span class="special">=</span> <span class="number">10</span><span class="special">;</span>      <span class="comment">// order</span>
<span class="identifier">vector</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="identifier">v</span><span class="special">;</span>
<span class="identifier">v</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">laguerre</span><span class="special">(</span><span class="number">0</span><span class="special">,</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">x</span><span class="special">)).</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">laguerre</span><span class="special">(</span><span class="number">1</span><span class="special">,</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">x</span><span class="special">));</span>
<span class="keyword">for</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span> <span class="special">=</span> <span class="number">1</span><span class="special">;</span> <span class="identifier">l</span> <span class="special">&lt;</span> <span class="number">10</span><span class="special">;</span> <span class="special">++</span><span class="identifier">l</span><span class="special">)</span>
   <span class="identifier">v</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">laguerre_next</span><span class="special">(</span><span class="identifier">l</span><span class="special">,</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">v</span><span class="special">[</span><span class="identifier">l</span><span class="special">],</span> <span class="identifier">v</span><span class="special">[</span><span class="identifier">l</span><span class="special">-</span><span class="number">1</span><span class="special">]));</span>
</pre>
<p>
        Formally the arguments are:
      </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">n</span></dt>
<dd><p>
              The degree of the last polynomial calculated.
            </p></dd>
<dt><span class="term">m</span></dt>
<dd><p>
              The order of the Associated Polynomial.
            </p></dd>
<dt><span class="term">x</span></dt>
<dd><p>
              The abscissa value.
            </p></dd>
<dt><span class="term">Ln</span></dt>
<dd><p>
              The value of the polynomial evaluated at degree <span class="emphasis"><em>n</em></span>.
            </p></dd>
<dt><span class="term">Lnm1</span></dt>
<dd><p>
              The value of the polynomial evaluated at degree <span class="emphasis"><em>n-1</em></span>.
            </p></dd>
</dl>
</div>
<h5>
<a name="math_toolkit.sf_poly.laguerre.h2"></a>
        <span class="phrase"><a name="math_toolkit.sf_poly.laguerre.accuracy"></a></span><a class="link" href="laguerre.html#math_toolkit.sf_poly.laguerre.accuracy">Accuracy</a>
      </h5>
<p>
        The following table shows peak errors (in units of epsilon) for various domains
        of input arguments. Note that only results for the widest floating point
        type on the system are given as narrower types have <a class="link" href="../relative_error.html#math_toolkit.relative_error.zero_error">effectively
        zero error</a>.
      </p>
<div class="table">
<a name="math_toolkit.sf_poly.laguerre.table_laguerre_n_x_"></a><p class="title"><b>Table 8.35. Error rates for laguerre(n, x)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for laguerre(n, x)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Laguerre Polynomials
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.82ε (Mean = 0.408ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 3.1e+03ε (Mean = 185ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.39e+04ε (Mean = 828ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 4.2e+03ε (Mean
                  = 251ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.39e+04ε (Mean = 828ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.1e+03ε (Mean = 185ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.sf_poly.laguerre.table_laguerre_n_m_x_"></a><p class="title"><b>Table 8.36. Error rates for laguerre(n, m, x)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for laguerre(n, m, x)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Associated Laguerre Polynomials
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.84ε (Mean = 0.0358ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 434ε (Mean = 10.7ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 167ε (Mean = 6.38ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 206ε (Mean = 6.86ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 167ε (Mean = 6.38ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 434ε (Mean = 11.1ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><p>
        Note that the worst errors occur when the degree increases, values greater
        than ~120 are very unlikely to produce sensible results, especially in the
        associated polynomial case when the order is also large. Further the relative
        errors are likely to grow arbitrarily large when the function is very close
        to a root.
      </p>
<h5>
<a name="math_toolkit.sf_poly.laguerre.h3"></a>
        <span class="phrase"><a name="math_toolkit.sf_poly.laguerre.testing"></a></span><a class="link" href="laguerre.html#math_toolkit.sf_poly.laguerre.testing">Testing</a>
      </h5>
<p>
        A mixture of spot tests of values calculated using functions.wolfram.com,
        and randomly generated test data are used: the test data was computed using
        <a href="http://shoup.net/ntl/doc/RR.txt" target="_top">NTL::RR</a> at 1000-bit
        precision.
      </p>
<h5>
<a name="math_toolkit.sf_poly.laguerre.h4"></a>
        <span class="phrase"><a name="math_toolkit.sf_poly.laguerre.implementation"></a></span><a class="link" href="laguerre.html#math_toolkit.sf_poly.laguerre.implementation">Implementation</a>
      </h5>
<p>
        These functions are implemented using the stable three term recurrence relations.
        These relations guarantee low absolute error but cannot guarantee low relative
        error near one of the roots of the polynomials.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="legendre_stieltjes.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../sf_poly.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="hermite.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
