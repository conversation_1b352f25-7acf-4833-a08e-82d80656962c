<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Riemann Zeta Function</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../zetas.html" title="Zeta Functions">
<link rel="prev" href="../zetas.html" title="Zeta Functions">
<link rel="next" href="../expint.html" title="Exponential Integrals">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../zetas.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../zetas.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../expint.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.zetas.zeta"></a><a class="link" href="zeta.html" title="Riemann Zeta Function">Riemann Zeta Function</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.zetas.zeta.h0"></a>
        <span class="phrase"><a name="math_toolkit.zetas.zeta.synopsis"></a></span><a class="link" href="zeta.html#math_toolkit.zetas.zeta.synopsis">Synopsis</a>
      </h5>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">zeta</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">zeta</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">z</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">zeta</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">z</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<p>
        The return type of these functions is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a>: the return type is <code class="computeroutput"><span class="keyword">double</span></code> if T is an integer type, and T otherwise.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<h5>
<a name="math_toolkit.zetas.zeta.h1"></a>
        <span class="phrase"><a name="math_toolkit.zetas.zeta.description"></a></span><a class="link" href="zeta.html#math_toolkit.zetas.zeta.description">Description</a>
      </h5>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">zeta</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">z</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">zeta</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">z</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<p>
        Returns the <a href="http://mathworld.wolfram.com/RiemannZetaFunction.html" target="_top">zeta
        function</a> of z:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/zeta1.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/zeta1.svg" align="middle"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/zeta2.svg" align="middle"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.zetas.zeta.h2"></a>
        <span class="phrase"><a name="math_toolkit.zetas.zeta.accuracy"></a></span><a class="link" href="zeta.html#math_toolkit.zetas.zeta.accuracy">Accuracy</a>
      </h5>
<p>
        The following table shows the peak errors (in units of epsilon) found on
        various platforms with various floating point types, along with comparisons
        to the <a href="http://www.gnu.org/software/gsl/" target="_top">GSL-1.9</a> and
        <a href="http://www.netlib.org/cephes/" target="_top">Cephes</a> libraries. Unless
        otherwise specified any floating point type that is narrower than the one
        shown will have <a class="link" href="../relative_error.html#math_toolkit.relative_error.zero_error">effectively
        zero error</a>.
      </p>
<div class="table">
<a name="math_toolkit.zetas.zeta.table_zeta"></a><p class="title"><b>Table 8.76. Error rates for zeta</b></p>
<div class="table-contents"><table class="table" summary="Error rates for zeta">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Zeta: Random values greater than 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.846ε (Mean = 0.0833ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 5.45ε (Mean = 1ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 8.69ε (Mean = 1.03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.846ε (Mean = 0.0833ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.836ε (Mean = 0.093ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Zeta: Random values less than 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.03ε (Mean = 2.93ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 538ε (Mean = 59.3ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 137ε (Mean = 13.8ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 70.1ε (Mean = 17.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.84ε (Mean = 3.12ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Zeta: Values close to and greater than 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.995ε (Mean = 0.5ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.9e+06ε (Mean = 5.11e+05ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 7.73ε (Mean = 4.07ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.995ε (Mean = 0.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.994ε (Mean = 0.421ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Zeta: Values close to and less than 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.508ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 8.53e+06ε (Mean = 1.87e+06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.991ε (Mean = 0.28ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.508ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.991ε (Mean = 0.375ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Zeta: Integer arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9ε (Mean = 3.06ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 70.3ε (Mean = 17.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 3.75ε (Mean = 1.1ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 28ε (Mean = 5.62ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9ε (Mean = 3ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
        The following error plot are based on an exhaustive search of the functions
        domain, MSVC-15.5 at <code class="computeroutput"><span class="keyword">double</span></code>
        precision, and GCC-7.1/Ubuntu for <code class="computeroutput"><span class="keyword">long</span>
        <span class="keyword">double</span></code> and <code class="computeroutput"><span class="identifier">__float128</span></code>.
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/zeta__double.svg" align="middle"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/zeta__80_bit_long_double.svg" align="middle"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/zeta____float128.svg" align="middle"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.zetas.zeta.h3"></a>
        <span class="phrase"><a name="math_toolkit.zetas.zeta.testing"></a></span><a class="link" href="zeta.html#math_toolkit.zetas.zeta.testing">Testing</a>
      </h5>
<p>
        The tests for these functions come in two parts: basic sanity checks use
        spot values calculated using <a href="http://functions.wolfram.com/webMathematica/FunctionEvaluation.jsp?name=Zeta" target="_top">Mathworld's
        online evaluator</a>, while accuracy checks use high-precision test values
        calculated at 1000-bit precision with <a href="http://shoup.net/ntl/doc/RR.txt" target="_top">NTL::RR</a>
        and this implementation. Note that the generic and type-specific versions
        of these functions use differing implementations internally, so this gives
        us reasonably independent test data. Using our test data to test other "known
        good" implementations also provides an additional sanity check.
      </p>
<h5>
<a name="math_toolkit.zetas.zeta.h4"></a>
        <span class="phrase"><a name="math_toolkit.zetas.zeta.implementation"></a></span><a class="link" href="zeta.html#math_toolkit.zetas.zeta.implementation">Implementation</a>
      </h5>
<p>
        All versions of these functions first use the usual reflection formulas to
        make their arguments positive:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/zeta3.svg"></span>

        </p></blockquote></div>
<p>
        The generic versions of these functions are implemented using the series:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/zeta6.svg"></span>

        </p></blockquote></div>
<p>
        When the significand (mantissa) size is recognised (currently for 53, 64
        and 113-bit reals, plus single-precision 24-bit handled via promotion to
        double) then a series of rational approximations <a class="link" href="../sf_implementation.html#math_toolkit.sf_implementation.rational_approximations_used">devised
        by JM</a> are used.
      </p>
<p>
        For 0 &lt; z &lt; 1 the approximating form is:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/zeta4.svg"></span>

        </p></blockquote></div>
<p>
        For a rational approximation <span class="emphasis"><em>R(1-z)</em></span> and a constant
        <span class="emphasis"><em>C</em></span>:
      </p>
<p>
        For 1 &lt; z &lt; 4 the approximating form is:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/zeta5.svg"></span>

        </p></blockquote></div>
<p>
        For a rational approximation <span class="emphasis"><em>R(n-z)</em></span> and a constant
        <span class="emphasis"><em>C</em></span> and integer <span class="emphasis"><em>n</em></span>:
      </p>
<p>
        For z &gt; 4 the approximating form is:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="serif_italic">ζ(z) = 1 + e<sup>R(z - n)</sup></span>
        </p></blockquote></div>
<p>
        For a rational approximation <span class="emphasis"><em>R(z-n)</em></span> and integer <span class="emphasis"><em>n</em></span>,
        note that the accuracy required for <span class="emphasis"><em>R(z-n)</em></span> is not full
        machine-precision, but an absolute error of: /ε<span class="emphasis"><em>R(0)</em></span>.
        This saves us quite a few digits when dealing with large <span class="emphasis"><em>z</em></span>,
        especially when ε is small.
      </p>
<p>
        Finally, there are some special cases for integer arguments, there are closed
        forms for negative or even integers:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/zeta7.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/zeta8.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/zeta9.svg"></span>

        </p></blockquote></div>
<p>
        and for positive odd integers we simply cache pre-computed values as these
        are of great benefit to some infinite series calculations.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../zetas.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../zetas.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../expint.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
