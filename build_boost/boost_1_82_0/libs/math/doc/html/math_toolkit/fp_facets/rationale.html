<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Design Rationale</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../fp_facets.html" title="Facets for Floating-Point Infinities and NaNs">
<link rel="prev" href="portability.html" title="Portability">
<link rel="next" href="../next_float.html" title="Floating-Point Representation Distance (ULP), and Finding Adjacent Floating-Point Values">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="portability.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../fp_facets.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../next_float.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.fp_facets.rationale"></a><a class="link" href="rationale.html" title="Design Rationale">Design Rationale</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            The flags are implemented as a const data member of the facet. Facets
            are reference counted, and locales can share facets. Therefore changing
            the flags of a facet would have effects that are hard to predict. An
            alternative design would be to implement the flags using <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">ios_base</span><span class="special">::</span><span class="identifier">xalloc</span></code> and <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">ios_base</span><span class="special">::</span><span class="identifier">iword</span></code>.
            Then one could safely modify the flags, and one could define manipulators
            that do so. However, for that to work with dynamically linked libraries,
            a <code class="computeroutput"><span class="special">.</span><span class="identifier">cpp</span></code>
            file would have to be added to the library. It was judged be more desirable
            to have a header-only library, than to have mutable flags and manipulators.
          </li>
<li class="listitem">
            The facet <code class="computeroutput"><span class="identifier">nonfinite_num_put</span></code>
            throws an exception when the <code class="computeroutput"><span class="identifier">trap_infinity</span></code>
            or <code class="computeroutput"><span class="identifier">trap_nan</span></code> flag is set
            and an attempt is made to format infinity or NaN. It would be better
            if the facet set the <code class="computeroutput"><span class="identifier">fail</span> <span class="identifier">bit</span></code> of the stream. However, facets
            derived from <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">num_put</span></code> do not have access to the stream
            state.
          </li>
</ul></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="portability.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../fp_facets.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../next_float.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
