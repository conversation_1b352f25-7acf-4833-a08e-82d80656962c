<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>cbrt</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../powers.html" title="Basic Functions">
<link rel="prev" href="expm1.html" title="expm1">
<link rel="next" href="sqrt1pm1.html" title="sqrt1pm1">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="expm1.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../powers.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="sqrt1pm1.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.powers.cbrt"></a><a class="link" href="cbrt.html" title="cbrt">cbrt</a>
</h3></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">cbrt</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cbrt</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cbrt</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<p>
        Returns the cubed root of x: x<sup>1/3</sup> or ∛x.
      </p>
<p>
        The return type of this function is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a>: the return is <code class="computeroutput"><span class="keyword">double</span></code>
        when <span class="emphasis"><em>x</em></span> is an integer type and T otherwise.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<p>
        Implemented using Halley iteration.
      </p>
<p>
        The following graph illustrates the behaviour of cbrt:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/cbrt.svg" align="middle"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.powers.cbrt.h0"></a>
        <span class="phrase"><a name="math_toolkit.powers.cbrt.accuracy"></a></span><a class="link" href="cbrt.html#math_toolkit.powers.cbrt.accuracy">Accuracy</a>
      </h5>
<p>
        For built in floating-point types <code class="computeroutput"><span class="identifier">cbrt</span></code>
        should have approximately 2 epsilon accuracy.
      </p>
<div class="table">
<a name="math_toolkit.powers.cbrt.table_cbrt"></a><p class="title"><b>Table 8.83. Error rates for cbrt</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cbrt">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  cbrt Function
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.34ε (Mean = 0.471ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.34ε (Mean = 0.471ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.34ε (Mean = 0.471ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.34ε (Mean = 0.471ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.34ε (Mean = 0.471ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.7ε (Mean = 0.565ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.7ε (Mean = 0.565ε))
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><h5>
<a name="math_toolkit.powers.cbrt.h1"></a>
        <span class="phrase"><a name="math_toolkit.powers.cbrt.testing"></a></span><a class="link" href="cbrt.html#math_toolkit.powers.cbrt.testing">Testing</a>
      </h5>
<p>
        A mixture of spot test sanity checks, and random high precision test values
        calculated using NTL::RR at 1000-bit precision.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="expm1.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../powers.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="sqrt1pm1.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
