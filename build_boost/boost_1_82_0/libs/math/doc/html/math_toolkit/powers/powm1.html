<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>powm1</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../powers.html" title="Basic Functions">
<link rel="prev" href="sqrt1pm1.html" title="sqrt1pm1">
<link rel="next" href="hypot.html" title="hypot">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="sqrt1pm1.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../powers.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="hypot.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.powers.powm1"></a><a class="link" href="powm1.html" title="powm1">powm1</a>
</h3></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">powm1</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<p>
        namespace boost{ namespace math{
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">powm1</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">powm1</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<p>
        Returns x<sup>y </sup> - 1.
      </p>
<p>
        The return type of this function is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a> when T1 and T2 are different types.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<p>
        There are two domains where this is useful: when <span class="emphasis"><em>y</em></span> is
        very small, or when <span class="emphasis"><em>x</em></span> is close to 1.
      </p>
<p>
        Note that for invalid input this function may raise a <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>
        or <a class="link" href="../error_handling.html#math_toolkit.error_handling.overflow_error">overflow_error</a>
        as appropriate.
      </p>
<p>
        Implemented in terms of <code class="computeroutput"><span class="identifier">expm1</span></code>.
      </p>
<p>
        The following graph illustrates the behaviour of powm1:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/powm1.svg" align="middle"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.powers.powm1.h0"></a>
        <span class="phrase"><a name="math_toolkit.powers.powm1.accuracy"></a></span><a class="link" href="powm1.html#math_toolkit.powers.powm1.accuracy">Accuracy</a>
      </h5>
<p>
        Should have approximately 2-3 epsilon accuracy.
      </p>
<div class="table">
<a name="math_toolkit.powers.powm1.table_powm1"></a><p class="title"><b>Table 8.85. Error rates for powm1</b></p>
<div class="table-contents"><table class="table" summary="Error rates for powm1">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  powm1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.06ε (Mean = 0.425ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.04ε (Mean = 0.493ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.88ε (Mean = 0.49ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.84ε (Mean = 0.486ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><h5>
<a name="math_toolkit.powers.powm1.h1"></a>
        <span class="phrase"><a name="math_toolkit.powers.powm1.testing"></a></span><a class="link" href="powm1.html#math_toolkit.powers.powm1.testing">Testing</a>
      </h5>
<p>
        A selection of random high precision test values calculated using NTL::RR
        at 1000-bit precision.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="sqrt1pm1.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../powers.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="hypot.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
