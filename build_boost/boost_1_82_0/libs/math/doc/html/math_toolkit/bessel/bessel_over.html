<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Bessel Function Overview</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../bessel.html" title="Bessel Functions">
<link rel="prev" href="../bessel.html" title="Bessel Functions">
<link rel="next" href="bessel_first.html" title="Bessel Functions of the First and Second Kinds">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../bessel.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../bessel.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="bessel_first.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.bessel.bessel_over"></a><a class="link" href="bessel_over.html" title="Bessel Function Overview">Bessel Function Overview</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.bessel.bessel_over.h0"></a>
        <span class="phrase"><a name="math_toolkit.bessel.bessel_over.ordinary_bessel_functions"></a></span><a class="link" href="bessel_over.html#math_toolkit.bessel.bessel_over.ordinary_bessel_functions">Ordinary
        Bessel Functions</a>
      </h5>
<p>
        Bessel Functions are solutions to Bessel's ordinary differential equation:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel1.svg"></span>

        </p></blockquote></div>
<p>
        where ν is the <span class="emphasis"><em>order</em></span> of the equation, and may be an arbitrary
        real or complex number, although integer orders are the most common occurrence.
      </p>
<p>
        This library supports either integer or real orders.
      </p>
<p>
        Since this is a second order differential equation, there must be two linearly
        independent solutions, the first of these is denoted J<sub>v</sub>
and known as a Bessel
        function of the first kind:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel2.svg"></span>

        </p></blockquote></div>
<p>
        This function is implemented in this library as <a class="link" href="bessel_first.html" title="Bessel Functions of the First and Second Kinds">cyl_bessel_j</a>.
      </p>
<p>
        The second solution is denoted either Y<sub>v</sub> or N<sub>v</sub>
and is known as either a Bessel
        Function of the second kind, or as a Neumann function:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel3.svg"></span>

        </p></blockquote></div>
<p>
        This function is implemented in this library as <a class="link" href="bessel_first.html" title="Bessel Functions of the First and Second Kinds">cyl_neumann</a>.
      </p>
<p>
        The Bessel functions satisfy the recurrence relations:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel4.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel5.svg"></span>

        </p></blockquote></div>
<p>
        Have the derivatives:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel6.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel7.svg"></span>

        </p></blockquote></div>
<p>
        Have the Wronskian relation:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel8.svg"></span>

        </p></blockquote></div>
<p>
        and the reflection formulae:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel9.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel10.svg"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.bessel.bessel_over.h1"></a>
        <span class="phrase"><a name="math_toolkit.bessel.bessel_over.modified_bessel_functions"></a></span><a class="link" href="bessel_over.html#math_toolkit.bessel.bessel_over.modified_bessel_functions">Modified
        Bessel Functions</a>
      </h5>
<p>
        The Bessel functions are valid for complex argument <span class="emphasis"><em>x</em></span>,
        and an important special case is the situation where <span class="emphasis"><em>x</em></span>
        is purely imaginary: giving a real valued result. In this case the functions
        are the two linearly independent solutions to the modified Bessel equation:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel1.svg"></span>

        </p></blockquote></div>
<p>
        The solutions are known as the modified Bessel functions of the first and
        second kind (or occasionally as the hyperbolic Bessel functions of the first
        and second kind). They are denoted I<sub>v</sub> and K<sub>v</sub>
respectively:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel2.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel3.svg"></span>

        </p></blockquote></div>
<p>
        These functions are implemented in this library as <a class="link" href="mbessel.html" title="Modified Bessel Functions of the First and Second Kinds">cyl_bessel_i</a>
        and <a class="link" href="mbessel.html" title="Modified Bessel Functions of the First and Second Kinds">cyl_bessel_k</a> respectively.
      </p>
<p>
        The modified Bessel functions satisfy the recurrence relations:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel4.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel5.svg"></span>

        </p></blockquote></div>
<p>
        Have the derivatives:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel6.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel7.svg"></span>

        </p></blockquote></div>
<p>
        Have the Wronskian relation:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel8.svg"></span>

        </p></blockquote></div>
<p>
        and the reflection formulae:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel9.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/mbessel10.svg"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.bessel.bessel_over.h2"></a>
        <span class="phrase"><a name="math_toolkit.bessel.bessel_over.spherical_bessel_functions"></a></span><a class="link" href="bessel_over.html#math_toolkit.bessel.bessel_over.spherical_bessel_functions">Spherical
        Bessel Functions</a>
      </h5>
<p>
        When solving the Helmholtz equation in spherical coordinates by separation
        of variables, the radial equation has the form:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/sbessel1.svg"></span>

        </p></blockquote></div>
<p>
        The two linearly independent solutions to this equation are called the spherical
        Bessel functions j<sub>n</sub> and y<sub>n</sub> and are related to the ordinary Bessel functions
        J<sub>n</sub> and Y<sub>n</sub> by:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/sbessel2.svg"></span>

        </p></blockquote></div>
<p>
        The spherical Bessel function of the second kind y<sub>n</sub>
is also known as the spherical
        Neumann function n<sub>n</sub>.
      </p>
<p>
        These functions are implemented in this library as <a class="link" href="sph_bessel.html" title="Spherical Bessel Functions of the First and Second Kinds">sph_bessel</a>
        and <a class="link" href="sph_bessel.html" title="Spherical Bessel Functions of the First and Second Kinds">sph_neumann</a>.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../bessel.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../bessel.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="bessel_first.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
