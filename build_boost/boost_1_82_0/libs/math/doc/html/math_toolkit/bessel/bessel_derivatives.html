<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Derivatives of the Bessel Functions</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../bessel.html" title="Bessel Functions">
<link rel="prev" href="sph_bessel.html" title="Spherical Bessel Functions of the First and Second Kinds">
<link rel="next" href="../hankel.html" title="Hankel Functions">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="sph_bessel.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../bessel.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../hankel.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.bessel.bessel_derivatives"></a><a class="link" href="bessel_derivatives.html" title="Derivatives of the Bessel Functions">Derivatives of
      the Bessel Functions</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.bessel.bessel_derivatives.h0"></a>
        <span class="phrase"><a name="math_toolkit.bessel.bessel_derivatives.synopsis"></a></span><a class="link" href="bessel_derivatives.html#math_toolkit.bessel.bessel_derivatives.synopsis">Synopsis</a>
      </h5>
<p>
        <code class="computeroutput"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">bessel_prime</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cyl_bessel_j_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cyl_bessel_j_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cyl_neumann_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cyl_neumann_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cyl_bessel_i_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cyl_bessel_i_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cyl_bessel_k_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">cyl_bessel_k_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">sph_bessel_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">sph_bessel_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">sph_neumann_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">sph_neumann_prime</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">v</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<h5>
<a name="math_toolkit.bessel.bessel_derivatives.h1"></a>
        <span class="phrase"><a name="math_toolkit.bessel.bessel_derivatives.description"></a></span><a class="link" href="bessel_derivatives.html#math_toolkit.bessel.bessel_derivatives.description">Description</a>
      </h5>
<p>
        These functions return the first derivative with respect to <span class="emphasis"><em>x</em></span>
        of the corresponding Bessel function.
      </p>
<p>
        The return type of these functions is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a> when T1 and T2 are different types.
        The functions are also optimised for the relatively common case that T1 is
        an integer.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<p>
        The functions return the result of <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>
        whenever the result is undefined or complex.
      </p>
<h5>
<a name="math_toolkit.bessel.bessel_derivatives.h2"></a>
        <span class="phrase"><a name="math_toolkit.bessel.bessel_derivatives.testing"></a></span><a class="link" href="bessel_derivatives.html#math_toolkit.bessel.bessel_derivatives.testing">Testing</a>
      </h5>
<p>
        There are two sets of test values: spot values calculated using <a href="http://www.wolframalpha.com/" target="_top">wolframalpha.com</a>,
        and a much larger set of tests computed using a relation to the underlying
        Bessel functions that the implementation does not use.
      </p>
<h5>
<a name="math_toolkit.bessel.bessel_derivatives.h3"></a>
        <span class="phrase"><a name="math_toolkit.bessel.bessel_derivatives.accuracy"></a></span><a class="link" href="bessel_derivatives.html#math_toolkit.bessel.bessel_derivatives.accuracy">Accuracy</a>
      </h5>
<p>
        The accuracy of these functions is broadly similar to the underlying Bessel
        functions.
      </p>
<div class="table">
<a name="math_toolkit.bessel.bessel_derivatives.table_cyl_bessel_i_prime_integer_orders_"></a><p class="title"><b>Table 8.50. Error rates for cyl_bessel_i_prime (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_i_prime (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel I'0: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.259ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.259ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.354ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'1: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.757ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.757ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.36ε (Mean = 0.782ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'n: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.31ε (Mean = 1.41ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 701ε (Mean = 212ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.61ε (Mean = 1.22ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.bessel.bessel_derivatives.table_cyl_bessel_i_prime"></a><p class="title"><b>Table 8.51. Error rates for cyl_bessel_i_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_i_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel I'0: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.259ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.259ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.354ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'1: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.757ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.757ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.36ε (Mean = 0.782ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'n: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.31ε (Mean = 1.41ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 701ε (Mean = 212ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.61ε (Mean = 1.22ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'v: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.62ε (Mean = 0.512ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.89e+03ε (Mean = 914ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.89e+03ε (Mean = 914ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.76e+03ε (Mean = 1.19e+03ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'n: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.95ε (Mean = 1.06ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 195ε (Mean = 37.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.85ε (Mean = 1.82ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'v: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.1ε (Mean = 2.93ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 336ε (Mean = 68.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14ε (Mean = 2.5ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'v: Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.6ε (Mean = 20.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.6ε (Mean = 20.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 59.5ε (Mean = 26.6ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.bessel.bessel_derivatives.table_cyl_bessel_j_prime_integer_orders_"></a><p class="title"><b>Table 8.52. Error rates for cyl_bessel_j_prime (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_j_prime (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel J0': Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.9ε (Mean = 6.82ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.9ε (Mean = 6.72ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.62ε (Mean = 2.55ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J0': Mathworld Data (Tricky cases) (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.44ε (Mean = 3.34ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.44ε (Mean = 3.31ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.67ε (Mean = 1.74ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1': Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.9ε (Mean = 3.37ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.9ε (Mean = 3.37ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.627ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1': Mathworld Data (tricky cases) (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 287ε (Mean = 129ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.88e+05ε (Mean = 2.63e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.88e+05ε (Mean = 2.63e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 288ε (Mean = 129ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel JN': Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.527ε (Mean = 0.128ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.29e+03ε (Mean = 312ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.29e+03ε (Mean = 355ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14ε (Mean = 6.13ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.bessel.bessel_derivatives.table_cyl_bessel_j_prime"></a><p class="title"><b>Table 8.53. Error rates for cyl_bessel_j_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_j_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel J0': Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.9ε (Mean = 6.82ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.9ε (Mean = 6.72ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.62ε (Mean = 2.55ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J0': Mathworld Data (Tricky cases)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.44ε (Mean = 3.34ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.44ε (Mean = 3.31ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.67ε (Mean = 1.74ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1': Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.9ε (Mean = 3.37ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.9ε (Mean = 3.37ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.627ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1': Mathworld Data (tricky cases)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 287ε (Mean = 129ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.88e+05ε (Mean = 2.63e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.88e+05ε (Mean = 2.63e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 288ε (Mean = 129ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel JN': Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.527ε (Mean = 0.128ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.29e+03ε (Mean = 312ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.29e+03ε (Mean = 355ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14ε (Mean = 6.13ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J': Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 21.5ε (Mean = 4.7ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.5ε (Mean = 9.31ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.5ε (Mean = 9.32ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 23.7ε (Mean = 8ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J': Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 989ε (Mean = 495ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 989ε (Mean = 495ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.9ε (Mean = 1.61ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel JN': Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.593ε (Mean = 0.0396ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.3ε (Mean = 1.85ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 79.4ε (Mean = 16.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.34ε (Mean = 0.999ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J': Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.885ε (Mean = 0.033ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 139ε (Mean = 6.47ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 279ε (Mean = 27.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 176ε (Mean = 9.75ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J': Random Data (Tricky large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 474ε (Mean = 62.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 474ε (Mean = 64.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 379ε (Mean = 45.4ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.bessel.bessel_derivatives.table_cyl_bessel_k_prime_integer_orders_"></a><p class="title"><b>Table 8.54. Error rates for cyl_bessel_k_prime (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_k_prime (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel K'0: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.39ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'1: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.736ε (Mean = 0.389ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.736ε (Mean = 0.389ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.761ε (Mean = 0.444ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'n: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 1.08ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 1.08ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.17ε (Mean = 1.75ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.bessel.bessel_derivatives.table_cyl_bessel_k_prime"></a><p class="title"><b>Table 8.55. Error rates for cyl_bessel_k_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_k_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel K'0: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.39ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'1: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.736ε (Mean = 0.389ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.736ε (Mean = 0.389ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.761ε (Mean = 0.444ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'n: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 1.08ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 1.08ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.17ε (Mean = 1.75ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'v: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.94ε (Mean = 2.44ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.94ε (Mean = 2.34ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.94ε (Mean = 1.47ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'v: Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 59.2ε (Mean = 42.9ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 58.7ε (Mean = 42.6ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.6ε (Mean = 11.8ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'n: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.45ε (Mean = 1.19ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.45ε (Mean = 1.19ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.67ε (Mean = 1.73ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'v: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.95ε (Mean = 1.53ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.95ε (Mean = 1.52ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.32ε (Mean = 1.65ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.bessel.bessel_derivatives.table_sph_bessel_prime"></a><p class="title"><b>Table 8.56. Error rates for sph_bessel_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for sph_bessel_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Bessel j': Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.753ε (Mean = 0.0343ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 167ε (Mean = 12ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 167ε (Mean = 33.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 307ε (Mean = 25.2ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.bessel.bessel_derivatives.table_sph_neumann_prime"></a><p class="title"><b>Table 8.57. Error rates for sph_neumann_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for sph_neumann_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  y': Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.988ε (Mean = 0.0869ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 158ε (Mean = 18.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 158ε (Mean = 20.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 296ε (Mean = 25.6ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><h5>
<a name="math_toolkit.bessel.bessel_derivatives.h4"></a>
        <span class="phrase"><a name="math_toolkit.bessel.bessel_derivatives.implementation"></a></span><a class="link" href="bessel_derivatives.html#math_toolkit.bessel.bessel_derivatives.implementation">Implementation</a>
      </h5>
<p>
        In the general case, the derivatives are calculated using the relations:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel_derivatives1.svg"></span>

        </p></blockquote></div>
<p>
        There are also a number of special cases, for large x we have:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel_derivatives4.svg"></span>

        </p></blockquote></div>
<p>
        And for small x:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/bessel_derivatives5.svg"></span>

        </p></blockquote></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="sph_bessel.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../bessel.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../hankel.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
