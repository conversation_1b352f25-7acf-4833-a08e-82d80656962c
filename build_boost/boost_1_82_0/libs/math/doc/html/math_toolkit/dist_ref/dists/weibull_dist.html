<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Weibull Distribution</title>
<link rel="stylesheet" href="../../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../dists.html" title="Distributions">
<link rel="prev" href="uniform_dist.html" title="Uniform Distribution">
<link rel="next" href="../dist_algorithms.html" title="Distribution Algorithms">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="uniform_dist.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../dists.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../dist_algorithms.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="math_toolkit.dist_ref.dists.weibull_dist"></a><a class="link" href="weibull_dist.html" title="Weibull Distribution">Weibull Distribution</a>
</h4></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">distributions</span><span class="special">/</span><span class="identifier">weibull</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">RealType</span> <span class="special">=</span> <span class="keyword">double</span><span class="special">,</span>
          <span class="keyword">class</span> <a class="link" href="../../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a>   <span class="special">=</span> <a class="link" href="../../pol_ref/pol_ref_ref.html" title="Policy Class Reference">policies::policy&lt;&gt;</a> <span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">weibull_distribution</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">weibull_distribution</span><span class="special">&lt;&gt;</span> <span class="identifier">weibull</span><span class="special">;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">RealType</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">weibull_distribution</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
   <span class="keyword">typedef</span> <span class="identifier">RealType</span> <span class="identifier">value_type</span><span class="special">;</span>
   <span class="keyword">typedef</span> <span class="identifier">Policy</span>   <span class="identifier">policy_type</span><span class="special">;</span>
   <span class="comment">// Construct:</span>
   <span class="identifier">weibull_distribution</span><span class="special">(</span><span class="identifier">RealType</span> <span class="identifier">shape</span><span class="special">,</span> <span class="identifier">RealType</span> <span class="identifier">scale</span> <span class="special">=</span> <span class="number">1</span><span class="special">)</span>
   <span class="comment">// Accessors:</span>
   <span class="identifier">RealType</span> <span class="identifier">shape</span><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
   <span class="identifier">RealType</span> <span class="identifier">scale</span><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
<span class="special">};</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<p>
          The <a href="http://en.wikipedia.org/wiki/Weibull_distribution" target="_top">Weibull
          distribution</a> is a continuous distribution with the <a href="http://en.wikipedia.org/wiki/Probability_density_function" target="_top">probability
          density function</a>:
        </p>
<div class="blockquote"><blockquote class="blockquote"><p>
            <span class="serif_italic">f(x; α, β) = (α/β) * (x / β)<sup>α - 1</sup> * e<sup>-(x/β)<sup>α</sup></sup></span>
          </p></blockquote></div>
<p>
          For shape parameter <span class="emphasis"><em>α</em></span> &gt; 0, and scale parameter
          <span class="emphasis"><em>β</em></span> &gt; 0, and <span class="emphasis"><em>x</em></span> &gt; 0.
        </p>
<p>
          The Weibull distribution is often used in the field of failure analysis;
          in particular it can mimic distributions where the failure rate varies
          over time. If the failure rate is:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              constant over time, then <span class="emphasis"><em>α</em></span> = 1, suggests that items
              are failing from random events.
            </li>
<li class="listitem">
              decreases over time, then <span class="emphasis"><em>α</em></span> &lt; 1, suggesting
              "infant mortality".
            </li>
<li class="listitem">
              increases over time, then <span class="emphasis"><em>α</em></span> &gt; 1, suggesting
              "wear out" - more likely to fail as time goes by.
            </li>
</ul></div>
<p>
          The following graph illustrates how the PDF varies with the shape parameter
          <span class="emphasis"><em>α</em></span>:
        </p>
<div class="blockquote"><blockquote class="blockquote"><p>
            <span class="inlinemediaobject"><img src="../../../../graphs/weibull_pdf1.svg" align="middle"></span>

          </p></blockquote></div>
<p>
          While this graph illustrates how the PDF varies with the scale parameter
          <span class="emphasis"><em>β</em></span>:
        </p>
<div class="blockquote"><blockquote class="blockquote"><p>
            <span class="inlinemediaobject"><img src="../../../../graphs/weibull_pdf2.svg" align="middle"></span>

          </p></blockquote></div>
<h5>
<a name="math_toolkit.dist_ref.dists.weibull_dist.h0"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.weibull_dist.related_distributions"></a></span><a class="link" href="weibull_dist.html#math_toolkit.dist_ref.dists.weibull_dist.related_distributions">Related
          distributions</a>
        </h5>
<p>
          When <span class="emphasis"><em>α</em></span> = 3, the <a href="http://en.wikipedia.org/wiki/Weibull_distribution" target="_top">Weibull
          distribution</a> appears similar to the <a href="http://en.wikipedia.org/wiki/Normal_distribution" target="_top">normal
          distribution</a>. When <span class="emphasis"><em>α</em></span> = 1, the Weibull distribution
          reduces to the <a href="http://en.wikipedia.org/wiki/Exponential_distribution" target="_top">exponential
          distribution</a>. The relationship of the types of extreme value distributions,
          of which the Weibull is but one, is discussed by <a href="http://www.worldscibooks.com/mathematics/p191.html" target="_top">Extreme
          Value Distributions, Theory and Applications Samuel Kotz &amp; Saralees
          Nadarajah</a>.
        </p>
<h5>
<a name="math_toolkit.dist_ref.dists.weibull_dist.h1"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.weibull_dist.member_functions"></a></span><a class="link" href="weibull_dist.html#math_toolkit.dist_ref.dists.weibull_dist.member_functions">Member
          Functions</a>
        </h5>
<pre class="programlisting"><span class="identifier">weibull_distribution</span><span class="special">(</span><span class="identifier">RealType</span> <span class="identifier">shape</span><span class="special">,</span> <span class="identifier">RealType</span> <span class="identifier">scale</span> <span class="special">=</span> <span class="number">1</span><span class="special">);</span>
</pre>
<p>
          Constructs a <a href="http://en.wikipedia.org/wiki/Weibull_distribution" target="_top">Weibull
          distribution</a> with shape <span class="emphasis"><em>shape</em></span> and scale <span class="emphasis"><em>scale</em></span>.
        </p>
<p>
          Requires that the <span class="emphasis"><em>shape</em></span> and <span class="emphasis"><em>scale</em></span>
          parameters are both greater than zero, otherwise calls <a class="link" href="../../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>.
        </p>
<pre class="programlisting"><span class="identifier">RealType</span> <span class="identifier">shape</span><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
</pre>
<p>
          Returns the <span class="emphasis"><em>shape</em></span> parameter of this distribution.
        </p>
<pre class="programlisting"><span class="identifier">RealType</span> <span class="identifier">scale</span><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
</pre>
<p>
          Returns the <span class="emphasis"><em>scale</em></span> parameter of this distribution.
        </p>
<h5>
<a name="math_toolkit.dist_ref.dists.weibull_dist.h2"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.weibull_dist.non_member_accessors"></a></span><a class="link" href="weibull_dist.html#math_toolkit.dist_ref.dists.weibull_dist.non_member_accessors">Non-member
          Accessors</a>
        </h5>
<p>
          All the <a class="link" href="../nmp.html" title="Non-Member Properties">usual non-member accessor
          functions</a> that are generic to all distributions are supported:
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.cdf">Cumulative Distribution Function</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.pdf">Probability Density Function</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.quantile">Quantile</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.hazard">Hazard Function</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.chf">Cumulative Hazard Function</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.mean">mean</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.median">median</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.mode">mode</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.variance">variance</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.sd">standard deviation</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.skewness">skewness</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.kurtosis">kurtosis</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.kurtosis_excess">kurtosis_excess</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.range">range</a> and <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.support">support</a>.
        </p>
<p>
          The domain of the random variable is [0, ∞].
        </p>
<h5>
<a name="math_toolkit.dist_ref.dists.weibull_dist.h3"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.weibull_dist.accuracy"></a></span><a class="link" href="weibull_dist.html#math_toolkit.dist_ref.dists.weibull_dist.accuracy">Accuracy</a>
        </h5>
<p>
          The Weibull distribution is implemented in terms of the standard library
          <code class="computeroutput"><span class="identifier">log</span></code> and <code class="computeroutput"><span class="identifier">exp</span></code>
          functions plus <a class="link" href="../../powers/expm1.html" title="expm1">expm1</a> and
          <a class="link" href="../../powers/log1p.html" title="log1p">log1p</a> and as such should
          have very low error rates.
        </p>
<h5>
<a name="math_toolkit.dist_ref.dists.weibull_dist.h4"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.weibull_dist.implementation"></a></span><a class="link" href="weibull_dist.html#math_toolkit.dist_ref.dists.weibull_dist.implementation">Implementation</a>
        </h5>
<p>
          In the following table <span class="emphasis"><em>α</em></span> is the shape parameter of
          the distribution, <span class="emphasis"><em>β</em></span> is its scale parameter, <span class="emphasis"><em>x</em></span>
          is the random variate, <span class="emphasis"><em>p</em></span> is the probability and <span class="emphasis"><em>q
          = 1-p</em></span>.
        </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Function
                  </p>
                </th>
<th>
                  <p>
                    Implementation Notes
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    pdf
                  </p>
                </td>
<td>
                  <p>
                    Using the relation: pdf = αβ<sup>-α </sup>x<sup>α - 1</sup> e<sup>-(x/beta)<sup>alpha</sup></sup>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    cdf
                  </p>
                </td>
<td>
                  <p>
                    Using the relation: p = -<a class="link" href="../../powers/expm1.html" title="expm1">expm1</a>(-(x/β)<sup>α</sup>)
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    cdf complement
                  </p>
                </td>
<td>
                  <p>
                    Using the relation: q = e<sup>-(x/β)<sup>α</sup></sup>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    quantile
                  </p>
                </td>
<td>
                  <p>
                    Using the relation: x = β * (-<a class="link" href="../../powers/log1p.html" title="log1p">log1p</a>(-p))<sup>1/α</sup>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    quantile from the complement
                  </p>
                </td>
<td>
                  <p>
                    Using the relation: x = β * (-log(q))<sup>1/α</sup>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    mean
                  </p>
                </td>
<td>
                  <p>
                    β * Γ(1 + 1/α)
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    variance
                  </p>
                </td>
<td>
                  <p>
                    β<sup>2</sup>(Γ(1 + 2/α) - Γ<sup>2</sup>(1 + 1/α))
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    mode
                  </p>
                </td>
<td>
                  <p>
                    β((α - 1) / α)<sup>1/α</sup>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    skewness
                  </p>
                </td>
<td>
                  <p>
                    Refer to <a href="http://mathworld.wolfram.com/WeibullDistribution.html" target="_top">Weisstein,
                    Eric W. "Weibull Distribution." From MathWorld--A Wolfram
                    Web Resource.</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    kurtosis
                  </p>
                </td>
<td>
                  <p>
                    Refer to <a href="http://mathworld.wolfram.com/WeibullDistribution.html" target="_top">Weisstein,
                    Eric W. "Weibull Distribution." From MathWorld--A Wolfram
                    Web Resource.</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    kurtosis excess
                  </p>
                </td>
<td>
                  <p>
                    Refer to <a href="http://mathworld.wolfram.com/WeibullDistribution.html" target="_top">Weisstein,
                    Eric W. "Weibull Distribution." From MathWorld--A Wolfram
                    Web Resource.</a>
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<h5>
<a name="math_toolkit.dist_ref.dists.weibull_dist.h5"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.weibull_dist.references"></a></span><a class="link" href="weibull_dist.html#math_toolkit.dist_ref.dists.weibull_dist.references">References</a>
        </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <a href="http://en.wikipedia.org/wiki/Weibull_distribution" target="_top">http://en.wikipedia.org/wiki/Weibull_distribution</a>
            </li>
<li class="listitem">
              <a href="http://mathworld.wolfram.com/WeibullDistribution.html" target="_top">Weisstein,
              Eric W. "Weibull Distribution." From MathWorld--A Wolfram
              Web Resource.</a>
            </li>
<li class="listitem">
              <a href="http://www.itl.nist.gov/div898/handbook/eda/section3/eda3668.htm" target="_top">Weibull
              in NIST Exploratory Data Analysis</a>
            </li>
</ul></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="uniform_dist.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../dists.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../dist_algorithms.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
