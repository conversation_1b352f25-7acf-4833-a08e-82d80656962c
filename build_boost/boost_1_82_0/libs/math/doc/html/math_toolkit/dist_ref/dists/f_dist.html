<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>F Distribution</title>
<link rel="stylesheet" href="../../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../dists.html" title="Distributions">
<link rel="prev" href="extreme_dist.html" title="Extreme Value Distribution">
<link rel="next" href="gamma_dist.html" title="Gamma (and Erlang) Distribution">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="extreme_dist.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../dists.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="gamma_dist.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="math_toolkit.dist_ref.dists.f_dist"></a><a class="link" href="f_dist.html" title="F Distribution">F Distribution</a>
</h4></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">distributions</span><span class="special">/</span><span class="identifier">fisher_f</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">RealType</span> <span class="special">=</span> <span class="keyword">double</span><span class="special">,</span>
          <span class="keyword">class</span> <a class="link" href="../../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a>   <span class="special">=</span> <a class="link" href="../../pol_ref/pol_ref_ref.html" title="Policy Class Reference">policies::policy&lt;&gt;</a> <span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">fisher_f_distribution</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">fisher_f_distribution</span><span class="special">&lt;&gt;</span> <span class="identifier">fisher_f</span><span class="special">;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">RealType</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">fisher_f_distribution</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
   <span class="keyword">typedef</span> <span class="identifier">RealType</span> <span class="identifier">value_type</span><span class="special">;</span>

   <span class="comment">// Construct:</span>
   <span class="identifier">fisher_f_distribution</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">RealType</span><span class="special">&amp;</span> <span class="identifier">i</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">RealType</span><span class="special">&amp;</span> <span class="identifier">j</span><span class="special">);</span>

   <span class="comment">// Accessors:</span>
   <span class="identifier">RealType</span> <span class="identifier">degrees_of_freedom1</span><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
   <span class="identifier">RealType</span> <span class="identifier">degrees_of_freedom2</span><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
<span class="special">};</span>

<span class="special">}}</span> <span class="comment">//namespaces</span>
</pre>
<p>
          The F distribution is a continuous distribution that arises when testing
          whether two samples have the same variance. If χ<sup>2</sup><sub>m</sub> and χ<sup>2</sup><sub>n</sub> are independent
          variates each distributed as Chi-Squared with <span class="emphasis"><em>m</em></span> and
          <span class="emphasis"><em>n</em></span> degrees of freedom, then the test statistic:
        </p>
<div class="blockquote"><blockquote class="blockquote"><p>
            <span class="serif_italic">F<sub>n,m</sub> = (χ<sup>2</sup><sub>n</sub> / n) / (χ<sup>2</sup><sub>m</sub> / m)</span>
          </p></blockquote></div>
<p>
          Is distributed over the range [0, ∞] with an F distribution, and has the
          PDF:
        </p>
<div class="blockquote"><blockquote class="blockquote"><p>
            <span class="inlinemediaobject"><img src="../../../../equations/fisher_pdf.svg"></span>

          </p></blockquote></div>
<p>
          The following graph illustrates how the PDF varies depending on the two
          degrees of freedom parameters.
        </p>
<div class="blockquote"><blockquote class="blockquote"><p>
            <span class="inlinemediaobject"><img src="../../../../graphs/fisher_f_pdf.svg" align="middle"></span>

          </p></blockquote></div>
<h5>
<a name="math_toolkit.dist_ref.dists.f_dist.h0"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.f_dist.member_functions"></a></span><a class="link" href="f_dist.html#math_toolkit.dist_ref.dists.f_dist.member_functions">Member Functions</a>
        </h5>
<pre class="programlisting"><span class="identifier">fisher_f_distribution</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">RealType</span><span class="special">&amp;</span> <span class="identifier">df1</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">RealType</span><span class="special">&amp;</span> <span class="identifier">df2</span><span class="special">);</span>
</pre>
<p>
          Constructs an F-distribution with numerator degrees of freedom <span class="emphasis"><em>df1</em></span>
          and denominator degrees of freedom <span class="emphasis"><em>df2</em></span>.
        </p>
<p>
          Requires that <span class="emphasis"><em>df1</em></span> and <span class="emphasis"><em>df2</em></span> are
          both greater than zero, otherwise <a class="link" href="../../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>
          is called.
        </p>
<pre class="programlisting"><span class="identifier">RealType</span> <span class="identifier">degrees_of_freedom1</span><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
</pre>
<p>
          Returns the numerator degrees of freedom parameter of the distribution.
        </p>
<pre class="programlisting"><span class="identifier">RealType</span> <span class="identifier">degrees_of_freedom2</span><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
</pre>
<p>
          Returns the denominator degrees of freedom parameter of the distribution.
        </p>
<h5>
<a name="math_toolkit.dist_ref.dists.f_dist.h1"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.f_dist.non_member_accessors"></a></span><a class="link" href="f_dist.html#math_toolkit.dist_ref.dists.f_dist.non_member_accessors">Non-member
          Accessors</a>
        </h5>
<p>
          All the <a class="link" href="../nmp.html" title="Non-Member Properties">usual non-member accessor
          functions</a> that are generic to all distributions are supported:
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.cdf">Cumulative Distribution Function</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.pdf">Probability Density Function</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.quantile">Quantile</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.hazard">Hazard Function</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.chf">Cumulative Hazard Function</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.mean">mean</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.median">median</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.mode">mode</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.variance">variance</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.sd">standard deviation</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.skewness">skewness</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.kurtosis">kurtosis</a>, <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.kurtosis_excess">kurtosis_excess</a>,
          <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.range">range</a> and <a class="link" href="../nmp.html#math_toolkit.dist_ref.nmp.support">support</a>.
        </p>
<p>
          The domain of the random variable is [0, +∞].
        </p>
<h5>
<a name="math_toolkit.dist_ref.dists.f_dist.h2"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.f_dist.examples"></a></span><a class="link" href="f_dist.html#math_toolkit.dist_ref.dists.f_dist.examples">Examples</a>
        </h5>
<p>
          Various <a class="link" href="../../stat_tut/weg/f_eg.html" title="F Distribution Examples">worked examples</a>
          are available illustrating the use of the F Distribution.
        </p>
<h5>
<a name="math_toolkit.dist_ref.dists.f_dist.h3"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.f_dist.accuracy"></a></span><a class="link" href="f_dist.html#math_toolkit.dist_ref.dists.f_dist.accuracy">Accuracy</a>
        </h5>
<p>
          The F distribution is implemented in terms of the <a class="link" href="../../sf_beta/ibeta_function.html" title="Incomplete Beta Functions">incomplete
          beta function</a> and its <a class="link" href="../../sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses">inverses</a>,
          refer to those functions for accuracy data.
        </p>
<h5>
<a name="math_toolkit.dist_ref.dists.f_dist.h4"></a>
          <span class="phrase"><a name="math_toolkit.dist_ref.dists.f_dist.implementation"></a></span><a class="link" href="f_dist.html#math_toolkit.dist_ref.dists.f_dist.implementation">Implementation</a>
        </h5>
<p>
          In the following table <span class="emphasis"><em>v1</em></span> and <span class="emphasis"><em>v2</em></span>
          are the first and second degrees of freedom parameters of the distribution,
          <span class="emphasis"><em>x</em></span> is the random variate, <span class="emphasis"><em>p</em></span> is
          the probability, and <span class="emphasis"><em>q = 1-p</em></span>.
        </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Function
                  </p>
                </th>
<th>
                  <p>
                    Implementation Notes
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    pdf
                  </p>
                </td>
<td>
                  <p>
                    The usual form of the PDF is given by:
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="inlinemediaobject"><img src="../../../../equations/fisher_pdf.svg"></span>

                    </p></blockquote></div>
                  <p>
                    However, that form is hard to evaluate directly without incurring
                    problems with either accuracy or numeric overflow.
                  </p>
                  <p>
                    Direct differentiation of the CDF expressed in terms of the incomplete
                    beta function
                  </p>
                  <p>
                    led to the following two formulas:
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">f<sub>v1,v2</sub>(x) = y * <a class="link" href="../../sf_beta/beta_derivative.html" title="Derivative of the Incomplete Beta Function">ibeta_derivative</a>(v2
                      / 2, v1 / 2, v2 / (v2 + v1 * x))</span>
                    </p></blockquote></div>
                  <p>
                    with y = (v2 * v1) / ((v2 + v1 * x) * (v2 + v1 * x))
                  </p>
                  <p>
                    and
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">f<sub>v1,v2</sub>(x) = y * <a class="link" href="../../sf_beta/beta_derivative.html" title="Derivative of the Incomplete Beta Function">ibeta_derivative</a>(v1
                      / 2, v2 / 2, v1 * x / (v2 + v1 * x))</span>
                    </p></blockquote></div>
                  <p>
                    with y = (z * v1 - x * v1 * v1) / z<sup>2</sup>
                  </p>
                  <p>
                    and z = v2 + v1 * x
                  </p>
                  <p>
                    The first of these is used for v1 * x &gt; v2, otherwise the
                    second is used.
                  </p>
                  <p>
                    The aim is to keep the <span class="emphasis"><em>x</em></span> argument to <a class="link" href="../../sf_beta/beta_derivative.html" title="Derivative of the Incomplete Beta Function">ibeta_derivative</a>
                    away from 1 to avoid rounding error.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    cdf
                  </p>
                </td>
<td>
                  <p>
                    Using the relations:
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">p = <a class="link" href="../../sf_beta/ibeta_function.html" title="Incomplete Beta Functions">ibeta</a>(v1
                      / 2, v2 / 2, v1 * x / (v2 + v1 * x))</span>
                    </p></blockquote></div>
                  <p>
                    and
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">:p = <a class="link" href="../../sf_beta/ibeta_function.html" title="Incomplete Beta Functions">ibetac</a>(v2
                      / 2, v1 / 2, v2 / (v2 + v1 * x))</span>
                    </p></blockquote></div>
                  <p>
                    The first is used for v1 * x &gt; v2, otherwise the second is
                    used.
                  </p>
                  <p>
                    The aim is to keep the <span class="emphasis"><em>x</em></span> argument to <a class="link" href="../../sf_beta/ibeta_function.html" title="Incomplete Beta Functions">ibeta</a> well
                    away from 1 to avoid rounding error.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    cdf complement
                  </p>
                </td>
<td>
                  <p>
                    Using the relations:
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">p = <a class="link" href="../../sf_beta/ibeta_function.html" title="Incomplete Beta Functions">ibetac</a>(v1
                      / 2, v2 / 2, v1 * x / (v2 + v1 * x))</span>
                    </p></blockquote></div>
                  <p>
                    and
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">p = <a class="link" href="../../sf_beta/ibeta_function.html" title="Incomplete Beta Functions">ibeta</a>(v2
                      / 2, v1 / 2, v2 / (v2 + v1 * x))</span>
                    </p></blockquote></div>
                  <p>
                    The first is used for v1 * x &lt; v2, otherwise the second is
                    used.
                  </p>
                  <p>
                    The aim is to keep the <span class="emphasis"><em>x</em></span> argument to <a class="link" href="../../sf_beta/ibeta_function.html" title="Incomplete Beta Functions">ibeta</a> well
                    away from 1 to avoid rounding error.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    quantile
                  </p>
                </td>
<td>
                  <p>
                    Using the relation:
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">x = v2 * a / (v1 * b)</span>
                    </p></blockquote></div>
                  <p>
                    where:
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">a = <a class="link" href="../../sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses">ibeta_inv</a>(v1
                      / 2, v2 / 2, p)</span>
                    </p></blockquote></div>
                  <p>
                    and
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">b = 1 - a</span>
                    </p></blockquote></div>
                  <p>
                    Quantities <span class="emphasis"><em>a</em></span> and <span class="emphasis"><em>b</em></span>
                    are both computed by <a class="link" href="../../sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses">ibeta_inv</a>
                    without the subtraction implied above.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    quantile
                  </p>
                  <p>
                    from the complement
                  </p>
                </td>
<td>
                  <p>
                    Using the relation:
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">x = v2 * a / (v1 * b)</span>
                    </p></blockquote></div>
                  <p>
                    where
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">a = <a class="link" href="../../sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses">ibetac_inv</a>(v1
                      / 2, v2 / 2, p)</span>
                    </p></blockquote></div>
                  <p>
                    and
                  </p>
                  <div class="blockquote"><blockquote class="blockquote"><p>
                      <span class="serif_italic">b = 1 - a</span>
                    </p></blockquote></div>
                  <p>
                    Quantities <span class="emphasis"><em>a</em></span> and <span class="emphasis"><em>b</em></span>
                    are both computed by <a class="link" href="../../sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses">ibetac_inv</a>
                    without the subtraction implied above.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    mean
                  </p>
                </td>
<td>
                  <p>
                    v2 / (v2 - 2)
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    variance
                  </p>
                </td>
<td>
                  <p>
                    2 * v2<sup>2 </sup> * (v1 + v2 - 2) / (v1 * (v2 - 2) * (v2 - 2) * (v2 - 4))
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    mode
                  </p>
                </td>
<td>
                  <p>
                    v2 * (v1 - 2) / (v1 * (v2 + 2))
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    skewness
                  </p>
                </td>
<td>
                  <p>
                    2 * (v2 + 2 * v1 - 2) * sqrt((2 * v2 - 8) / (v1 * (v2 + v1 -
                    2))) / (v2 - 6)
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    kurtosis and kurtosis excess
                  </p>
                </td>
<td>
                  <p>
                    Refer to, <a href="http://mathworld.wolfram.com/F-Distribution.html" target="_top">Weisstein,
                    Eric W. "F-Distribution." From MathWorld--A Wolfram
                    Web Resource.</a>
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="extreme_dist.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../dists.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="gamma_dist.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
