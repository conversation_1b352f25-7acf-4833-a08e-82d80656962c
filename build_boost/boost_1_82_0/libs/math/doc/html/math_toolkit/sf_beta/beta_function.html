<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Beta</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../sf_beta.html" title="Beta Functions">
<link rel="prev" href="../sf_beta.html" title="Beta Functions">
<link rel="next" href="ibeta_function.html" title="Incomplete Beta Functions">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../sf_beta.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../sf_beta.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ibeta_function.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.sf_beta.beta_function"></a><a class="link" href="beta_function.html" title="Beta">Beta</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.sf_beta.beta_function.h0"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.beta_function.synopsis"></a></span><a class="link" href="beta_function.html#math_toolkit.sf_beta.beta_function.synopsis">Synopsis</a>
      </h5>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">beta</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">beta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">beta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<h5>
<a name="math_toolkit.sf_beta.beta_function.h1"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.beta_function.description"></a></span><a class="link" href="beta_function.html#math_toolkit.sf_beta.beta_function.description">Description</a>
      </h5>
<p>
        The beta function is defined by:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/beta1.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/beta.svg" align="middle"></span>

        </p></blockquote></div>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<p>
        The return type of these functions is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a> when T1 and T2 are different types.
      </p>
<h5>
<a name="math_toolkit.sf_beta.beta_function.h2"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.beta_function.accuracy"></a></span><a class="link" href="beta_function.html#math_toolkit.sf_beta.beta_function.accuracy">Accuracy</a>
      </h5>
<p>
        The following table shows peak errors for various domains of input arguments,
        along with comparisons to the <a href="http://www.gnu.org/software/gsl/" target="_top">GSL-1.9</a>
        and <a href="http://www.netlib.org/cephes/" target="_top">Cephes</a> libraries.
        Note that only results for the widest floating point type on the system are
        given as narrower types have <a class="link" href="../relative_error.html#math_toolkit.relative_error.zero_error">effectively
        zero error</a>.
      </p>
<div class="table">
<a name="math_toolkit.sf_beta.beta_function.table_beta"></a><p class="title"><b>Table 8.17. Error rates for beta</b></p>
<div class="table-contents"><table class="table" summary="Error rates for beta">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> <span class="red">Max = +INFε (Mean = +INFε) <a class="link" href="../logs_and_tables/logs.html#errors_GNU_C_version_7_1_0_linux_double_beta_GSL_2_1_Beta_Function_Small_Values">And
                  other failures.</a>)</span><br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 1.14ε (Mean = 0.574ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.86ε (Mean = 1.22ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 364ε (Mean = 76.2ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.86ε (Mean = 1.22ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.23ε (Mean = 1.14ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.978ε (Mean = 0.0595ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.18e+03ε (Mean = 238ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 1.09e+03ε (Mean = 265ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 61.4ε (Mean = 19.4ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.07e+03ε (Mean = 264ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 107ε (Mean = 24.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 96.5ε (Mean = 22.4ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Beta Function: Divergent Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 12.1ε (Mean = 1.99ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 176ε (Mean = 28ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.99ε (Mean = 2.44ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 128ε (Mean = 23.8ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.8ε (Mean = 2.71ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.4ε (Mean = 2.19ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
        Note that the worst errors occur when a or b are large, and that when this
        is the case the result is very close to zero, so absolute errors will be
        very small.
      </p>
<h5>
<a name="math_toolkit.sf_beta.beta_function.h3"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.beta_function.testing"></a></span><a class="link" href="beta_function.html#math_toolkit.sf_beta.beta_function.testing">Testing</a>
      </h5>
<p>
        A mixture of spot tests of exact values, and randomly generated test data
        are used: the test data was computed using <a href="http://shoup.net/ntl/doc/RR.txt" target="_top">NTL::RR</a>
        at 1000-bit precision.
      </p>
<h5>
<a name="math_toolkit.sf_beta.beta_function.h4"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.beta_function.implementation"></a></span><a class="link" href="beta_function.html#math_toolkit.sf_beta.beta_function.implementation">Implementation</a>
      </h5>
<p>
        Traditional methods of evaluating the beta function either involve evaluating
        the gamma functions directly, or taking logarithms and then exponentiating
        the result. However, the former is prone to overflows for even very modest
        arguments, while the latter is prone to cancellation errors. As an alternative,
        if we regard the gamma function as a white-box containing the <a class="link" href="../lanczos.html" title="The Lanczos Approximation">Lanczos
        approximation</a>, then we can combine the power terms:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/beta2.svg"></span>

        </p></blockquote></div>
<p>
        which is almost the ideal solution, however almost all of the error occurs
        in evaluating the power terms when <span class="emphasis"><em>a</em></span> or <span class="emphasis"><em>b</em></span>
        are large. If we assume that <span class="emphasis"><em>a &gt; b</em></span> then the larger
        of the two power terms can be reduced by a factor of <span class="emphasis"><em>b</em></span>,
        which immediately cuts the maximum error in half:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/beta3.svg"></span>

        </p></blockquote></div>
<p>
        This may not be the final solution, but it is very competitive compared to
        other implementation methods.
      </p>
<p>
        The generic implementation - where no <a class="link" href="../lanczos.html" title="The Lanczos Approximation">Lanczos
        approximation</a> approximation is available - is implemented in a very
        similar way to the generic version of the gamma function by means of Sterling's
        approximation. Again in order to avoid numerical overflow the power terms
        that prefix the series are collected together
      </p>
<p>
        There are a few special cases worth mentioning:
      </p>
<p>
        When <span class="emphasis"><em>a</em></span> or <span class="emphasis"><em>b</em></span> are less than one,
        we can use the recurrence relations:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/beta4.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/beta5.svg"></span>

        </p></blockquote></div>
<p>
        to move to a more favorable region where they are both greater than 1.
      </p>
<p>
        In addition:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/beta7.svg"></span>

        </p></blockquote></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../sf_beta.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../sf_beta.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ibeta_function.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
