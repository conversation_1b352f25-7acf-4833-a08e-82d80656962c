<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Error <PERSON>ling Policies</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../pol_ref.html" title="Policy Reference">
<link rel="prev" href="../pol_ref.html" title="Policy Reference">
<link rel="next" href="internal_promotion.html" title="Internal Floating-point Promotion Policies">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../pol_ref.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../pol_ref.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="internal_promotion.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.pol_ref.error_handling_policies"></a><a class="link" href="error_handling_policies.html" title="Error Handling Policies">Error Handling
      Policies</a>
</h3></div></div></div>
<p>
        There are two orthogonal aspects to error handling:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            What to do (if anything) with the error.
          </li>
<li class="listitem">
            What kind of error is being raised.
          </li>
</ul></div>
<h5>
<a name="math_toolkit.pol_ref.error_handling_policies.h0"></a>
        <span class="phrase"><a name="math_toolkit.pol_ref.error_handling_policies.available_actions_when_an_error_"></a></span><a class="link" href="error_handling_policies.html#math_toolkit.pol_ref.error_handling_policies.available_actions_when_an_error_">Available
        Actions When an Error is Raised</a>
      </h5>
<p>
        What to do with the error is encapsulated by an enumerated type:
      </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">policies</span> <span class="special">{</span>

<span class="keyword">enum</span> <span class="identifier">error_policy_type</span>
<span class="special">{</span>
   <span class="identifier">throw_on_error</span> <span class="special">=</span> <span class="number">0</span><span class="special">,</span> <span class="comment">// throw an exception.</span>
   <span class="identifier">errno_on_error</span> <span class="special">=</span> <span class="number">1</span><span class="special">,</span> <span class="comment">// set ::errno &amp; return 0, NaN, infinity or best guess.</span>
   <span class="identifier">ignore_error</span> <span class="special">=</span> <span class="number">2</span><span class="special">,</span> <span class="comment">// return 0, NaN, infinity or best guess.</span>
   <span class="identifier">user_error</span> <span class="special">=</span> <span class="number">3</span>  <span class="comment">// call a user-defined error handler.</span>
<span class="special">};</span>

<span class="special">}}}</span> <span class="comment">// namespaces</span>
</pre>
<p>
        The various enumerated values have the following meanings:
      </p>
<h6>
<a name="math_toolkit.pol_ref.error_handling_policies.h1"></a>
        <span class="phrase"><a name="math_toolkit.pol_ref.error_handling_policies.throw_on_error"></a></span><a class="link" href="error_handling_policies.html#math_toolkit.pol_ref.error_handling_policies.throw_on_error">throw_on_error</a>
      </h6>
<p>
        Will throw one of the following exceptions, depending upon the type of the
        error:
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Error Type
                </p>
              </th>
<th>
                <p>
                  Exception
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Domain Error
                </p>
              </td>
<td>
                <p>
                  std::domain_error
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Pole Error
                </p>
              </td>
<td>
                <p>
                  std::domain_error
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Overflow Error
                </p>
              </td>
<td>
                <p>
                  std::overflow_error
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Underflow Error
                </p>
              </td>
<td>
                <p>
                  std::underflow_error
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Denorm Error
                </p>
              </td>
<td>
                <p>
                  std::underflow_error
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Evaluation Error
                </p>
              </td>
<td>
                <p>
                  boost::math::evaluation_error
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Indeterminate Result Error
                </p>
              </td>
<td>
                <p>
                  std::domain_error
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="math_toolkit.pol_ref.error_handling_policies.h2"></a>
        <span class="phrase"><a name="math_toolkit.pol_ref.error_handling_policies.errno_on_error"></a></span><a class="link" href="error_handling_policies.html#math_toolkit.pol_ref.error_handling_policies.errno_on_error">errno_on_error</a>
      </h6>
<p>
        Will set global <a href="http://en.wikipedia.org/wiki/Errno" target="_top"><code class="computeroutput"><span class="special">::</span><span class="identifier">errno</span></code></a>
        <code class="computeroutput"><span class="special">::</span><span class="identifier">errno</span></code>
        to one of the following values depending upon the error type (often EDOM
        = 33 and ERANGE = 34), and then return the same value as if the error had
        been ignored:
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Error Type
                </p>
              </th>
<th>
                <p>
                  errno value
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Domain Error
                </p>
              </td>
<td>
                <p>
                  EDOM
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Pole Error
                </p>
              </td>
<td>
                <p>
                  EDOM
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Overflow Error
                </p>
              </td>
<td>
                <p>
                  ERANGE
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Underflow Error
                </p>
              </td>
<td>
                <p>
                  ERANGE
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Denorm Error
                </p>
              </td>
<td>
                <p>
                  ERANGE
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Evaluation Error
                </p>
              </td>
<td>
                <p>
                  EDOM
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Indeterminate Result Error
                </p>
              </td>
<td>
                <p>
                  EDOM
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="math_toolkit.pol_ref.error_handling_policies.h3"></a>
        <span class="phrase"><a name="math_toolkit.pol_ref.error_handling_policies.ignore_error"></a></span><a class="link" href="error_handling_policies.html#math_toolkit.pol_ref.error_handling_policies.ignore_error">ignore_error</a>
      </h6>
<p>
        Will return one of the values below depending on the error type (<code class="computeroutput"><span class="special">::</span><span class="identifier">errno</span></code>
        is NOT changed)::
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Error Type
                </p>
              </th>
<th>
                <p>
                  Returned Value
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Domain Error
                </p>
              </td>
<td>
                <p>
                  std::numeric_limits&lt;T&gt;::quiet_NaN()
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Pole Error
                </p>
              </td>
<td>
                <p>
                  std::numeric_limits&lt;T&gt;::quiet_NaN()
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Overflow Error
                </p>
              </td>
<td>
                <p>
                  std::numeric_limits&lt;T&gt;::infinity()
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Underflow Error
                </p>
              </td>
<td>
                <p>
                  0
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Denorm Error
                </p>
              </td>
<td>
                <p>
                  The denormalised value.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Evaluation Error
                </p>
              </td>
<td>
                <p>
                  The best guess (perhaps NaN) as to the result: which may be significantly
                  in error.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Indeterminate Result Error
                </p>
              </td>
<td>
                <p>
                  Depends on the function where the error occurred
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="math_toolkit.pol_ref.error_handling_policies.h4"></a>
        <span class="phrase"><a name="math_toolkit.pol_ref.error_handling_policies.user_error"></a></span><a class="link" href="error_handling_policies.html#math_toolkit.pol_ref.error_handling_policies.user_error">user_error</a>
      </h6>
<p>
        Will call a user defined error handler: these are forward declared in boost/math/policies/error_handling.hpp,
        but the actual definitions must be provided by the user:
      </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">policies</span><span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">user_domain_error</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">function</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">message</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">user_pole_error</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">function</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">message</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">user_overflow_error</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">function</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">message</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">user_underflow_error</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">function</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">message</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">user_denorm_error</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">function</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">message</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">user_rounding_error</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">function</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">message</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">TargetType</span><span class="special">&gt;</span>
<span class="identifier">TargetType</span> <span class="identifier">user_rounding_error</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">function</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">message</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">TargetType</span><span class="special">&amp;</span> <span class="identifier">t</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">user_indeterminate_result_error</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">function</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">message</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>

<span class="special">}}}</span> <span class="comment">// namespaces</span>
</pre>
<p>
        Note that the strings <span class="emphasis"><em>function</em></span> and <span class="emphasis"><em>message</em></span>
        may contain "%1%" format specifiers designed to be used in conjunction
        with Boost.Format. If these strings are to be presented to the program's
        end-user then the "%1%" format specifier should be replaced with
        the name of type T in the <span class="emphasis"><em>function</em></span> string, and if there
        is a %1% specifier in the <span class="emphasis"><em>message</em></span> string then it should
        be replaced with the value of <span class="emphasis"><em>val</em></span>.
      </p>
<p>
        There is more information on user-defined error handlers in the <a class="link" href="../pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers">tutorial
        here</a>.
      </p>
<h5>
<a name="math_toolkit.pol_ref.error_handling_policies.h5"></a>
        <span class="phrase"><a name="math_toolkit.pol_ref.error_handling_policies.kinds_of_error_raised"></a></span><a class="link" href="error_handling_policies.html#math_toolkit.pol_ref.error_handling_policies.kinds_of_error_raised">Kinds
        of Error Raised</a>
      </h5>
<p>
        There are six kinds of error reported by this library, which are summarised
        in the following table:
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Error Type
                </p>
              </th>
<th>
                <p>
                  Policy Class
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Domain Error
                </p>
              </td>
<td>
                <p>
                  boost::math::policies::domain_error&lt;<span class="emphasis"><em>action</em></span>&gt;
                </p>
              </td>
<td>
                <p>
                  Raised when more or more arguments are outside the defined range
                  of the function.
                </p>
                <p>
                  Defaults to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">domain_error</span><span class="special">&lt;</span><span class="identifier">throw_on_error</span><span class="special">&gt;</span></code>
                </p>
                <p>
                  When the action is set to <span class="emphasis"><em>throw_on_error</em></span> then
                  throws <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">domain_error</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Pole Error
                </p>
              </td>
<td>
                <p>
                  boost::math::policies::pole_error&lt;<span class="emphasis"><em>action</em></span>&gt;
                </p>
              </td>
<td>
                <p>
                  Raised when more or more arguments would cause the function to
                  be evaluated at a pole.
                </p>
                <p>
                  Defaults to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">pole_error</span><span class="special">&lt;</span><span class="identifier">throw_on_error</span><span class="special">&gt;</span></code>
                </p>
                <p>
                  When the action is <span class="emphasis"><em>throw_on_error</em></span> then throw
                  a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">domain_error</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Overflow Error
                </p>
              </td>
<td>
                <p>
                  boost::math::policies::overflow_error&lt;<span class="emphasis"><em>action</em></span>&gt;
                </p>
              </td>
<td>
                <p>
                  Raised when the result of the function is outside the representable
                  range of the floating point type used.
                </p>
                <p>
                  Defaults to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">overflow_error</span><span class="special">&lt;</span><span class="identifier">throw_on_error</span><span class="special">&gt;</span></code>.
                </p>
                <p>
                  When the action is <span class="emphasis"><em>throw_on_error</em></span> then throws
                  a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Underflow Error
                </p>
              </td>
<td>
                <p>
                  boost::math::policies::underflow_error&lt;<span class="emphasis"><em>action</em></span>&gt;
                </p>
              </td>
<td>
                <p>
                  Raised when the result of the function is too small to be represented
                  in the floating point type used.
                </p>
                <p>
                  Defaults to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">underflow_error</span><span class="special">&lt;</span><span class="identifier">ignore_error</span><span class="special">&gt;</span></code>
                </p>
                <p>
                  When the specified action is <span class="emphasis"><em>throw_on_error</em></span>
                  then throws a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">underflow_error</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Denorm Error
                </p>
              </td>
<td>
                <p>
                  boost::math::policies::denorm_error&lt;<span class="emphasis"><em>action</em></span>&gt;
                </p>
              </td>
<td>
                <p>
                  Raised when the result of the function is a denormalised value.
                </p>
                <p>
                  Defaults to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">denorm_error</span><span class="special">&lt;</span><span class="identifier">ignore_error</span><span class="special">&gt;</span></code>
                </p>
                <p>
                  When the action is <span class="emphasis"><em>throw_on_error</em></span> then throws
                  a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">underflow_error</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Rounding Error
                </p>
              </td>
<td>
                <p>
                  boost::math::policies::rounding_error&lt;<span class="emphasis"><em>action</em></span>&gt;
                </p>
              </td>
<td>
                <p>
                  Raised When one of the rounding functions <a class="link" href="../rounding/round.html" title="Rounding Functions">round</a>,
                  <a class="link" href="../rounding/trunc.html" title="Truncation Functions">trunc</a> or <a class="link" href="../rounding/modf.html" title="Integer and Fractional Part Splitting (modf)">modf</a> is called with
                  an argument that has no integer representation, or is too large
                  to be represented in the result type
                </p>
                <p>
                  Defaults to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">rounding_error</span><span class="special">&lt;</span><span class="identifier">throw_on_error</span><span class="special">&gt;</span></code>
                </p>
                <p>
                  When the action is <span class="emphasis"><em>throw_on_error</em></span> then throws
                  <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">rounding_error</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Evaluation Error
                </p>
              </td>
<td>
                <p>
                  boost::math::policies::evaluation_error&lt;<span class="emphasis"><em>action</em></span>&gt;
                </p>
              </td>
<td>
                <p>
                  Raised when the result of the function is well defined and finite,
                  but we were unable to compute it. Typically this occurs when an
                  iterative method fails to converge. Of course ideally this error
                  should never be raised: feel free to report it as a bug if it is!
                </p>
                <p>
                  Defaults to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">evaluation_error</span><span class="special">&lt;</span><span class="identifier">throw_on_error</span><span class="special">&gt;</span></code>
                </p>
                <p>
                  When the action is <span class="emphasis"><em>throw_on_error</em></span> then throws
                  <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">evaluation_error</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Indeterminate Result Error
                </p>
              </td>
<td>
                <p>
                  boost::math::policies::indeterminate_result_error&lt;<span class="emphasis"><em>action</em></span>&gt;
                </p>
              </td>
<td>
                <p>
                  Raised when the result of a function is not defined for the values
                  that were passed to it.
                </p>
                <p>
                  Defaults to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">indeterminate_result_error</span><span class="special">&lt;</span><span class="identifier">ignore_error</span><span class="special">&gt;</span></code>
                </p>
                <p>
                  When the action is <span class="emphasis"><em>throw_on_error</em></span> then throws
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">domain_error</span></code>
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h5>
<a name="math_toolkit.pol_ref.error_handling_policies.h6"></a>
        <span class="phrase"><a name="math_toolkit.pol_ref.error_handling_policies.examples"></a></span><a class="link" href="error_handling_policies.html#math_toolkit.pol_ref.error_handling_policies.examples">Examples</a>
      </h5>
<p>
        Suppose we want a call to <code class="computeroutput"><span class="identifier">tgamma</span></code>
        to behave in a C-compatible way and set global <code class="computeroutput"><span class="special">::</span><span class="identifier">errno</span></code> rather than throw an exception, we
        can achieve this at the call site using:
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">gamma</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">tgamma</span><span class="special">;</span>

<span class="comment">//using namespace boost::math::policies; may also be convenient.</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">policy</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">evaluation_error</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">domain_error</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">overflow_error</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">domain_error</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">pole_error</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">::</span><span class="identifier">errno_on_error</span><span class="special">;</span>

<span class="comment">// Define a policy:</span>
<span class="keyword">typedef</span> <span class="identifier">policy</span><span class="special">&lt;</span>
  <span class="identifier">domain_error</span><span class="special">&lt;</span><span class="identifier">errno_on_error</span><span class="special">&gt;,</span>
  <span class="identifier">pole_error</span><span class="special">&lt;</span><span class="identifier">errno_on_error</span><span class="special">&gt;,</span>
  <span class="identifier">overflow_error</span><span class="special">&lt;</span><span class="identifier">errno_on_error</span><span class="special">&gt;,</span>
  <span class="identifier">evaluation_error</span><span class="special">&lt;</span><span class="identifier">errno_on_error</span><span class="special">&gt;</span>
<span class="special">&gt;</span> <span class="identifier">my_policy</span><span class="special">;</span>

<span class="keyword">double</span> <span class="identifier">my_value</span> <span class="special">=</span> <span class="number">0.</span><span class="special">;</span> <span class="comment">// </span>

<span class="comment">// Call the function applying my_policy:</span>
<span class="keyword">double</span> <span class="identifier">t1</span> <span class="special">=</span> <span class="identifier">tgamma</span><span class="special">(</span><span class="identifier">my_value</span><span class="special">,</span> <span class="identifier">my_policy</span><span class="special">());</span>

<span class="comment">// Alternatively (and equivalently) we could use helpful function</span>
<span class="comment">// make_policy and define everything at the call site:</span>
<span class="keyword">double</span> <span class="identifier">t2</span> <span class="special">=</span> <span class="identifier">tgamma</span><span class="special">(</span><span class="identifier">my_value</span><span class="special">,</span>
  <span class="identifier">make_policy</span><span class="special">(</span>
    <span class="identifier">domain_error</span><span class="special">&lt;</span><span class="identifier">errno_on_error</span><span class="special">&gt;(),</span>
    <span class="identifier">pole_error</span><span class="special">&lt;</span><span class="identifier">errno_on_error</span><span class="special">&gt;(),</span>
    <span class="identifier">overflow_error</span><span class="special">&lt;</span><span class="identifier">errno_on_error</span><span class="special">&gt;(),</span>
    <span class="identifier">evaluation_error</span><span class="special">&lt;</span><span class="identifier">errno_on_error</span><span class="special">&gt;()</span> <span class="special">)</span>
  <span class="special">);</span>
</pre>
<p>
        Suppose we want a statistical distribution to return infinities, rather than
        throw exceptions, then we can use:
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">distributions</span><span class="special">/</span><span class="identifier">normal</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">normal_distribution</span><span class="special">;</span>

<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">policies</span><span class="special">;</span>

<span class="comment">// Define a specific policy:</span>
<span class="keyword">typedef</span> <span class="identifier">policy</span><span class="special">&lt;</span>
      <span class="identifier">overflow_error</span><span class="special">&lt;</span><span class="identifier">ignore_error</span><span class="special">&gt;</span>
      <span class="special">&gt;</span> <span class="identifier">my_policy</span><span class="special">;</span>

<span class="comment">// Define the distribution, using my_policy:</span>
<span class="keyword">typedef</span> <span class="identifier">normal_distribution</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">,</span> <span class="identifier">my_policy</span><span class="special">&gt;</span> <span class="identifier">my_norm</span><span class="special">;</span>

<span class="comment">// Construct a my_norm distribution, using default mean and standard deviation,</span>
<span class="comment">// and get a 0.05 or 5% quantile:</span>
<span class="keyword">double</span> <span class="identifier">q</span> <span class="special">=</span> <span class="identifier">quantile</span><span class="special">(</span><span class="identifier">my_norm</span><span class="special">(),</span> <span class="number">0.05</span><span class="special">);</span> <span class="comment">// = -1.64485</span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../pol_ref.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../pol_ref.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="internal_promotion.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
