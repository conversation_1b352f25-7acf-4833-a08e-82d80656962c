<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Overloading template functions with float128_t</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../float128.html" title="Implementation of Float128 type">
<link rel="prev" href="../float128.html" title="Implementation of Float128 type">
<link rel="next" href="exp_function.html" title="Exponential function">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../float128.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../float128.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="exp_function.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.float128.overloading"></a><a class="link" href="overloading.html" title="Overloading template functions with float128_t">Overloading template
      functions with float128_t</a>
</h3></div></div></div>
<p>
        An artifact of providing C++ standard library support for quadmath may mandate
        the inclusion of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">cstdfloat</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
        <span class="bold"><strong>before</strong></span> the inclusion of other headers.
      </p>
<p>
        Consider a function that calls <code class="computeroutput"><span class="identifier">fabs</span><span class="special">(</span><span class="identifier">x</span><span class="special">)</span></code>
        and has previously injected <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">fabs</span><span class="special">()</span></code> into local scope via a <code class="computeroutput"><span class="keyword">using</span></code>
        directive:
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">unsigned_compare</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T</span> <span class="identifier">b</span><span class="special">)</span>
<span class="special">{</span>
   <span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">fabs</span><span class="special">;</span>
   <span class="keyword">return</span> <span class="identifier">fabs</span><span class="special">(</span><span class="identifier">a</span><span class="special">)</span> <span class="special">==</span> <span class="identifier">fabs</span><span class="special">(</span><span class="identifier">b</span><span class="special">);</span>
<span class="special">}</span>
</pre>
<p>
        In this function, the correct overload of <code class="computeroutput"><span class="identifier">fabs</span></code>
        may be found via <a href="http://en.wikipedia.org/wiki/Argument-dependent_name_lookup" target="_top">argument-dependent-lookup
        (ADL)</a> or by calling one of the <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">fabs</span></code>
        overloads. There is a key difference between them however: an overload in
        the same namespace as T and found via ADL need <span class="emphasis"><em><span class="bold"><strong>not
        be defined at the time the function is declared</strong></span></em></span>. However,
        all the types declared in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">cstdfloat</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
        are fundamental types, so for these types we are relying on finding an overload
        declared in namespace <code class="computeroutput"><span class="identifier">std</span></code>.
        In that case however, <span class="emphasis"><em><span class="bold"><strong>all such overloads
        must be declared prior to the definition of function <code class="computeroutput"><span class="identifier">unsigned_compare</span></code>
        otherwise they are not considered</strong></span></em></span>.
      </p>
<p>
        In the event that <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">cstdfloat</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
        has been included <span class="bold"><strong>after</strong></span> the definition of
        the above function, the correct overload of <code class="computeroutput"><span class="identifier">fabs</span></code>,
        while present, is simply not considered as part of the overload set. So the
        compiler tries to downcast the <code class="computeroutput"><span class="identifier">float128_t</span></code>
        argument first to <code class="computeroutput"><span class="keyword">long</span> <span class="keyword">double</span></code>,
        then to <code class="computeroutput"><span class="keyword">double</span></code>, then to <code class="computeroutput"><span class="keyword">float</span></code>; the compilation fails because the result
        is ambiguous. However the compiler error message will appear cruelly inscrutable,
        at an apparently irrelevant line number and making no mention of <code class="computeroutput"><span class="identifier">float128</span></code>: the word <span class="emphasis"><em>ambiguous</em></span>
        is the clue to what is wrong.
      </p>
<p>
        Provided you <code class="computeroutput"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">cstdfloat</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
        <span class="bold"><strong>before</strong></span> the inclusion of the any header containing
        generic floating point code (such as other Boost.Math headers, then the compiler
        will know about and use the <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">fabs</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">float128_t</span><span class="special">)</span></code>
        that we provide in <code class="computeroutput"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">cstdfloat</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../float128.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../float128.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="exp_function.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
