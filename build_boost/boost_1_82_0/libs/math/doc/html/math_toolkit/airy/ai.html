<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Airy Ai Function</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../airy.html" title="Airy Functions">
<link rel="prev" href="../airy.html" title="Airy Functions">
<link rel="next" href="bi.html" title="Airy Bi Function">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../airy.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../airy.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="bi.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.airy.ai"></a><a class="link" href="ai.html" title="Airy Ai Function">Airy Ai Function</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.airy.ai.h0"></a>
        <span class="phrase"><a name="math_toolkit.airy.ai.synopsis"></a></span><a class="link" href="ai.html#math_toolkit.airy.ai.synopsis">Synopsis</a>
      </h5>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">airy</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span> <span class="special">{</span>

 <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
 <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">airy_ai</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">x</span><span class="special">);</span>

 <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Policy</span><span class="special">&gt;</span>
 <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">airy_ai</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Policy</span><span class="special">&amp;);</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<h5>
<a name="math_toolkit.airy.ai.h1"></a>
        <span class="phrase"><a name="math_toolkit.airy.ai.description"></a></span><a class="link" href="ai.html#math_toolkit.airy.ai.description">Description</a>
      </h5>
<p>
        The function <a class="link" href="ai.html" title="Airy Ai Function">airy_ai</a> calculates
        the Airy function Ai which is the first solution to the differential equation:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/airy.svg"></span>

        </p></blockquote></div>
<p>
        See Weisstein, Eric W. "Airy Functions." From MathWorld--A Wolfram
        Web Resource. <a href="http://mathworld.wolfram.com/AiryFunctions.html" target="_top">http://mathworld.wolfram.com/AiryFunctions.html</a>
      </p>
<p>
        and <a href="https://en.wikipedia.org/wiki/Airy_zeta_function" target="_top">Airy Zeta
        function</a>.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<p>
        The following graph illustrates how this function changes as <span class="emphasis"><em>x</em></span>
        changes: for negative <span class="emphasis"><em>x</em></span> the function is cyclic, while
        for positive <span class="emphasis"><em>x</em></span> the value tends to zero:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/airy_ai.svg" align="middle"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.airy.ai.h2"></a>
        <span class="phrase"><a name="math_toolkit.airy.ai.accuracy"></a></span><a class="link" href="ai.html#math_toolkit.airy.ai.accuracy">Accuracy</a>
      </h5>
<p>
        This function is implemented entirely in terms of the Bessel functions <a class="link" href="../bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds">cyl_bessel_j</a> and <a class="link" href="../bessel/mbessel.html" title="Modified Bessel Functions of the First and Second Kinds">cyl_bessel_k</a> - refer to those
        functions for detailed accuracy information.
      </p>
<p>
        In general though, the relative error is low (less than 100 ε) for <span class="emphasis"><em>x
        &gt; 0</em></span> while only the absolute error is low for <span class="emphasis"><em>x &lt;
        0</em></span> as the following error plot illustrates:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/ai__double.svg" align="middle"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.airy.ai.h3"></a>
        <span class="phrase"><a name="math_toolkit.airy.ai.testing"></a></span><a class="link" href="ai.html#math_toolkit.airy.ai.testing">Testing</a>
      </h5>
<p>
        Since this function is implemented in terms of other special functions, there
        are only a few basic sanity checks, using test values from <a href="http://functions.wolfram.com/" target="_top">Wolfram
        Airy Functions</a>.
      </p>
<h5>
<a name="math_toolkit.airy.ai.h4"></a>
        <span class="phrase"><a name="math_toolkit.airy.ai.implementation"></a></span><a class="link" href="ai.html#math_toolkit.airy.ai.implementation">Implementation</a>
      </h5>
<p>
        This function is implemented in terms of the Bessel functions using the relations:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/airy_ai.svg"></span>

        </p></blockquote></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../airy.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../airy.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="bi.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
