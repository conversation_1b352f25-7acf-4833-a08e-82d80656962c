<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Finding Zeros of Airy Functions</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../airy.html" title="Airy Functions">
<link rel="prev" href="bip.html" title="Airy Bi' Function">
<link rel="next" href="../ellint.html" title="Elliptic Integrals">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="bip.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../airy.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../ellint.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.airy.airy_root"></a><a class="link" href="airy_root.html" title="Finding Zeros of Airy Functions">Finding Zeros of Airy Functions</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.airy.airy_root.h0"></a>
        <span class="phrase"><a name="math_toolkit.airy.airy_root.synopsis"></a></span><a class="link" href="airy_root.html#math_toolkit.airy.airy_root.synopsis">Synopsis</a>
      </h5>
<p>
        <code class="computeroutput"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">airy</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<p>
        Functions for obtaining both a single zero or root of the Airy functions,
        and placing multiple zeros into a container like <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span></code>
        by providing an output iterator.
      </p>
<p>
        The signature of the single value functions are:
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">airy_ai_zero</span><span class="special">(</span>
         <span class="keyword">int</span> <span class="identifier">m</span><span class="special">);</span>         <span class="comment">// 1-based index of zero.</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">airy_bi_zero</span><span class="special">(</span>
         <span class="keyword">int</span> <span class="identifier">m</span><span class="special">);</span>         <span class="comment">// 1-based index of zero.</span>
</pre>
<p>
        and for multiple zeros:
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">OutputIterator</span><span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">airy_ai_zero</span><span class="special">(</span>
                     <span class="keyword">int</span> <span class="identifier">start_index</span><span class="special">,</span>           <span class="comment">// 1-based index of first zero.</span>
                     <span class="keyword">unsigned</span> <span class="identifier">number_of_zeros</span><span class="special">,</span>  <span class="comment">// How many zeros to generate.</span>
                     <span class="identifier">OutputIterator</span> <span class="identifier">out_it</span><span class="special">);</span>    <span class="comment">// Destination for zeros.</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">OutputIterator</span><span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">airy_bi_zero</span><span class="special">(</span>
                     <span class="keyword">int</span> <span class="identifier">start_index</span><span class="special">,</span>           <span class="comment">// 1-based index of zero.</span>
                     <span class="keyword">unsigned</span> <span class="identifier">number_of_zeros</span><span class="special">,</span>  <span class="comment">// How many zeros to generate</span>
                     <span class="identifier">OutputIterator</span> <span class="identifier">out_it</span><span class="special">);</span>    <span class="comment">// Destination for zeros.</span>
</pre>
<p>
        There are also versions which allow control of the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policies</a>
        for error handling and precision.
      </p>
<pre class="programlisting"> <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
 <span class="identifier">T</span> <span class="identifier">airy_ai_zero</span><span class="special">(</span>
          <span class="keyword">int</span> <span class="identifier">m</span><span class="special">,</span>          <span class="comment">// 1-based index of zero.</span>
          <span class="keyword">const</span> <span class="identifier">Policy</span><span class="special">&amp;);</span> <span class="comment">// Policy to use.</span>

 <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
 <span class="identifier">T</span> <span class="identifier">airy_bi_zero</span><span class="special">(</span>
          <span class="keyword">int</span> <span class="identifier">m</span><span class="special">,</span>          <span class="comment">// 1-based index of zero.</span>
          <span class="keyword">const</span> <span class="identifier">Policy</span><span class="special">&amp;);</span> <span class="comment">// Policy to use.</span>


<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">OutputIterator</span><span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">airy_ai_zero</span><span class="special">(</span>
                     <span class="keyword">int</span> <span class="identifier">start_index</span><span class="special">,</span>           <span class="comment">// 1-based index of first zero.</span>
                     <span class="keyword">unsigned</span> <span class="identifier">number_of_zeros</span><span class="special">,</span>  <span class="comment">// How many zeros to generate.</span>
                     <span class="identifier">OutputIterator</span> <span class="identifier">out_it</span><span class="special">,</span>     <span class="comment">// Destination for zeros.</span>
                     <span class="keyword">const</span> <span class="identifier">Policy</span><span class="special">&amp;</span> <span class="identifier">pol</span><span class="special">);</span>        <span class="comment">// Policy to use.</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">OutputIterator</span><span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">airy_bi_zero</span><span class="special">(</span>
                     <span class="keyword">int</span> <span class="identifier">start_index</span><span class="special">,</span>           <span class="comment">// 1-based index of zero.</span>
                     <span class="keyword">unsigned</span> <span class="identifier">number_of_zeros</span><span class="special">,</span>  <span class="comment">// How many zeros to generate.</span>
                     <span class="identifier">OutputIterator</span> <span class="identifier">out_it</span><span class="special">,</span>     <span class="comment">// Destination for zeros.</span>
                     <span class="keyword">const</span> <span class="identifier">Policy</span><span class="special">&amp;</span> <span class="identifier">pol</span><span class="special">);</span>        <span class="comment">// Policy to use.</span>
</pre>
<h5>
<a name="math_toolkit.airy.airy_root.h1"></a>
        <span class="phrase"><a name="math_toolkit.airy.airy_root.description"></a></span><a class="link" href="airy_root.html#math_toolkit.airy.airy_root.description">Description</a>
      </h5>
<p>
        The Airy Ai and Bi functions have an infinite number of zeros on the negative
        real axis. The real zeros on the negative real axis can be found by solving
        for the roots of
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="emphasis"><em>Ai(x<sub>m</sub>) = 0</em></span>
        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="emphasis"><em>Bi(y<sub>m</sub>) = 0</em></span>
        </p></blockquote></div>
<p>
        Here, <span class="emphasis"><em>x<sub>m</sub></em></span> represents the <span class="emphasis"><em>m<sup>th</sup></em></span> root
        of the Airy Ai function, and <span class="emphasis"><em>y<sub>m</sub></em></span> represents the <span class="emphasis"><em>m<sup>th</sup></em></span>
        root of the Airy Bi function.
      </p>
<p>
        The zeros or roots (values of <code class="computeroutput"><span class="identifier">x</span></code>
        where the function crosses the horizontal <code class="computeroutput"><span class="identifier">y</span>
        <span class="special">=</span> <span class="number">0</span></code>
        axis) of the Airy Ai and Bi functions are computed by two functions, <code class="computeroutput"><span class="identifier">airy_ai_zero</span></code> and <code class="computeroutput"><span class="identifier">airy_bi_zero</span></code>.
      </p>
<p>
        In each case the index or rank of the zero returned is 1-based, which is
        to say:
      </p>
<pre class="programlisting"><span class="identifier">airy_ai_zero</span><span class="special">(</span><span class="number">1</span><span class="special">);</span>
</pre>
<p>
        returns the first zero of Ai.
      </p>
<p>
        Passing an <code class="computeroutput"><span class="identifier">start_index</span> <span class="special">&lt;=</span>
        <span class="number">0</span></code> results in a <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>
        being raised.
      </p>
<p>
        The first few zeros returned by these functions have approximate values as
        follows:
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  m
                </p>
              </th>
<th>
                <p>
                  Ai
                </p>
              </th>
<th>
                <p>
                  Bi
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  -2.33811...
                </p>
              </td>
<td>
                <p>
                  -1.17371...
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  2
                </p>
              </td>
<td>
                <p>
                  -4.08795...
                </p>
              </td>
<td>
                <p>
                  -3.27109...
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  3
                </p>
              </td>
<td>
                <p>
                  -5.52056...
                </p>
              </td>
<td>
                <p>
                  -4.83074...
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  -6.78671...
                </p>
              </td>
<td>
                <p>
                  -6.16985...
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  5
                </p>
              </td>
<td>
                <p>
                  -7.94413...
                </p>
              </td>
<td>
                <p>
                  -7.37676...
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  6
                </p>
              </td>
<td>
                <p>
                  -9.02265...
                </p>
              </td>
<td>
                <p>
                  -8.49195...
                </p>
              </td>
</tr>
</tbody>
</table></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/airy_zeros.svg" align="middle"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.airy.airy_root.h2"></a>
        <span class="phrase"><a name="math_toolkit.airy.airy_root.examples_of_finding_airy_zeros"></a></span><a class="link" href="airy_root.html#math_toolkit.airy.airy_root.examples_of_finding_airy_zeros">Examples
        of finding Airy Zeros</a>
      </h5>
<p>
        This example demonstrates calculating zeros of the Airy functions. It also
        shows how Boost.Math and Boost.Multiprecision can be combined to provide
        a many decimal digit precision. For 50 decimal digit precision we need to
        include
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">multiprecision</span><span class="special">/</span><span class="identifier">cpp_dec_float</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<p>
        and a <code class="computeroutput"><span class="keyword">typedef</span></code> for <code class="computeroutput"><span class="identifier">float_type</span></code> may be convenient (allowing
        a quick switch to re-compute at built-in <code class="computeroutput"><span class="keyword">double</span></code>
        or other precision)
      </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">multiprecision</span><span class="special">::</span><span class="identifier">cpp_dec_float_50</span> <span class="identifier">float_type</span><span class="special">;</span>
</pre>
<p>
        To use the functions for finding zeros of the functions we need
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">airy</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<p>
        This example shows obtaining both a single zero of the Airy functions, and
        then placing multiple zeros into a container like <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span></code>
        by providing an iterator. The signature of the single-value Airy Ai function
        is:
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">airy_ai_zero</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">);</span> <span class="comment">// 1-based index of the zero.</span>
</pre>
<p>
        The signature of multiple zeros Airy Ai function is:
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">OutputIterator</span><span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">airy_ai_zero</span><span class="special">(</span>
                         <span class="keyword">unsigned</span> <span class="identifier">start_index</span><span class="special">,</span> <span class="comment">// 1-based index of the zero.</span>
                         <span class="keyword">unsigned</span> <span class="identifier">number_of_zeros</span><span class="special">,</span> <span class="comment">// How many zeros to generate.</span>
                         <span class="identifier">OutputIterator</span> <span class="identifier">out_it</span><span class="special">);</span> <span class="comment">// Destination for zeros.</span>
</pre>
<p>
        There are also versions which allows control of the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policies</a>
        for error handling and precision.
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Policy</span><span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">airy_ai_zero</span><span class="special">(</span>
                         <span class="keyword">unsigned</span> <span class="identifier">start_index</span><span class="special">,</span> <span class="comment">// 1-based index of the zero.</span>
                         <span class="keyword">unsigned</span> <span class="identifier">number_of_zeros</span><span class="special">,</span> <span class="comment">// How many zeros to generate.</span>
                         <span class="identifier">OutputIterator</span> <span class="identifier">out_it</span><span class="special">,</span> <span class="comment">// Destination for zeros.</span>
                         <span class="keyword">const</span> <span class="identifier">Policy</span><span class="special">&amp;</span> <span class="identifier">pol</span><span class="special">);</span>  <span class="comment">// Policy to use.</span>
</pre>
<div class="tip"><table border="0" summary="Tip">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Tip]" src="../../../../../../doc/src/images/tip.png"></td>
<th align="left">Tip</th>
</tr>
<tr><td align="left" valign="top"><p>
          It is always wise to place code using Boost.Math inside <code class="computeroutput"><span class="keyword">try</span><span class="char">'n'</span><span class="keyword">catch</span></code> blocks;
          this will ensure that helpful error messages are shown when exceptional
          conditions arise.
        </p></td></tr>
</table></div>
<p>
        First, evaluate a single Airy zero.
      </p>
<p>
        The precision is controlled by the template parameter <code class="computeroutput"><span class="identifier">T</span></code>,
        so this example has <code class="computeroutput"><span class="keyword">double</span></code> precision,
        at least 15 but up to 17 decimal digits (for the common 64-bit double).
      </p>
<pre class="programlisting"><span class="keyword">double</span> <span class="identifier">aiz1</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">airy_ai_zero</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">1</span><span class="special">);</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"boost::math::airy_ai_zero&lt;double&gt;(1) = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">aiz1</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="keyword">double</span> <span class="identifier">aiz2</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">airy_ai_zero</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">2</span><span class="special">);</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"boost::math::airy_ai_zero&lt;double&gt;(2) = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">aiz2</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="keyword">double</span> <span class="identifier">biz3</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">airy_bi_zero</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">3</span><span class="special">);</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"boost::math::airy_bi_zero&lt;double&gt;(3) = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">biz3</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
</pre>
<p>
        Other versions of <code class="computeroutput"><span class="identifier">airy_ai_zero</span></code>
        and <code class="computeroutput"><span class="identifier">airy_bi_zero</span></code> allow calculation
        of multiple zeros with one call, placing the results in a container, often
        <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span></code>. For example, generate and display
        the first five <code class="computeroutput"><span class="keyword">double</span></code> roots
        <a href="http://mathworld.wolfram.com/AiryFunctionZeros.html" target="_top">Wolfram
        Airy Functions Zeros</a>.
      </p>
<pre class="programlisting"><span class="keyword">unsigned</span> <span class="keyword">int</span> <span class="identifier">n_roots</span> <span class="special">=</span> <span class="number">5U</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="identifier">roots</span><span class="special">;</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">airy_ai_zero</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">1U</span><span class="special">,</span> <span class="identifier">n_roots</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">back_inserter</span><span class="special">(</span><span class="identifier">roots</span><span class="special">));</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"airy_ai_zeros:"</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">copy</span><span class="special">(</span><span class="identifier">roots</span><span class="special">.</span><span class="identifier">begin</span><span class="special">(),</span>
          <span class="identifier">roots</span><span class="special">.</span><span class="identifier">end</span><span class="special">(),</span>
          <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream_iterator</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span><span class="special">,</span> <span class="string">"\n"</span><span class="special">));</span>
</pre>
<p>
        The first few real roots of Ai(x) are approximately -2.33811, -4.08795, -5.52056,
        -6.7867144, -7.94413, -9.02265 ...
      </p>
<p>
        Or we can use Boost.Multiprecision to generate 50 decimal digit roots.
      </p>
<p>
        We set the precision of the output stream, and show trailing zeros to display
        a fixed 50 decimal digits.
      </p>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span><span class="special">.</span><span class="identifier">precision</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">float_type</span><span class="special">&gt;::</span><span class="identifier">digits10</span><span class="special">);</span> <span class="comment">// float_type has 50 decimal digits.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">showpoint</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span> <span class="comment">// Show trailing zeros too.</span>

<span class="keyword">unsigned</span> <span class="keyword">int</span> <span class="identifier">m</span> <span class="special">=</span> <span class="number">1U</span><span class="special">;</span>
<span class="identifier">float_type</span> <span class="identifier">r</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">airy_ai_zero</span><span class="special">&lt;</span><span class="identifier">float_type</span><span class="special">&gt;(</span><span class="number">1U</span><span class="special">);</span> <span class="comment">// 1st root.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"boost::math::airy_bi_zero&lt;float_type&gt;("</span> <span class="special">&lt;&lt;</span> <span class="identifier">m</span> <span class="special">&lt;&lt;</span> <span class="string">")  = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">r</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="identifier">m</span> <span class="special">=</span> <span class="number">2</span><span class="special">;</span>
<span class="identifier">r</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">airy_ai_zero</span><span class="special">&lt;</span><span class="identifier">float_type</span><span class="special">&gt;(</span><span class="number">2U</span><span class="special">);</span> <span class="comment">// 2nd root.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"boost::math::airy_bi_zero&lt;float_type&gt;("</span> <span class="special">&lt;&lt;</span> <span class="identifier">m</span> <span class="special">&lt;&lt;</span> <span class="string">")  = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">r</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="identifier">m</span> <span class="special">=</span> <span class="number">7U</span><span class="special">;</span>
<span class="identifier">r</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">airy_bi_zero</span><span class="special">&lt;</span><span class="identifier">float_type</span><span class="special">&gt;(</span><span class="number">7U</span><span class="special">);</span> <span class="comment">// 7th root.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"boost::math::airy_bi_zero&lt;float_type&gt;("</span> <span class="special">&lt;&lt;</span> <span class="identifier">m</span> <span class="special">&lt;&lt;</span> <span class="string">")  = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">r</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span><span class="identifier">float_type</span><span class="special">&gt;</span> <span class="identifier">zeros</span><span class="special">;</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">airy_ai_zero</span><span class="special">&lt;</span><span class="identifier">float_type</span><span class="special">&gt;(</span><span class="number">1U</span><span class="special">,</span> <span class="number">3</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">back_inserter</span><span class="special">(</span><span class="identifier">zeros</span><span class="special">));</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"airy_ai_zeros:"</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="comment">// Print the roots to the output stream.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">copy</span><span class="special">(</span><span class="identifier">zeros</span><span class="special">.</span><span class="identifier">begin</span><span class="special">(),</span> <span class="identifier">zeros</span><span class="special">.</span><span class="identifier">end</span><span class="special">(),</span>
          <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream_iterator</span><span class="special">&lt;</span><span class="identifier">float_type</span><span class="special">&gt;(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span><span class="special">,</span> <span class="string">"\n"</span><span class="special">));</span>
</pre>
<p>
        Produces the program output:
      </p>
<pre class="programlisting">boost::math::airy_ai_zero&lt;double&gt;(1) = -2.33811
boost::math::airy_ai_zero&lt;double&gt;(2) = -4.08795
boost::math::airy_bi_zero&lt;double&gt;(3) = -4.83074
airy_ai_zeros:
-2.33811
-4.08795
-5.52056
-6.78671
-7.94413

boost::math::airy_bi_zero&lt;float_type&gt;(1)  = -2.3381074104597670384891972524467354406385401456711
boost::math::airy_bi_zero&lt;float_type&gt;(2)  = -4.0879494441309706166369887014573910602247646991085
boost::math::airy_bi_zero&lt;float_type&gt;(7)  = -9.5381943793462388866329885451560196208390720763825
airy_ai_zeros:
-2.3381074104597670384891972524467354406385401456711
-4.0879494441309706166369887014573910602247646991085
-5.5205598280955510591298555129312935737972142806175
</pre>
<p>
        The full code (and output) for this example is at <a href="../../../../example/airy_zeros_example.cpp" target="_top">airy_zeros_example.cpp</a>,
      </p>
<h4>
<a name="math_toolkit.airy.airy_root.h3"></a>
        <span class="phrase"><a name="math_toolkit.airy.airy_root.implementation"></a></span><a class="link" href="airy_root.html#math_toolkit.airy.airy_root.implementation">Implementation</a>
      </h4>
<p>
        Given the following function (A&amp;S 10.4.105):
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/airy_zero_1.svg"></span>

        </p></blockquote></div>
<p>
        Then an initial estimate for the n<sup>th</sup> zero a<sub>n</sub> of Ai is given by (A&amp;S 10.4.94):
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/airy_zero_2.svg"></span>

        </p></blockquote></div>
<p>
        and an initial estimate for the n<sup>th</sup> zero b<sub>n</sub> of Bi is given by (A&amp;S 10.4.98):
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/airy_zero_3.svg"></span>

        </p></blockquote></div>
<p>
        Thereafter the roots are refined using Newton iteration.
      </p>
<h4>
<a name="math_toolkit.airy.airy_root.h4"></a>
        <span class="phrase"><a name="math_toolkit.airy.airy_root.testing"></a></span><a class="link" href="airy_root.html#math_toolkit.airy.airy_root.testing">Testing</a>
      </h4>
<p>
        The precision of evaluation of zeros was tested at 50 decimal digits using
        <code class="computeroutput"><span class="identifier">cpp_dec_float_50</span></code> and found
        identical with spot values computed by <a href="http://www.wolframalpha.com/" target="_top">Wolfram
        Alpha</a>.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="bip.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../airy.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../ellint.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
