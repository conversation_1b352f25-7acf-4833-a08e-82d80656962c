<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Elliptic Integrals of the First Kind - Legendre Form</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../ellint.html" title="Elliptic Integrals">
<link rel="prev" href="ellint_carlson.html" title="Elliptic Integrals - Carlson Form">
<link rel="next" href="ellint_2.html" title="Elliptic Integrals of the Second Kind - Legendre Form">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ellint_carlson.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ellint.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ellint_2.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.ellint.ellint_1"></a><a class="link" href="ellint_1.html" title="Elliptic Integrals of the First Kind - Legendre Form">Elliptic Integrals of the
      First Kind - Legendre Form</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.ellint.ellint_1.h0"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_1.synopsis"></a></span><a class="link" href="ellint_1.html#math_toolkit.ellint.ellint_1.synopsis">Synopsis</a>
      </h5>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">ellint_1</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span> <span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_1</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">k</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">phi</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_1</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">k</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">phi</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_1</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">k</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_1</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<h5>
<a name="math_toolkit.ellint.ellint_1.h1"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_1.description"></a></span><a class="link" href="ellint_1.html#math_toolkit.ellint.ellint_1.description">Description</a>
      </h5>
<p>
        These two functions evaluate the incomplete elliptic integral of the first
        kind <span class="emphasis"><em>F(φ, k)</em></span> and its complete counterpart <span class="emphasis"><em>K(k)
        = F(π/2, k)</em></span>.
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/ellint_1.svg" align="middle"></span>

        </p></blockquote></div>
<p>
        The return type of these functions is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a> when T1 and T2 are different types:
        when they are the same type then the result is the same type as the arguments.
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_1</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">k</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">phi</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_1</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">k</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">phi</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<p>
        Returns the incomplete elliptic integral of the first kind <span class="emphasis"><em>F(φ,
        k)</em></span>:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint2.svg"></span>

        </p></blockquote></div>
<p>
        Requires k<sup>2</sup>sin<sup>2</sup>(phi) &lt; 1, otherwise returns the result of <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_1</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">k</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_1</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<p>
        Returns the complete elliptic integral of the first kind <span class="emphasis"><em>K(k)</em></span>:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint6.svg"></span>

        </p></blockquote></div>
<p>
        Requires |k| &lt; 1, otherwise returns the result of <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<h5>
<a name="math_toolkit.ellint.ellint_1.h2"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_1.accuracy"></a></span><a class="link" href="ellint_1.html#math_toolkit.ellint.ellint_1.accuracy">Accuracy</a>
      </h5>
<p>
        These functions are computed using only basic arithmetic operations, so there
        isn't much variation in accuracy over differing platforms. Note that only
        results for the widest floating point type on the system are given as narrower
        types have <a class="link" href="../relative_error.html#math_toolkit.relative_error.zero_error">effectively
        zero error</a>. All values are relative errors in units of epsilon.
      </p>
<div class="table">
<a name="math_toolkit.ellint.ellint_1.table_ellint_1"></a><p class="title"><b>Table 8.63. Error rates for ellint_1</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_1">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral F: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.94ε (Mean = 0.509ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = +INFε (Mean
                  = +INFε) <a class="link" href="../logs_and_tables/logs.html#errors_GNU_C_version_7_1_0_linux_long_double_ellint_1__cmath__Elliptic_Integral_F_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.919ε (Mean = 0.544ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.94ε (Mean = 0.509ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.919ε (Mean = 0.542ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral F: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.57ε (Mean = 0.56ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.56ε (Mean = 0.816ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.99ε (Mean = 0.797ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.57ε (Mean = 0.561ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.26ε (Mean = 0.631ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
        The following error plot are based on an exhaustive search of the functions
        domain, MSVC-15.5 at <code class="computeroutput"><span class="keyword">double</span></code>
        precision, and GCC-7.1/Ubuntu for <code class="computeroutput"><span class="keyword">long</span>
        <span class="keyword">double</span></code> and <code class="computeroutput"><span class="identifier">__float128</span></code>.
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/elliptic_integral_k__double.svg" align="middle"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/elliptic_integral_k__80_bit_long_double.svg" align="middle"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/elliptic_integral_k____float128.svg" align="middle"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.ellint.ellint_1.h3"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_1.testing"></a></span><a class="link" href="ellint_1.html#math_toolkit.ellint.ellint_1.testing">Testing</a>
      </h5>
<p>
        The tests use a mixture of spot test values calculated using the online calculator
        at <a href="http://functions.wolfram.com/" target="_top">functions.wolfram.com</a>,
        and random test data generated using NTL::RR at 1000-bit precision and this
        implementation.
      </p>
<h5>
<a name="math_toolkit.ellint.ellint_1.h4"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_1.implementation"></a></span><a class="link" href="ellint_1.html#math_toolkit.ellint.ellint_1.implementation">Implementation</a>
      </h5>
<p>
        For up to 80-bit long double precision the complete versions of these functions
        are implemented as Taylor series expansions as in: "Fast computation
        of complete elliptic integrals and Jacobian elliptic functions", Celestial
        Mechanics and Dynamical Astronomy, April 2012.
      </p>
<p>
        Otherwise these functions are implemented in terms of Carlson's integrals
        using the relations:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint19.svg"></span>

        </p></blockquote></div>
<p>
        and
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint20.svg"></span>

        </p></blockquote></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ellint_carlson.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ellint.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ellint_2.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
