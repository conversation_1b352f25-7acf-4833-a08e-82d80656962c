<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Elliptic Integrals - Carlson Form</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../ellint.html" title="Elliptic Integrals">
<link rel="prev" href="ellint_intro.html" title="Elliptic Integral Overview">
<link rel="next" href="ellint_1.html" title="Elliptic Integrals of the First Kind - Legendre Form">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ellint_intro.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ellint.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ellint_1.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.ellint.ellint_carlson"></a><a class="link" href="ellint_carlson.html" title="Elliptic Integrals - Carlson Form">Elliptic Integrals
      - Carlson Form</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.ellint.ellint_carlson.h0"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_carlson.synopsis"></a></span><a class="link" href="ellint_carlson.html#math_toolkit.ellint.ellint_carlson.synopsis">Synopsis</a>
      </h5>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">ellint_rf</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span> <span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rf</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rf</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">ellint_rd</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span> <span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rd</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rd</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">ellint_rj</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span> <span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T4</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rj</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="identifier">T4</span> <span class="identifier">p</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T4</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rj</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="identifier">T4</span> <span class="identifier">p</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">ellint_rc</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span> <span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rc</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rc</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">ellint_rg</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span> <span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rg</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rg</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<h5>
<a name="math_toolkit.ellint.ellint_carlson.h1"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_carlson.description"></a></span><a class="link" href="ellint_carlson.html#math_toolkit.ellint.ellint_carlson.description">Description</a>
      </h5>
<p>
        These functions return Carlson's symmetrical elliptic integrals, the functions
        have complicated behavior over all their possible domains, but the following
        graph gives an idea of their behavior:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/ellint_carlson.svg" align="middle"></span>

        </p></blockquote></div>
<p>
        The return type of these functions is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a> when the arguments are of different
        types: otherwise the return is the same type as the arguments.
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rf</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rf</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>
</pre>
<p>
        Returns Carlson's Elliptic Integral <span class="emphasis"><em>R<sub>F</sub></em></span>:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint9.svg"></span>

        </p></blockquote></div>
<p>
        Requires that all of the arguments are non-negative, and at most one may
        be zero. Otherwise returns the result of <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rd</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rd</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>
</pre>
<p>
        Returns Carlson's elliptic integral R<sub>D</sub>:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint10.svg"></span>

        </p></blockquote></div>
<p>
        Requires that x and y are non-negative, with at most one of them zero, and
        that z &gt;= 0. Otherwise returns the result of <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T4</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rj</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="identifier">T4</span> <span class="identifier">p</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T4</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rj</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="identifier">T4</span> <span class="identifier">p</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>
</pre>
<p>
        Returns Carlson's elliptic integral R<sub>J</sub>:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint11.svg"></span>

        </p></blockquote></div>
<p>
        Requires that x, y and z are non-negative, with at most one of them zero,
        and that <span class="emphasis"><em>p != 0</em></span>. Otherwise returns the result of <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<p>
        When <span class="emphasis"><em>p &lt; 0</em></span> the function returns the <a href="http://en.wikipedia.org/wiki/Cauchy_principal_value" target="_top">Cauchy
        principal value</a> using the relation:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint17.svg"></span>

        </p></blockquote></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rc</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rc</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>
</pre>
<p>
        Returns Carlson's elliptic integral R<sub>C</sub>:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint12.svg"></span>

        </p></blockquote></div>
<p>
        Requires that <span class="emphasis"><em>x &gt; 0</em></span> and that <span class="emphasis"><em>y != 0</em></span>.
        Otherwise returns the result of <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<p>
        When <span class="emphasis"><em>y &lt; 0</em></span> the function returns the <a href="http://mathworld.wolfram.com/CauchyPrincipalValue.html" target="_top">Cauchy
        principal value</a> using the relation:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint18.svg"></span>

        </p></blockquote></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rg</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">)</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ellint_rg</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">z</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;)</span>
</pre>
<p>
        Returns Carlson's elliptic integral <span class="emphasis"><em>R<sub>G</sub>:</em></span>
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint27.svg"></span>

        </p></blockquote></div>
<p>
        Requires that x and y are non-negative, otherwise returns the result of
        <a class="link" href="../error_handling.html#math_toolkit.error_handling.domain_error">domain_error</a>.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<h5>
<a name="math_toolkit.ellint.ellint_carlson.h2"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_carlson.testing"></a></span><a class="link" href="ellint_carlson.html#math_toolkit.ellint.ellint_carlson.testing">Testing</a>
      </h5>
<p>
        There are two sets of tests.
      </p>
<p>
        Spot tests compare selected values with test data given in:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          B. C. Carlson, <span class="emphasis"><em><a href="http://arxiv.org/abs/math.CA/9409227" target="_top">Numerical
          computation of real or complex elliptic integrals</a></em></span>. Numerical
          Algorithms, Volume 10, Number 1 / March, 1995, pp 13-26.
        </p></blockquote></div>
<p>
        Random test data generated using NTL::RR at 1000-bit precision and our implementation
        checks for rounding-errors and/or regressions.
      </p>
<p>
        There are also sanity checks that use the inter-relations between the integrals
        to verify their correctness: see the above Carlson paper for details.
      </p>
<h5>
<a name="math_toolkit.ellint.ellint_carlson.h3"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_carlson.accuracy"></a></span><a class="link" href="ellint_carlson.html#math_toolkit.ellint.ellint_carlson.accuracy">Accuracy</a>
      </h5>
<p>
        These functions are computed using only basic arithmetic operations, so there
        isn't much variation in accuracy over differing platforms. Note that only
        results for the widest floating-point type on the system are given as narrower
        types have <a class="link" href="../relative_error.html#math_toolkit.relative_error.zero_error">effectively
        zero error</a>. All values are relative errors in units of epsilon.
      </p>
<div class="table">
<a name="math_toolkit.ellint.ellint_carlson.table_ellint_rc"></a><p class="title"><b>Table 8.58. Error rates for ellint_rc</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rc">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  RC: Random data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.4ε (Mean = 0.624ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.995ε (Mean = 0.433ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.962ε (Mean = 0.407ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.ellint.ellint_carlson.table_ellint_rd"></a><p class="title"><b>Table 8.59. Error rates for ellint_rd</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rd">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  RD: Random data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.59ε (Mean = 0.878ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.73ε (Mean = 0.831ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 0.803ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.896ε (Mean = 0.022ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.88ε (Mean = 0.839ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.65ε (Mean = 0.82ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 16.5ε (Mean = 0.843ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: x = y
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.824ε (Mean = 0.0272ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 3.74ε (Mean = 0.84ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.85ε (Mean = 0.865ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.51ε (Mean = 0.816ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: x = 0, y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2ε (Mean = 0.656ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.19ε (Mean = 0.522ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.16ε (Mean = 0.497ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: x = y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.03ε (Mean = 0.418ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.387ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.03ε (Mean = 0.418ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: x = 0
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.85ε (Mean = 0.781ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.79ε (Mean = 0.883ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.64ε (Mean = 0.894ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.ellint.ellint_carlson.table_ellint_rg"></a><p class="title"><b>Table 8.60. Error rates for ellint_rg</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rg">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  RG: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.983ε (Mean = 0.0172ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 0.983ε (Mean = 0.0172ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.95ε (Mean = 0.951ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.65ε (Mean = 0.929ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RG: two values 0
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RG: All values the same or zero
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.992ε (Mean = 0.288ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.06ε (Mean = 0.348ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RG: two values the same
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.594ε (Mean = 0.0103ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 0.594ε (Mean = 0.0103ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.51ε (Mean = 0.404ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.96ε (Mean = 0.374ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RG: one value zero
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.14ε (Mean = 0.722ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.96ε (Mean = 0.674ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.ellint.ellint_carlson.table_ellint_rf"></a><p class="title"><b>Table 8.61. Error rates for ellint_rf</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rf">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  RF: Random data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.73ε (Mean = 0.804ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.54ε (Mean = 0.674ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.02ε (Mean = 0.677ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RF: x = y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.999ε (Mean = 0.34ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.991ε (Mean = 0.345ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.34ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RF: x = y or y = z or x = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.536ε (Mean = 0.00658ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.89ε (Mean = 0.749ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.95ε (Mean = 0.418ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.21ε (Mean = 0.394ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RF: x = 0, y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.29ε (Mean = 0.527ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.894ε (Mean = 0.338ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.407ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RF: z = 0
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.54ε (Mean = 0.781ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.7ε (Mean = 0.539ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.89ε (Mean = 0.587ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.ellint.ellint_carlson.table_ellint_rj"></a><p class="title"><b>Table 8.62. Error rates for ellint_rj</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rj">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  RJ: Random data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.52ε (Mean = 0.0184ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 3.57ε (Mean = 0.704ε) <a class="link" href="../logs_and_tables/logs.html#errors_GNU_C_version_7_1_0_linux_double_ellint_rj_GSL_2_1_RJ_Random_data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 186ε (Mean = 6.67ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 215ε (Mean = 7.66ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RJ: 4 Equal Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.03ε (Mean = 0.418ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.387ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.03ε (Mean = 0.418ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RJ: 3 Equal Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 3.96ε (Mean = 1.06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 20.8ε (Mean = 0.986ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 39.9ε (Mean = 1.17ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RJ: 2 Equal Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.6ε (Mean = 0.0228ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.57ε (Mean = 0.754ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 220ε (Mean = 6.64ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 214ε (Mean = 5.28ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RJ: Equal z and p
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.742ε (Mean = 0.0166ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.62ε (Mean = 0.699ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 17.2ε (Mean = 1.16ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 16.1ε (Mean = 1.14ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><h5>
<a name="math_toolkit.ellint.ellint_carlson.h4"></a>
        <span class="phrase"><a name="math_toolkit.ellint.ellint_carlson.implementation"></a></span><a class="link" href="ellint_carlson.html#math_toolkit.ellint.ellint_carlson.implementation">Implementation</a>
      </h5>
<p>
        The key of Carlson's algorithm [<a class="link" href="ellint_intro.html#ellint_ref_carlson79">Carlson79</a>]
        is the duplication theorem:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint13.svg"></span>

        </p></blockquote></div>
<p>
        By applying it repeatedly, <span class="emphasis"><em>x</em></span>, <span class="emphasis"><em>y</em></span>,
        <span class="emphasis"><em>z</em></span> get closer and closer. When they are nearly equal,
        the special case equation
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint16.svg"></span>

        </p></blockquote></div>
<p>
        is used. More specifically, <span class="emphasis"><em>[R F]</em></span> is evaluated from
        a Taylor series expansion to the fifth order. The calculations of the other
        three integrals are analogous, except for R<sub>C</sub> which can be computed from elementary
        functions.
      </p>
<p>
        For <span class="emphasis"><em>p &lt; 0</em></span> in <span class="emphasis"><em>R<sub>J</sub>(x, y, z, p)</em></span>
        and <span class="emphasis"><em>y &lt; 0</em></span> in <span class="emphasis"><em>R<sub>C</sub>(x, y)</em></span>, the integrals
        are singular and their <a href="http://mathworld.wolfram.com/CauchyPrincipalValue.html" target="_top">Cauchy
        principal values</a> are returned via the relations:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint17.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ellint18.svg"></span>

        </p></blockquote></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ellint_intro.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ellint.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ellint_1.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
