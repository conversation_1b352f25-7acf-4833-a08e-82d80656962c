<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Setting the Termination Condition for Integration</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../double_exponential.html" title="Double-exponential quadrature">
<link rel="prev" href="de_exp_sinh.html" title="exp_sinh">
<link rel="next" href="de_levels.html" title="Setting the Maximum Interval Halvings and Memory Requirements">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="de_exp_sinh.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../double_exponential.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="de_levels.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.double_exponential.de_tol"></a><a class="link" href="de_tol.html" title="Setting the Termination Condition for Integration">Setting the Termination
      Condition for Integration</a>
</h3></div></div></div>
<p>
        The integrate method for all three double-exponential quadratures supports
        <span class="emphasis"><em>tolerance</em></span> argument that acts as the termination condition
        for integration.
      </p>
<p>
        The tolerance is met when two subsequent estimates of the integral have absolute
        error less than <code class="computeroutput"><span class="identifier">tolerance</span><span class="special">*</span><span class="identifier">L1</span></code>.
      </p>
<p>
        It is highly recommended that the tolerance be left at the default value
        of √ε, or something similar. Since double exponential quadrature converges
        exponentially fast for functions in Hardy spaces, then once the routine has
        <span class="bold"><strong>proved</strong></span> that the error is ~√ε, then the error
        should in fact be ~ε.
      </p>
<p>
        If you request that the error be ~ε, this tolerance might never be achieved
        (as the summation is not stabilized ala Kahan), and the routine will simply
        flounder, dividing the interval in half in order to increase the precision
        of the integrand, only to be thwarted by floating point roundoff.
      </p>
<p>
        If for some reason, the default value doesn't quite achieve full precision,
        then you could try something a little smaller such as √ε/4 or ε<sup>2/3</sup>. However,
        more likely, you need to check that your function to be integrated is able
        to return accurate values, and that there are no other issues with your integration
        scheme.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="de_exp_sinh.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../double_exponential.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="de_levels.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
