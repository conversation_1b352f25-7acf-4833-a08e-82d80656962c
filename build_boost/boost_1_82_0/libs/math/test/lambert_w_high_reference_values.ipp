

// A collection of big Lambert W test values computed using 100 decimal digits precision.
// C++ floating-point type is RealType.

// Written by I:\modular-boost\libs\math\test\lambert_w_high_reference_values.cpp Tue Oct 10 14:32:47 2017

// Copyright Paul A<PERSON> 2017.
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt
// or copy at http://www.boost.org/LICENSE_1_0.txt)

// Size of arrays of arguments z and Lambert W
static const unsigned int noof_tests = 450;

// Declare arrays of arguments z and Lambert W(z)

template <typename RealType>
static RealType zs[450];

template <typename RealType>
static RealType ws[450];
// The values are defined using the macro BOOST_MATH_TEST_VALUE to ensure
// that both built-in and multiprecision types are correctly initialized with full precision.
// built-in types like float, double require a floating-point literal like 3.14,
// but multiprecision types require a decimal digit string like "3.14".
// Numerical values are chosen to avoid exactly representable values.

template <typename RealType>
void init_zws()
{
  zs<RealType>[0] = BOOST_MATH_TEST_VALUE(RealType, 0.5999999999999999777955395074968691915273666381835937500000000000000000000000000000000000000000000000);
  ws<RealType>[0] = BOOST_MATH_TEST_VALUE(RealType, 0.4015636367870725840596232119093618401250821463765781307144089819032551434525179709162089487011544152);
  zs<RealType>[1] = BOOST_MATH_TEST_VALUE(RealType, 1.699999999999999955591079014993738383054733276367187500000000000000000000000000000000000000000000000);
  ws<RealType>[1] = BOOST_MATH_TEST_VALUE(RealType, 0.7796011225311008662356536916883580556792500749037209859530390902424444585607630246126725241921761054);
  zs<RealType>[2] = BOOST_MATH_TEST_VALUE(RealType, 2.199999999999999955591079014993738383054733276367187500000000000000000000000000000000000000000000000);
  ws<RealType>[2] = BOOST_MATH_TEST_VALUE(RealType, 0.8970741340486472832529094012483877796469756302241005331449133504770916140694317158875490176641388180);
  zs<RealType>[3] = BOOST_MATH_TEST_VALUE(RealType, 2.699999999999999955591079014993738383054733276367187500000000000000000000000000000000000000000000000);
  ws<RealType>[3] = BOOST_MATH_TEST_VALUE(RealType, 0.9966287342654774578203091807272871494846015619689842252588224928604557045580020903399140826514419525);
  zs<RealType>[4] = BOOST_MATH_TEST_VALUE(RealType, 3.199999999999999955591079014993738383054733276367187500000000000000000000000000000000000000000000000);
  ws<RealType>[4] = BOOST_MATH_TEST_VALUE(RealType, 1.083216216147652072726562700563454011684986526601600660806443834509711499483389930134543612892458143);
  zs<RealType>[5] = BOOST_MATH_TEST_VALUE(RealType, 3.699999999999999955591079014993738383054733276367187500000000000000000000000000000000000000000000000);
  ws<RealType>[5] = BOOST_MATH_TEST_VALUE(RealType, 1.159953178612022143701086341067113318247085320992117266262618001908576504625710753138135645536760705);
  zs<RealType>[6] = BOOST_MATH_TEST_VALUE(RealType, 7.899999999999999911182158029987476766109466552734375000000000000000000000000000000000000000000000000);
  ws<RealType>[6] = BOOST_MATH_TEST_VALUE(RealType, 1.598067606240196984785534680781403865575242180069554020841247076138095065416648450279328891063284787);
  zs<RealType>[7] = BOOST_MATH_TEST_VALUE(RealType, 8.399999999999999911182158029987476766109466552734375000000000000000000000000000000000000000000000000);
  ws<RealType>[7] = BOOST_MATH_TEST_VALUE(RealType, 1.635986015616761306128656680352102711658504330413761752847915150776296724715125185878789170353595843);
  zs<RealType>[8] = BOOST_MATH_TEST_VALUE(RealType, 8.899999999999999911182158029987476766109466552734375000000000000000000000000000000000000000000000000);
  ws<RealType>[8] = BOOST_MATH_TEST_VALUE(RealType, 1.672019249364215143714449427934723990551745560176619694762404483433765553308432498126823728454150807);
  zs<RealType>[9] = BOOST_MATH_TEST_VALUE(RealType, 9.399999999999999911182158029987476766109466552734375000000000000000000000000000000000000000000000000);
  ws<RealType>[9] = BOOST_MATH_TEST_VALUE(RealType, 1.706351956427362203924265356724807963736463682858697517135613748929048683757986880699421428246987716);
  zs<RealType>[10] = BOOST_MATH_TEST_VALUE(RealType, 9.899999999999999911182158029987476766109466552734375000000000000000000000000000000000000000000000000);
  ws<RealType>[10] = BOOST_MATH_TEST_VALUE(RealType, 1.739142551733351601404562031720166836256093578556407891184787967858395290655747464534386031726054901);
  zs<RealType>[11] = BOOST_MATH_TEST_VALUE(RealType, 20.29999999999999982236431605997495353221893310546875000000000000000000000000000000000000000000000000);
  ws<RealType>[11] = BOOST_MATH_TEST_VALUE(RealType, 2.215253873529547121281024099654049024258898512352506053956048219674735189566735605935868519028886428);
  zs<RealType>[12] = BOOST_MATH_TEST_VALUE(RealType, 20.79999999999999982236431605997495353221893310546875000000000000000000000000000000000000000000000000);
  ws<RealType>[12] = BOOST_MATH_TEST_VALUE(RealType, 2.232037942848570707824689153745947970289311384094147352193787995777039984911477501437729555943891179);
  zs<RealType>[13] = BOOST_MATH_TEST_VALUE(RealType, 21.29999999999999982236431605997495353221893310546875000000000000000000000000000000000000000000000000);
  ws<RealType>[13] = BOOST_MATH_TEST_VALUE(RealType, 2.248461062664032810797682669829614177334474567497410976269795770382256291948680343286055598456930946);
  zs<RealType>[14] = BOOST_MATH_TEST_VALUE(RealType, 21.79999999999999982236431605997495353221893310546875000000000000000000000000000000000000000000000000);
  ws<RealType>[14] = BOOST_MATH_TEST_VALUE(RealType, 2.264538836003641674575766487227130628592151315874095763183468118573707558576164351416415105283538250);
  zs<RealType>[15] = BOOST_MATH_TEST_VALUE(RealType, 22.29999999999999982236431605997495353221893310546875000000000000000000000000000000000000000000000000);
  ws<RealType>[15] = BOOST_MATH_TEST_VALUE(RealType, 2.280285864286149926152034464925849817265625703056626970221292432804223262557041139245862876595960246);
  zs<RealType>[16] = BOOST_MATH_TEST_VALUE(RealType, 45.09999999999999964472863211994990706443786621093750000000000000000000000000000000000000000000000000);
  ws<RealType>[16] = BOOST_MATH_TEST_VALUE(RealType, 2.784730975471531592298228296023808374578957621993717936291403085791375505867336041880555203144734624);
  zs<RealType>[17] = BOOST_MATH_TEST_VALUE(RealType, 45.59999999999999964472863211994990706443786621093750000000000000000000000000000000000000000000000000);
  ws<RealType>[17] = BOOST_MATH_TEST_VALUE(RealType, 2.792846418923343663453351656549736190861671652329623509279809945695836105896108078166742811990549890);
  zs<RealType>[18] = BOOST_MATH_TEST_VALUE(RealType, 46.09999999999999964472863211994990706443786621093750000000000000000000000000000000000000000000000000);
  ws<RealType>[18] = BOOST_MATH_TEST_VALUE(RealType, 2.800879481577357402817907432620737944445117856442267934361375865486347844977822691465905776042660153);
  zs<RealType>[19] = BOOST_MATH_TEST_VALUE(RealType, 46.59999999999999964472863211994990706443786621093750000000000000000000000000000000000000000000000000);
  ws<RealType>[19] = BOOST_MATH_TEST_VALUE(RealType, 2.808831854396542967901795549912651228215931108039763441789870364477609029775521508274395566200250327);
  zs<RealType>[20] = BOOST_MATH_TEST_VALUE(RealType, 47.09999999999999964472863211994990706443786621093750000000000000000000000000000000000000000000000000);
  ws<RealType>[20] = BOOST_MATH_TEST_VALUE(RealType, 2.816705176341098453894950633365766251645928038203952811501285665902169257597881259137118085186909385);
  zs<RealType>[21] = BOOST_MATH_TEST_VALUE(RealType, 94.69999999999999928945726423989981412887573242187500000000000000000000000000000000000000000000000000);
  ws<RealType>[21] = BOOST_MATH_TEST_VALUE(RealType, 3.343650750934026694453868349588423323594970057683998463032955783031328100668795635364027627344237744);
  zs<RealType>[22] = BOOST_MATH_TEST_VALUE(RealType, 95.19999999999999928945726423989981412887573242187500000000000000000000000000000000000000000000000000);
  ws<RealType>[22] = BOOST_MATH_TEST_VALUE(RealType, 3.347704927126314354014952127881909800322435946123852141764728005964807369865451182872647651261526300);
  zs<RealType>[23] = BOOST_MATH_TEST_VALUE(RealType, 95.69999999999999928945726423989981412887573242187500000000000000000000000000000000000000000000000000);
  ws<RealType>[23] = BOOST_MATH_TEST_VALUE(RealType, 3.351738986777429550093016062864573016038852034507491269297237351947364962103400790753443005963042748);
  zs<RealType>[24] = BOOST_MATH_TEST_VALUE(RealType, 96.19999999999999928945726423989981412887573242187500000000000000000000000000000000000000000000000000);
  ws<RealType>[24] = BOOST_MATH_TEST_VALUE(RealType, 3.355753131971562146591617562968067128167583310591520027232903513337163599725129242852363808925517286);
  zs<RealType>[25] = BOOST_MATH_TEST_VALUE(RealType, 96.69999999999999928945726423989981412887573242187500000000000000000000000000000000000000000000000000);
  ws<RealType>[25] = BOOST_MATH_TEST_VALUE(RealType, 3.359747561740841558826391853676861600267695314122615815793967179881207925371616384184099572300595115);
  zs<RealType>[26] = BOOST_MATH_TEST_VALUE(RealType, 193.8999999999999985789145284797996282577514648437500000000000000000000000000000000000000000000000000);
  ws<RealType>[26] = BOOST_MATH_TEST_VALUE(RealType, 3.905067494884853812892253268046191881524005958444131697564696414502793024379297479024372424749155738);
  zs<RealType>[27] = BOOST_MATH_TEST_VALUE(RealType, 194.3999999999999985789145284797996282577514648437500000000000000000000000000000000000000000000000000);
  ws<RealType>[27] = BOOST_MATH_TEST_VALUE(RealType, 3.907117899842841728838668195110205529083454921828289550856799078486258603076095983089478749398791291);
  zs<RealType>[28] = BOOST_MATH_TEST_VALUE(RealType, 194.8999999999999985789145284797996282577514648437500000000000000000000000000000000000000000000000000);
  ws<RealType>[28] = BOOST_MATH_TEST_VALUE(RealType, 3.909163256350964853423766659493861241213806026764267552120249985812650869611378163906691630909022161);
  zs<RealType>[29] = BOOST_MATH_TEST_VALUE(RealType, 195.3999999999999985789145284797996282577514648437500000000000000000000000000000000000000000000000000);
  ws<RealType>[29] = BOOST_MATH_TEST_VALUE(RealType, 3.911203589561939804120818106435026078510094046010650488053868908640181610985462139185885264355418070);
  zs<RealType>[30] = BOOST_MATH_TEST_VALUE(RealType, 195.8999999999999985789145284797996282577514648437500000000000000000000000000000000000000000000000000);
  ws<RealType>[30] = BOOST_MATH_TEST_VALUE(RealType, 3.913238924439855435510247090467540747905833384629853846395096935851013937767959235773727554750691716);
  zs<RealType>[31] = BOOST_MATH_TEST_VALUE(RealType, 392.2999999999999971578290569595992565155029296875000000000000000000000000000000000000000000000000000);
  ws<RealType>[31] = BOOST_MATH_TEST_VALUE(RealType, 4.473790759373999449370836932900380698603499930598355812583693103082314531706325269694063530407295371);
  zs<RealType>[32] = BOOST_MATH_TEST_VALUE(RealType, 392.7999999999999971578290569595992565155029296875000000000000000000000000000000000000000000000000000);
  ws<RealType>[32] = BOOST_MATH_TEST_VALUE(RealType, 4.474831809850706594148527116907834093420614735070734923094248837531977152944616462128228522597237267);
  zs<RealType>[33] = BOOST_MATH_TEST_VALUE(RealType, 393.2999999999999971578290569595992565155029296875000000000000000000000000000000000000000000000000000);
  ws<RealType>[33] = BOOST_MATH_TEST_VALUE(RealType, 4.475871580159411118557716684955449097432917202595361508172914971791116938813457453917365177551691682);
  zs<RealType>[34] = BOOST_MATH_TEST_VALUE(RealType, 393.7999999999999971578290569595992565155029296875000000000000000000000000000000000000000000000000000);
  ws<RealType>[34] = BOOST_MATH_TEST_VALUE(RealType, 4.476910073482067128895548362807270116617425992201334451790294796151311859601136186866422234634474300);
  zs<RealType>[35] = BOOST_MATH_TEST_VALUE(RealType, 394.2999999999999971578290569595992565155029296875000000000000000000000000000000000000000000000000000);
  ws<RealType>[35] = BOOST_MATH_TEST_VALUE(RealType, 4.477947292988721649990662195165267393745885698851422229805500000725367834662498733709056508177586635);
  zs<RealType>[36] = BOOST_MATH_TEST_VALUE(RealType, 789.0999999999999943156581139191985130310058593750000000000000000000000000000000000000000000000000000);
  ws<RealType>[36] = BOOST_MATH_TEST_VALUE(RealType, 5.051256108760896701894497014701900464234767080894666089938550993707446937559086016796794249885331870);
  zs<RealType>[37] = BOOST_MATH_TEST_VALUE(RealType, 789.5999999999999943156581139191985130310058593750000000000000000000000000000000000000000000000000000);
  ws<RealType>[37] = BOOST_MATH_TEST_VALUE(RealType, 5.051784868057603576951613257575983601901987808830448747962646970040648657993520282283444052127887316);
  zs<RealType>[38] = BOOST_MATH_TEST_VALUE(RealType, 790.0999999999999943156581139191985130310058593750000000000000000000000000000000000000000000000000000);
  ws<RealType>[38] = BOOST_MATH_TEST_VALUE(RealType, 5.052313301769511279751645919401950992205691275454166720959640667514665636089541176437079485020106302);
  zs<RealType>[39] = BOOST_MATH_TEST_VALUE(RealType, 790.5999999999999943156581139191985130310058593750000000000000000000000000000000000000000000000000000);
  ws<RealType>[39] = BOOST_MATH_TEST_VALUE(RealType, 5.052841410301356078567942312344300154667895407100576839086958094315459736887113324816418833061471414);
  zs<RealType>[40] = BOOST_MATH_TEST_VALUE(RealType, 791.0999999999999943156581139191985130310058593750000000000000000000000000000000000000000000000000000);
  ws<RealType>[40] = BOOST_MATH_TEST_VALUE(RealType, 5.053369194057116916125095352093384287199494222654442252497641737159855576046108292479434106853949424);
  zs<RealType>[41] = BOOST_MATH_TEST_VALUE(RealType, 1582.699999999999988631316227838397026062011718750000000000000000000000000000000000000000000000000000);
  ws<RealType>[41] = BOOST_MATH_TEST_VALUE(RealType, 5.637454835061548840266762838608075961241621341710147974330149448636747596037634477093772466715414237);
  zs<RealType>[42] = BOOST_MATH_TEST_VALUE(RealType, 1583.199999999999988631316227838397026062011718750000000000000000000000000000000000000000000000000000);
  ws<RealType>[42] = BOOST_MATH_TEST_VALUE(RealType, 5.637723113558296965227371679987485292172631743514584329014143690566703873701821724552724491259434917);
  zs<RealType>[43] = BOOST_MATH_TEST_VALUE(RealType, 1583.699999999999988631316227838397026062011718750000000000000000000000000000000000000000000000000000);
  ws<RealType>[43] = BOOST_MATH_TEST_VALUE(RealType, 5.637991309264171414360659662528863849150184273111159470910102371892495978961903358821307687677635837);
  zs<RealType>[44] = BOOST_MATH_TEST_VALUE(RealType, 1584.199999999999988631316227838397026062011718750000000000000000000000000000000000000000000000000000);
  ws<RealType>[44] = BOOST_MATH_TEST_VALUE(RealType, 5.638259422230692585112176850810679719906543165397130936013277468573934642794738015313302920143170105);
  zs<RealType>[45] = BOOST_MATH_TEST_VALUE(RealType, 1584.699999999999988631316227838397026062011718750000000000000000000000000000000000000000000000000000);
  ws<RealType>[45] = BOOST_MATH_TEST_VALUE(RealType, 5.638527452509332631758526797455952459448323578747282813479742362912515476029842704193513987433394644);
  zs<RealType>[46] = BOOST_MATH_TEST_VALUE(RealType, 3169.899999999999977262632455676794052124023437500000000000000000000000000000000000000000000000000000);
  ws<RealType>[46] = BOOST_MATH_TEST_VALUE(RealType, 6.231791473267630119617879242959215790063493036893226016268939602113573649847295482162112707750032205);
  zs<RealType>[47] = BOOST_MATH_TEST_VALUE(RealType, 3170.399999999999977262632455676794052124023437500000000000000000000000000000000000000000000000000000);
  ws<RealType>[47] = BOOST_MATH_TEST_VALUE(RealType, 6.231927385287213295870608336780291881793156334477048126729390997875392137109426481813143445559537548);
  zs<RealType>[48] = BOOST_MATH_TEST_VALUE(RealType, 3170.899999999999977262632455676794052124023437500000000000000000000000000000000000000000000000000000);
  ws<RealType>[48] = BOOST_MATH_TEST_VALUE(RealType, 6.232063276283731898910298571907046320310117335045161587714712693774590117520030116772150624981197754);
  zs<RealType>[49] = BOOST_MATH_TEST_VALUE(RealType, 3171.399999999999977262632455676794052124023437500000000000000000000000000000000000000000000000000000);
  ws<RealType>[49] = BOOST_MATH_TEST_VALUE(RealType, 6.232199146263736642645449823754129665166787360756180379390602947566154676495717638352300033758537319);
  zs<RealType>[50] = BOOST_MATH_TEST_VALUE(RealType, 3171.899999999999977262632455676794052124023437500000000000000000000000000000000000000000000000000000);
  ws<RealType>[50] = BOOST_MATH_TEST_VALUE(RealType, 6.232334995233775170638824027293638315162522524016381054824777727129130712726670908695639061242466679);
  zs<RealType>[51] = BOOST_MATH_TEST_VALUE(RealType, 6344.299999999999954525264911353588104248046875000000000000000000000000000000000000000000000000000000);
  ws<RealType>[51] = BOOST_MATH_TEST_VALUE(RealType, 6.833478246863882246641726870717734354293613258312600642204509694006101911629409462573891987188902394);
  zs<RealType>[52] = BOOST_MATH_TEST_VALUE(RealType, 6344.799999999999954525264911353588104248046875000000000000000000000000000000000000000000000000000000);
  ws<RealType>[52] = BOOST_MATH_TEST_VALUE(RealType, 6.833546994320183648585668463184962739224514055009742694319567301283431438840148992714833003589320204);
  zs<RealType>[53] = BOOST_MATH_TEST_VALUE(RealType, 6345.299999999999954525264911353588104248046875000000000000000000000000000000000000000000000000000000);
  ws<RealType>[53] = BOOST_MATH_TEST_VALUE(RealType, 6.833615736447355580644330241871199203480309206612453771835338393474744796397760691906867357518286300);
  zs<RealType>[54] = BOOST_MATH_TEST_VALUE(RealType, 6345.799999999999954525264911353588104248046875000000000000000000000000000000000000000000000000000000);
  ws<RealType>[54] = BOOST_MATH_TEST_VALUE(RealType, 6.833684473246229472880513750644790479773339419112019177698673979527062571403548057152859520971070532);
  zs<RealType>[55] = BOOST_MATH_TEST_VALUE(RealType, 6346.299999999999954525264911353588104248046875000000000000000000000000000000000000000000000000000000);
  ws<RealType>[55] = BOOST_MATH_TEST_VALUE(RealType, 6.833753204717636560302304979968870884092145760178319785601850619423465965447586003582575169154068642);
  zs<RealType>[56] = BOOST_MATH_TEST_VALUE(RealType, 12693.09999999999990905052982270717620849609375000000000000000000000000000000000000000000000000000000);
  ws<RealType>[56] = BOOST_MATH_TEST_VALUE(RealType, 7.441712782644182656362567954733218054310136462342348107691598780010100425506115311857309428345451583);
  zs<RealType>[57] = BOOST_MATH_TEST_VALUE(RealType, 12693.59999999999990905052982270717620849609375000000000000000000000000000000000000000000000000000000);
  ws<RealType>[57] = BOOST_MATH_TEST_VALUE(RealType, 7.441747507160214264985418199644937473985616288224663570878672057496608603535012780430207292902251849);
  zs<RealType>[58] = BOOST_MATH_TEST_VALUE(RealType, 12694.09999999999990905052982270717620849609375000000000000000000000000000000000000000000000000000000);
  ws<RealType>[58] = BOOST_MATH_TEST_VALUE(RealType, 7.441782230327669457854270012077867036991216446419568353636383337201042455550854674638301527917381460);
  zs<RealType>[59] = BOOST_MATH_TEST_VALUE(RealType, 12694.59999999999990905052982270717620849609375000000000000000000000000000000000000000000000000000000);
  ws<RealType>[59] = BOOST_MATH_TEST_VALUE(RealType, 7.441816952146653566134582734750438863756337927374408689634478920961001428844890048307273438220285103);
  zs<RealType>[60] = BOOST_MATH_TEST_VALUE(RealType, 12695.09999999999990905052982270717620849609375000000000000000000000000000000000000000000000000000000);
  ws<RealType>[60] = BOOST_MATH_TEST_VALUE(RealType, 7.441851672617271908624631672809964354572668086864496527202255560220860635169893350319392062176172956);
  zs<RealType>[61] = BOOST_MATH_TEST_VALUE(RealType, 25390.69999999999981810105964541435241699218750000000000000000000000000000000000000000000000000000000);
  ws<RealType>[61] = BOOST_MATH_TEST_VALUE(RealType, 8.055751887730669404078767770906158058132806188168812278807914210510560983286915475256358360988722249);
  zs<RealType>[62] = BOOST_MATH_TEST_VALUE(RealType, 25391.19999999999981810105964541435241699218750000000000000000000000000000000000000000000000000000000);
  ws<RealType>[62] = BOOST_MATH_TEST_VALUE(RealType, 8.055769405252722224984585767512155671001638813031160420183226603705765994004819084590112882738598672);
  zs<RealType>[63] = BOOST_MATH_TEST_VALUE(RealType, 25391.69999999999981810105964541435241699218750000000000000000000000000000000000000000000000000000000);
  ws<RealType>[63] = BOOST_MATH_TEST_VALUE(RealType, 8.055786922434032119761416627590837471869427434519755833207927697913643222261584517182449368833198187);
  zs<RealType>[64] = BOOST_MATH_TEST_VALUE(RealType, 25392.19999999999981810105964541435241699218750000000000000000000000000000000000000000000000000000000);
  ws<RealType>[64] = BOOST_MATH_TEST_VALUE(RealType, 8.055804439274612409649066171031328348764224415259811919589560542548689157122598047317682477881428519);
  zs<RealType>[65] = BOOST_MATH_TEST_VALUE(RealType, 25392.69999999999981810105964541435241699218750000000000000000000000000000000000000000000000000000000);
  ws<RealType>[65] = BOOST_MATH_TEST_VALUE(RealType, 8.055821955774476415104661363464701307020928082642356236080373490396966544402792637267897239283500728);
  zs<RealType>[66] = BOOST_MATH_TEST_VALUE(RealType, 50785.89999999999963620211929082870483398437500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[66] = BOOST_MATH_TEST_VALUE(RealType, 8.674936078635983017984123951954366186973716699495736629864279614242284857757227515949355498579742855);
  zs<RealType>[67] = BOOST_MATH_TEST_VALUE(RealType, 50786.39999999999963620211929082870483398437500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[67] = BOOST_MATH_TEST_VALUE(RealType, 8.674944906241453706059825549964349902974004039927040359264744515026031399261972506772892769382818801);
  zs<RealType>[68] = BOOST_MATH_TEST_VALUE(RealType, 50786.89999999999963620211929082870483398437500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[68] = BOOST_MATH_TEST_VALUE(RealType, 8.674953733760944136535950689333928914142360745655931032041490757092023706394578714640748394257288035);
  zs<RealType>[69] = BOOST_MATH_TEST_VALUE(RealType, 50787.39999999999963620211929082870483398437500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[69] = BOOST_MATH_TEST_VALUE(RealType, 8.674962561194455991628227539852952953077017660325256919933499525371091080399080487843972214866128384);
  zs<RealType>[70] = BOOST_MATH_TEST_VALUE(RealType, 50787.89999999999963620211929082870483398437500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[70] = BOOST_MATH_TEST_VALUE(RealType, 8.674971388541990953502931535257895802702731784533955912147060505216027254503478909260086842672435376);
  zs<RealType>[71] = BOOST_MATH_TEST_VALUE(RealType, 101576.2999999999992724042385816574096679687500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[71] = BOOST_MATH_TEST_VALUE(RealType, 9.298691796027940703796635264110043143787629375260023629778966472817740701497549236671005785312535137);
  zs<RealType>[72] = BOOST_MATH_TEST_VALUE(RealType, 101576.7999999999992724042385816574096679687500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[72] = BOOST_MATH_TEST_VALUE(RealType, 9.298696240460783074349599408079465706595862952879886273836096426172349132276424038993850275528909958);
  zs<RealType>[73] = BOOST_MATH_TEST_VALUE(RealType, 101577.2999999999992724042385816574096679687500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[73] = BOOST_MATH_TEST_VALUE(RealType, 9.298700684871954559132336105768526810554925567844384854912235341295989207897852971537611849804454104);
  zs<RealType>[74] = BOOST_MATH_TEST_VALUE(RealType, 101577.7999999999992724042385816574096679687500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[74] = BOOST_MATH_TEST_VALUE(RealType, 9.298705129261455370304348674457005008539407510001142864340543902043967324875967473097043636378431136);
  zs<RealType>[75] = BOOST_MATH_TEST_VALUE(RealType, 101578.2999999999992724042385816574096679687500000000000000000000000000000000000000000000000000000000);
  ws<RealType>[75] = BOOST_MATH_TEST_VALUE(RealType, 9.298709573629285720022020159137488905547397121761906103664298171650800509043504137427221381746219971);
  zs<RealType>[76] = BOOST_MATH_TEST_VALUE(RealType, 203157.0999999999985448084771633148193359375000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[76] = BOOST_MATH_TEST_VALUE(RealType, 9.926524439637924502814640096432844232405010548632138451412344878257773957471037720806722776924379539);
  zs<RealType>[77] = BOOST_MATH_TEST_VALUE(RealType, 203157.5999999999985448084771633148193359375000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[77] = BOOST_MATH_TEST_VALUE(RealType, 9.926526675539305999408212665862555525393765588240345090590079743764108100617622492751979808963302929);
  zs<RealType>[78] = BOOST_MATH_TEST_VALUE(RealType, 203158.0999999999985448084771633148193359375000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[78] = BOOST_MATH_TEST_VALUE(RealType, 9.926528911435230720557328431324006644047736665798752128461117587165401958858923779103702189649205849);
  zs<RealType>[79] = BOOST_MATH_TEST_VALUE(RealType, 203158.5999999999985448084771633148193359375000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[79] = BOOST_MATH_TEST_VALUE(RealType, 9.926531147325698692990351270769682423599587366983008197322280565618502972980495987784743296088232692);
  zs<RealType>[80] = BOOST_MATH_TEST_VALUE(RealType, 203159.0999999999985448084771633148193359375000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[80] = BOOST_MATH_TEST_VALUE(RealType, 9.926533383210709943435448417027560593313394713874207050608325275972416603539435687443617960861779968);
  zs<RealType>[81] = BOOST_MATH_TEST_VALUE(RealType, 406318.6999999999970896169543266296386718750000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[81] = BOOST_MATH_TEST_VALUE(RealType, 10.55800844020678300858930169345380918056498196515847559789815847210932705985905912177836375627483410);
  zs<RealType>[82] = BOOST_MATH_TEST_VALUE(RealType, 406319.1999999999970896169543266296386718750000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[82] = BOOST_MATH_TEST_VALUE(RealType, 10.55800956429896255310197650482898317196991260081559124902272662470413320348092630336068243562420612);
  zs<RealType>[83] = BOOST_MATH_TEST_VALUE(RealType, 406319.6999999999970896169543266296386718750000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[83] = BOOST_MATH_TEST_VALUE(RealType, 10.55801068838976919073174818917086439876248092007955187694896892558084843931726958729264479171526918);
  zs<RealType>[84] = BOOST_MATH_TEST_VALUE(RealType, 406320.1999999999970896169543266296386718750000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[84] = BOOST_MATH_TEST_VALUE(RealType, 10.55801181247920292484283725870748463023358051483533901203380901404123382789184489956279108152225757);
  zs<RealType>[85] = BOOST_MATH_TEST_VALUE(RealType, 406320.6999999999970896169543266296386718750000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[85] = BOOST_MATH_TEST_VALUE(RealType, 10.55801293656726375879945184504060224737500313478807503007007909767698116683614226527139047633989602);
  zs<RealType>[86] = BOOST_MATH_TEST_VALUE(RealType, 812641.8999999999941792339086532592773437500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[86] = BOOST_MATH_TEST_VALUE(RealType, 11.19277715105303335957380751089185510982783382888615163475399731608447432939046539739780186010205819);
  zs<RealType>[87] = BOOST_MATH_TEST_VALUE(RealType, 812642.3999999999941792339086532592773437500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[87] = BOOST_MATH_TEST_VALUE(RealType, 11.19277771586759068427927907791232978730718798905802091714667366555094578546313245757715733663116681);
  zs<RealType>[88] = BOOST_MATH_TEST_VALUE(RealType, 812642.8999999999941792339086532592773437500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[88] = BOOST_MATH_TEST_VALUE(RealType, 11.19277828068180282941234300813602678219028255437023366225732009891673392268727353365317316060216307);
  zs<RealType>[89] = BOOST_MATH_TEST_VALUE(RealType, 812643.3999999999941792339086532592773437500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[89] = BOOST_MATH_TEST_VALUE(RealType, 11.19277884549566979539611569279587635086022997209586259427933274111486838234969448101548392041897529);
  zs<RealType>[90] = BOOST_MATH_TEST_VALUE(RealType, 812643.8999999999941792339086532592773437500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[90] = BOOST_MATH_TEST_VALUE(RealType, 11.19277941030919158265371274430325256214137887195738215091099754333691681726015244339566835725917388);
  zs<RealType>[91] = BOOST_MATH_TEST_VALUE(RealType, 1625288.299999999988358467817306518554687500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[91] = BOOST_MATH_TEST_VALUE(RealType, 11.83051367496350653298055098412234220179378826002120373759410524883137483448021534372309124250280047);
  zs<RealType>[92] = BOOST_MATH_TEST_VALUE(RealType, 1625288.799999999988358467817306518554687500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[92] = BOOST_MATH_TEST_VALUE(RealType, 11.83051395862415209457082318977971516657969184941726425523617075901029866766828046681793457310563883);
  zs<RealType>[93] = BOOST_MATH_TEST_VALUE(RealType, 1625289.299999999988358467817306518554687500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[93] = BOOST_MATH_TEST_VALUE(RealType, 11.83051424228471092157568395260197960555928287686416745291625975894259102434581858031780382895984480);
  zs<RealType>[94] = BOOST_MATH_TEST_VALUE(RealType, 1625289.799999999988358467817306518554687500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[94] = BOOST_MATH_TEST_VALUE(RealType, 11.83051452594518301404831336917713066668156156528094910312372347647232811359765848597839160347350790);
  zs<RealType>[95] = BOOST_MATH_TEST_VALUE(RealType, 1625290.299999999988358467817306518554687500000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[95] = BOOST_MATH_TEST_VALUE(RealType, 11.83051480960556837204189148713503938528677530351797077101569714537002853052165595070586086051268476);
  zs<RealType>[96] = BOOST_MATH_TEST_VALUE(RealType, 3250581.099999999976716935634613037109375000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[96] = BOOST_MATH_TEST_VALUE(RealType, 12.47094296296818886963926120402441135953658337315101944303084986533714570937576415675358344893956724);
  zs<RealType>[97] = BOOST_MATH_TEST_VALUE(RealType, 3250581.599999999976716935634613037109375000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[97] = BOOST_MATH_TEST_VALUE(RealType, 12.47094310536827791354684005276581162972597260940308218544073416384666193653508004167640448380153389);
  zs<RealType>[98] = BOOST_MATH_TEST_VALUE(RealType, 3250582.099999999976716935634613037109375000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[98] = BOOST_MATH_TEST_VALUE(RealType, 12.47094324776834517437426924689730750513918775389042001111365883776867938003401942562632403665303867);
  zs<RealType>[99] = BOOST_MATH_TEST_VALUE(RealType, 3250582.599999999976716935634613037109375000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[99] = BOOST_MATH_TEST_VALUE(RealType, 12.47094339016839065212822905567651460418204106065566910956134121802725695306834966790193342511971825);
  zs<RealType>[100] = BOOST_MATH_TEST_VALUE(RealType, 3250583.099999999976716935634613037109375000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[100] = BOOST_MATH_TEST_VALUE(RealType, 12.47094353256841434681539974528531469757660901993281429423438578866652694560046559516127568892277702);
  zs<RealType>[101] = BOOST_MATH_TEST_VALUE(RealType, 6501166.699999999953433871269226074218750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[101] = BOOST_MATH_TEST_VALUE(RealType, 13.11382518009820116753812472043031659983277915902361747977758246698818963218870351102587174635468138);
  zs<RealType>[102] = BOOST_MATH_TEST_VALUE(RealType, 6501167.199999999953433871269226074218750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[102] = BOOST_MATH_TEST_VALUE(RealType, 13.11382525155825542039438232302300244148043560690215658478061719482590950005701713995387903749679862);
  zs<RealType>[103] = BOOST_MATH_TEST_VALUE(RealType, 6501167.699999999953433871269226074218750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[103] = BOOST_MATH_TEST_VALUE(RealType, 13.11382532301830420490055394669714665225378812412907655382308789130810346826183238238494205026247934);
  zs<RealType>[104] = BOOST_MATH_TEST_VALUE(RealType, 6501168.199999999953433871269226074218750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[104] = BOOST_MATH_TEST_VALUE(RealType, 13.11382539447834752105747833428094533284492363009218919720913636223924531666313533113069940376003390);
  zs<RealType>[105] = BOOST_MATH_TEST_VALUE(RealType, 6501168.699999999953433871269226074218750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[105] = BOOST_MATH_TEST_VALUE(RealType, 13.11382546593838536886599422840946554656689837543146085874754543863703505630372640720269904462657597);
  zs<RealType>[106] = BOOST_MATH_TEST_VALUE(RealType, 13002337.89999999990686774253845214843750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[106] = BOOST_MATH_TEST_VALUE(RealType, 13.75895020161120780671931348674820949097896740529525960699598966308825998645623474235280989608207426);
  zs<RealType>[107] = BOOST_MATH_TEST_VALUE(RealType, 13002338.39999999990686774253845214843750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[107] = BOOST_MATH_TEST_VALUE(RealType, 13.75895023746031789528316024226987524061812679192459530422102497447151229457148397431817422821296573);
  zs<RealType>[108] = BOOST_MATH_TEST_VALUE(RealType, 13002338.89999999990686774253845214843750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[108] = BOOST_MATH_TEST_VALUE(RealType, 13.75895027330942661161180008030275959022186764318551714315248259172940117391346074981407903526666876);
  zs<RealType>[109] = BOOST_MATH_TEST_VALUE(RealType, 13002339.39999999990686774253845214843750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[109] = BOOST_MATH_TEST_VALUE(RealType, 13.75895030915853395570533826541548036105186387000942779022045956615233350302797407587096919814230872);
  zs<RealType>[110] = BOOST_MATH_TEST_VALUE(RealType, 13002339.89999999990686774253845214843750000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[110] = BOOST_MATH_TEST_VALUE(RealType, 13.75895034500763992756388006216453398588380356491793568993934227427809822610433476168943506890083264);
  zs<RealType>[111] = BOOST_MATH_TEST_VALUE(RealType, 26004680.29999999981373548507690429687500000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[111] = BOOST_MATH_TEST_VALUE(RealType, 14.40613306792050284417781173963237397180782490395810703112834440216893801894568593170935318229410890);
  zs<RealType>[112] = BOOST_MATH_TEST_VALUE(RealType, 26004680.79999999981373548507690429687500000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[112] = BOOST_MATH_TEST_VALUE(RealType, 14.40613308589978129513196522846261186416164678471600282263899627087138088581441641199768735966026721);
  zs<RealType>[113] = BOOST_MATH_TEST_VALUE(RealType, 26004681.29999999981373548507690429687500000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[113] = BOOST_MATH_TEST_VALUE(RealType, 14.40613310387905940184947896220802688669394773806882218972392634458869861468933663343960564575146049);
  zs<RealType>[114] = BOOST_MATH_TEST_VALUE(RealType, 26004681.79999999981373548507690429687500000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[114] = BOOST_MATH_TEST_VALUE(RealType, 14.40613312185833716433036614707023490965167453502978925783310059180078511451175950772072935073580417);
  zs<RealType>[115] = BOOST_MATH_TEST_VALUE(RealType, 26004682.29999999981373548507690429687500000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[115] = BOOST_MATH_TEST_VALUE(RealType, 14.40613313983761458257463998925009132119130606601627537868443064669880766892972378195587657468067971);
  zs<RealType>[116] = BOOST_MATH_TEST_VALUE(RealType, 52009365.09999999962747097015380859375000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[116] = BOOST_MATH_TEST_VALUE(RealType, 15.05521023228726997012482201156518637282193264830919527945406914876568997920242700346221079695380120);
  zs<RealType>[117] = BOOST_MATH_TEST_VALUE(RealType, 52009365.59999999962747097015380859375000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[117] = BOOST_MATH_TEST_VALUE(RealType, 15.05521024130213601448008511719004686304317399587148372739532783438679535880037848363163034299000680);
  zs<RealType>[118] = BOOST_MATH_TEST_VALUE(RealType, 52009366.09999999962747097015380859375000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[118] = BOOST_MATH_TEST_VALUE(RealType, 15.05521025031700197250576749203130491372164140425152090159289103953022449076855552216703380049236381);
  zs<RealType>[119] = BOOST_MATH_TEST_VALUE(RealType, 52009366.59999999962747097015380859375000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[119] = BOOST_MATH_TEST_VALUE(RealType, 15.05521025933186784420187079237695573264884951878394949291027777307830551044730470942903907859985247);
  zs<RealType>[120] = BOOST_MATH_TEST_VALUE(RealType, 52009367.09999999962747097015380859375000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[120] = BOOST_MATH_TEST_VALUE(RealType, 15.05521026834673362956839667451494683193779819588967935304969871800597790007309597120010839282494067);
  zs<RealType>[121] = BOOST_MATH_TEST_VALUE(RealType, 104018734.6999999992549419403076171875000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[121] = BOOST_MATH_TEST_VALUE(RealType, 15.70603645611954384988565703849374122026373428508755081783343271665282590664826044624943527766443724);
  zs<RealType>[122] = BOOST_MATH_TEST_VALUE(RealType, 104018735.1999999992549419403076171875000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[122] = BOOST_MATH_TEST_VALUE(RealType, 15.70603646063864032656975241668975461127107196165116494804371210729046808944447344810561150619338235);
  zs<RealType>[123] = BOOST_MATH_TEST_VALUE(RealType, 104018735.6999999992549419403076171875000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[123] = BOOST_MATH_TEST_VALUE(RealType, 15.70603646515773678160916860265187423935628996612114399500020977395299556819648453302891654761783891);
  zs<RealType>[124] = BOOST_MATH_TEST_VALUE(RealType, 104018736.1999999992549419403076171875000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[124] = BOOST_MATH_TEST_VALUE(RealType, 15.70603646967683321500390580404963095388245044851589624040628425347772179453119739096056755672581358);
  zs<RealType>[125] = BOOST_MATH_TEST_VALUE(RealType, 104018736.6999999992549419403076171875000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[125] = BOOST_MATH_TEST_VALUE(RealType, 15.70603647419592962675396422855255261373445463144900248624737121930916887983366690742461452872492017);
  zs<RealType>[126] = BOOST_MATH_TEST_VALUE(RealType, 208037473.8999999985098838806152343750000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[126] = BOOST_MATH_TEST_VALUE(RealType, 16.35848223073077299062251727505489121683259009838940921798588655999804611259258002812224135777088225);
  zs<RealType>[127] = BOOST_MATH_TEST_VALUE(RealType, 208037474.3999999985098838806152343750000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[127] = BOOST_MATH_TEST_VALUE(RealType, 16.35848223299572857263748180340545017346327532474491820120184954137107185953921166019993001720173076);
  zs<RealType>[128] = BOOST_MATH_TEST_VALUE(RealType, 208037474.8999999985098838806152343750000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[128] = BOOST_MATH_TEST_VALUE(RealType, 16.35848223526068414922688843525885798316030222323873469187325647317943052465221773791004107044449214);
  zs<RealType>[129] = BOOST_MATH_TEST_VALUE(RealType, 208037475.3999999985098838806152343750000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[129] = BOOST_MATH_TEST_VALUE(RealType, 16.35848223752563972039073719664683805774368875669934675288398977599557153297961083834670781371769393);
  zs<RealType>[130] = BOOST_MATH_TEST_VALUE(RealType, 208037475.8999999985098838806152343750000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[130] = BOOST_MATH_TEST_VALUE(RealType, 16.35848223979059528612902811360111362158185991940560941697071272367978154194499902342370856607708754);
  zs<RealType>[131] = BOOST_MATH_TEST_VALUE(RealType, 416074952.2999999970197677612304687500000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[131] = BOOST_MATH_TEST_VALUE(RealType, 17.01243162682130034575873110256608416240774650633244657569787078893008324146198524461655542099298038);
  zs<RealType>[132] = BOOST_MATH_TEST_VALUE(RealType, 416074952.7999999970197677612304687500000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[132] = BOOST_MATH_TEST_VALUE(RealType, 17.01243162795629150708082853641104106051019632512982699681254455598867301594442867945136512864263362);
  zs<RealType>[133] = BOOST_MATH_TEST_VALUE(RealType, 416074953.2999999970197677612304687500000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[133] = BOOST_MATH_TEST_VALUE(RealType, 17.01243162909128266704320348800926473937585791237790855606445141461836852095077452961626092636714582);
  zs<RealType>[134] = BOOST_MATH_TEST_VALUE(RealType, 416074953.7999999970197677612304687500000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[134] = BOOST_MATH_TEST_VALUE(RealType, 17.01243163022627382564585596062316405185836375654028401358545369509940490853717120734831332950839378);
  zs<RealType>[135] = BOOST_MATH_TEST_VALUE(RealType, 416074954.2999999970197677612304687500000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[135] = BOOST_MATH_TEST_VALUE(RealType, 17.01243163136126498288878595751514783906405969399381198927824453551799680870112429531524827406849599);
  zs<RealType>[136] = BOOST_MATH_TEST_VALUE(RealType, 832149909.0999999940395355224609375000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[136] = BOOST_MATH_TEST_VALUE(RealType, 17.66778049214031531812887566464334167360339296689778348663603985291368769599603595003791335301847127);
  zs<RealType>[137] = BOOST_MATH_TEST_VALUE(RealType, 832149909.5999999940395355224609375000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[137] = BOOST_MATH_TEST_VALUE(RealType, 17.66778049270898194728430180770507660352501688482825963526508387721596470881802640086432444438183220);
  zs<RealType>[138] = BOOST_MATH_TEST_VALUE(RealType, 832149910.0999999940395355224609375000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[138] = BOOST_MATH_TEST_VALUE(RealType, 17.66778049327764857609902322798165858268194375878492382051153135096018706001421025620618302837247343);
  zs<RealType>[139] = BOOST_MATH_TEST_VALUE(RealType, 832149910.5999999940395355224609375000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[139] = BOOST_MATH_TEST_VALUE(RealType, 17.66778049384631520457303992588186753608991034689224716778630078855718157314806355505907287211879952);
  zs<RealType>[140] = BOOST_MATH_TEST_VALUE(RealType, 832149911.0999999940395355224609375000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[140] = BOOST_MATH_TEST_VALUE(RealType, 17.66778049441498183270635190181448338802862077472029294388350982070482138347224074032285443619695132);
  zs<RealType>[141] = BOOST_MATH_TEST_VALUE(RealType, 1664299822.699999988079071044921875000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[141] = BOOST_MATH_TEST_VALUE(RealType, 18.32443493355098619384682217293063408860906200997313752841900524481309067580120995737616551816092439);
  zs<RealType>[142] = BOOST_MATH_TEST_VALUE(RealType, 1664299823.199999988079071044921875000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[142] = BOOST_MATH_TEST_VALUE(RealType, 18.32443493383586636729058593564510700968056859723407482295764411091295912034128445270765389474245827);
  zs<RealType>[143] = BOOST_MATH_TEST_VALUE(RealType, 1664299823.699999988079071044921875000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[143] = BOOST_MATH_TEST_VALUE(RealType, 18.32443493412074654064899329117538024263559388031556198852503241876369903917896474239140595505063322);
  zs<RealType>[144] = BOOST_MATH_TEST_VALUE(RealType, 1664299824.199999988079071044921875000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[144] = BOOST_MATH_TEST_VALUE(RealType, 18.32443493440562671392204423957266503795757499599462266552348787036107193130165331139452279532396827);
  zs<RealType>[145] = BOOST_MATH_TEST_VALUE(RealType, 1664299824.699999988079071044921875000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[145] = BOOST_MATH_TEST_VALUE(RealType, 18.32443493469050688710973878088817264608384098178071957390837672366993756271957964256600743216105241);
  zs<RealType>[146] = BOOST_MATH_TEST_VALUE(RealType, 3328599649.899999976158142089843750000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[146] = BOOST_MATH_TEST_VALUE(RealType, 18.98231003239481891260092191504387611307556374245363459040509191182476364522292852639675828304549626);
  zs<RealType>[147] = BOOST_MATH_TEST_VALUE(RealType, 3328599650.399999976158142089843750000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[147] = BOOST_MATH_TEST_VALUE(RealType, 18.98231003253751491629227276099735868489913807533078209230206170806009818301507483588395812018180071);
  zs<RealType>[148] = BOOST_MATH_TEST_VALUE(RealType, 3328599650.899999976158142089843750000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[148] = BOOST_MATH_TEST_VALUE(RealType, 18.98231003268021091996224244869947622441697585814089764491702992369913535253679093973930949506347048);
  zs<RealType>[149] = BOOST_MATH_TEST_VALUE(RealType, 3328599651.399999976158142089843750000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[149] = BOOST_MATH_TEST_VALUE(RealType, 18.98231003282290692361083097815664339084466388779045270504033488281798754360146784756669433227860478);
  zs<RealType>[150] = BOOST_MATH_TEST_VALUE(RealType, 3328599651.899999976158142089843750000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[150] = BOOST_MATH_TEST_VALUE(RealType, 18.98231003296560292723803834937527484339490103369072191109295056853780039115858285449484374166727442);
  zs<RealType>[151] = BOOST_MATH_TEST_VALUE(RealType, 6657199304.299999952316284179687500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[151] = BOOST_MATH_TEST_VALUE(RealType, 19.64132875213179003616975291758951989874401990433758371552185122694920942525627888352145273462707829);
  zs<RealType>[152] = BOOST_MATH_TEST_VALUE(RealType, 6657199304.799999952316284179687500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[152] = BOOST_MATH_TEST_VALUE(RealType, 19.64132875220325804117690436378168767212426802746287406741611709213764258286377104459764498688864645);
  zs<RealType>[153] = BOOST_MATH_TEST_VALUE(RealType, 6657199305.299999952316284179687500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[153] = BOOST_MATH_TEST_VALUE(RealType, 19.64132875227472604617870068525483911300930564165365547681598983747537164183343994434536282821758671);
  zs<RealType>[154] = BOOST_MATH_TEST_VALUE(RealType, 6657199305.799999952316284179687500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[154] = BOOST_MATH_TEST_VALUE(RealType, 19.64132875234619405117514188200977760121223842398566254787219879319150457646967224084449244020129346);
  zs<RealType>[155] = BOOST_MATH_TEST_VALUE(RealType, 6657199306.299999952316284179687500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[155] = BOOST_MATH_TEST_VALUE(RealType, 19.64132875241766205616622795404730651654599119614988454540132958996734683240549999645255105620924194);
  zs<RealType>[156] = BOOST_MATH_TEST_VALUE(RealType, 13314398613.09999990463256835937500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[156] = BOOST_MATH_TEST_VALUE(RealType, 20.30142100521655914391255442878301089651369196196103134174539854119020213383927831733183603430177342);
  zs<RealType>[157] = BOOST_MATH_TEST_VALUE(RealType, 13314398613.59999990463256835937500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[157] = BOOST_MATH_TEST_VALUE(RealType, 20.30142100525234952404035395706360529741527156245298468796715007831095483401537550300887615324478886);
  zs<RealType>[158] = BOOST_MATH_TEST_VALUE(RealType, 13314398614.09999990463256835937500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[158] = BOOST_MATH_TEST_VALUE(RealType, 20.30142100528813990416681239948722494340433727853716710673987742922492342629617928698493424484032660);
  zs<RealType>[159] = BOOST_MATH_TEST_VALUE(RealType, 13314398614.59999990463256835937500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[159] = BOOST_MATH_TEST_VALUE(RealType, 20.30142100532393028429192975605397043801464163741054316603439612006945607064743419379856370010506017);
  zs<RealType>[160] = BOOST_MATH_TEST_VALUE(RealType, 13314398615.09999990463256835937500000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[160] = BOOST_MATH_TEST_VALUE(RealType, 20.30142100535972066441570602676394238477992584177232532005791683216204582260476049842371709123221255);
  zs<RealType>[161] = BOOST_MATH_TEST_VALUE(RealType, 26628797230.69999980926513671875000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[161] = BOOST_MATH_TEST_VALUE(RealType, 20.96252285249100030290397518601237584604981130278135272151318314970663709603528431386852754379112730);
  zs<RealType>[162] = BOOST_MATH_TEST_VALUE(RealType, 26628797231.19999980926513671875000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[162] = BOOST_MATH_TEST_VALUE(RealType, 20.96252285250892202655550269280724503656839238502424605070290458403293889858635506895301235328962628);
  zs<RealType>[163] = BOOST_MATH_TEST_VALUE(RealType, 26628797231.69999980926513671875000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[163] = BOOST_MATH_TEST_VALUE(RealType, 20.96252285252684375020669438704809175058008607738171622380516384350651954854873527952764745960194675);
  zs<RealType>[164] = BOOST_MATH_TEST_VALUE(RealType, 26628797232.19999980926513671875000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[164] = BOOST_MATH_TEST_VALUE(RealType, 20.96252285254476547385755026873492858475358446577888309438244123935827841754860782205245677270931493);
  zs<RealType>[165] = BOOST_MATH_TEST_VALUE(RealType, 26628797232.69999980926513671875000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[165] = BOOST_MATH_TEST_VALUE(RealType, 20.96252285256268719750807033786776813575757892712773332275219858225617530315647886304839621190128467);
  zs<RealType>[166] = BOOST_MATH_TEST_VALUE(RealType, 53257594465.89999961853027343750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[166] = BOOST_MATH_TEST_VALUE(RealType, 21.62457581338519341205251851678920458391270655214844894715960888032707282421057623183584027330480633);
  zs<RealType>[167] = BOOST_MATH_TEST_VALUE(RealType, 53257594466.39999961853027343750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[167] = BOOST_MATH_TEST_VALUE(RealType, 21.62457581339416678276021956345172476031736610096020431411495093672867810851785199503170153825356689);
  zs<RealType>[168] = BOOST_MATH_TEST_VALUE(RealType, 53257594466.89999961853027343750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[168] = BOOST_MATH_TEST_VALUE(RealType, 21.62457581340314015346783652970955706237153204994825959199820337645288096708677451415833137778699486);
  zs<RealType>[169] = BOOST_MATH_TEST_VALUE(RealType, 53257594467.39999961853027343750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[169] = BOOST_MATH_TEST_VALUE(RealType, 21.62457581341211352417536941556270306715209214940104899493615505349319261368609864010882236880736924);
  zs<RealType>[170] = BOOST_MATH_TEST_VALUE(RealType, 53257594467.89999961853027343750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[170] = BOOST_MATH_TEST_VALUE(RealType, 21.62457581342108689488281822101116435173593410522139124663366619300728455522058827136186640157150006);
  zs<RealType>[171] = BOOST_MATH_TEST_VALUE(RealType, 106515188936.2999992370605468750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[171] = BOOST_MATH_TEST_VALUE(RealType, 22.28752626920637793567947968920002916518604504482941952960611736969307899126632949710731838314939535);
  zs<RealType>[172] = BOOST_MATH_TEST_VALUE(RealType, 106515188936.7999992370605468750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[172] = BOOST_MATH_TEST_VALUE(RealType, 22.28752626921087052760828375602269251105477904207815836374209426134525952488796027799984818720270105);
  zs<RealType>[173] = BOOST_MATH_TEST_VALUE(RealType, 106515188937.2999992370605468750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[173] = BOOST_MATH_TEST_VALUE(RealType, 22.28752626921536311953706677275954356032288516798309826935596847224545105365234647336923162745148650);
  zs<RealType>[174] = BOOST_MATH_TEST_VALUE(RealType, 106515188937.7999992370605468750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[174] = BOOST_MATH_TEST_VALUE(RealType, 22.28752626921985571146582873941058251041835477955178166028729368533877454281235062918122417686844554);
  zs<RealType>[175] = BOOST_MATH_TEST_VALUE(RealType, 106515188938.2999992370605468750000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[175] = BOOST_MATH_TEST_VALUE(RealType, 22.28752626922434830339456965597580955876917923101340004236523230885349136854795158825395309771408668);
  zs<RealType>[176] = BOOST_MATH_TEST_VALUE(RealType, 213030377877.0999984741210937500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[176] = BOOST_MATH_TEST_VALUE(RealType, 22.95132494498044370189057193061229322652798069141485425830911821689634860387911575520267236257752886);
  zs<RealType>[177] = BOOST_MATH_TEST_VALUE(RealType, 213030377877.5999984741210937500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[177] = BOOST_MATH_TEST_VALUE(RealType, 22.95132494498269279111869452328160161214112251666554529993600600683165630516488115832469896181046045);
  zs<RealType>[178] = BOOST_MATH_TEST_VALUE(RealType, 213030377878.0999984741210937500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[178] = BOOST_MATH_TEST_VALUE(RealType, 22.95132494498494188034681184635345858036581911693953330207290531058251991996389235617064799550256701);
  zs<RealType>[179] = BOOST_MATH_TEST_VALUE(RealType, 213030377878.5999984741210937500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[179] = BOOST_MATH_TEST_VALUE(RealType, 22.95132494498719096957492389982786415591514868991446226878757600605585200116652511909077437303220322);
  zs<RealType>[180] = BOOST_MATH_TEST_VALUE(RealType, 213030377879.0999984741210937500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[180] = BOOST_MATH_TEST_VALUE(RealType, 22.95132494498944005880303068370481836350218943309407900398512057690019248825245765368449728229683978);
  zs<RealType>[181] = BOOST_MATH_TEST_VALUE(RealType, 426060755758.6999969482421875000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[181] = BOOST_MATH_TEST_VALUE(RealType, 23.61592645786617198666287725520753845230512592375445890316693635014712570198963226722376898746719788);
  zs<RealType>[182] = BOOST_MATH_TEST_VALUE(RealType, 426060755759.1999969482421875000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[182] = BOOST_MATH_TEST_VALUE(RealType, 23.61592645786729785413748555882136649105347335405111452573107829620408251494572350634646372518383704);
  zs<RealType>[183] = BOOST_MATH_TEST_VALUE(RealType, 426060755759.6999969482421875000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[183] = BOOST_MATH_TEST_VALUE(RealType, 23.61592645786842372161209254336341886225886040142987940947830085760720112665756558438930745003988259);
  zs<RealType>[184] = BOOST_MATH_TEST_VALUE(RealType, 426060755760.1999969482421875000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[184] = BOOST_MATH_TEST_VALUE(RealType, 23.61592645786954958908669820883369556901450401232685427846511608592597720877347644103130699500020980);
  zs<RealType>[185] = BOOST_MATH_TEST_VALUE(RealType, 426060755760.6999969482421875000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[185] = BOOST_MATH_TEST_VALUE(RealType, 23.61592645787067545656130255523219661441362113316725652002816962722163580709685521423287347532232663);
  zs<RealType>[186] = BOOST_MATH_TEST_VALUE(RealType, 852121511521.8999938964843750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[186] = BOOST_MATH_TEST_VALUE(RealType, 24.28128892222268328128985001144764937331876138884560895942339750264590092000743925470556345687342935);
  zs<RealType>[187] = BOOST_MATH_TEST_VALUE(RealType, 852121511522.3999938964843750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[187] = BOOST_MATH_TEST_VALUE(RealType, 24.28128892222324684237927811409049669410007439237129556446372910358198486719174460302964062617263997);
  zs<RealType>[188] = BOOST_MATH_TEST_VALUE(RealType, 852121511522.8999938964843750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[188] = BOOST_MATH_TEST_VALUE(RealType, 24.28128892222381040346870588656954830537234790758650044870333521914480895767426200215549894604462944);
  zs<RealType>[189] = BOOST_MATH_TEST_VALUE(RealType, 852121511523.3999938964843750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[189] = BOOST_MATH_TEST_VALUE(RealType, 24.28128892222437396455813332888480420752271669125958665876145325130479060260809395708493929658437074);
  zs<RealType>[190] = BOOST_MATH_TEST_VALUE(RealType, 852121511523.8999938964843750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[190] = BOOST_MATH_TEST_VALUE(RealType, 24.28128892222493752564756044103626440093831550015823616066865071071416085939189362440560522437633662);
  zs<RealType>[191] = BOOST_MATH_TEST_VALUE(RealType, 1704243023048.299987792968750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[191] = BOOST_MATH_TEST_VALUE(RealType, 24.94737360307934242599198887438952878908310927656184464570761925753018240286868931047379101991192915);
  zs<RealType>[192] = BOOST_MATH_TEST_VALUE(RealType, 1704243023048.799987792968750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[192] = BOOST_MATH_TEST_VALUE(RealType, 24.94737360307962450443999814141844483539218897580562586948087460916859526796854661158129821949554554);
  zs<RealType>[193] = BOOST_MATH_TEST_VALUE(RealType, 1704243023049.299987792968750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[193] = BOOST_MATH_TEST_VALUE(RealType, 24.94737360307990658288800732581258578292473970863909517564631259029126397702972526621236324413898654);
  zs<RealType>[194] = BOOST_MATH_TEST_VALUE(RealType, 1704243023049.799987792968750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[194] = BOOST_MATH_TEST_VALUE(RealType, 24.94737360308018866133601642757195163172921046426642186059476077116782502974396487792107071563852968);
  zs<RealType>[195] = BOOST_MATH_TEST_VALUE(RealType, 1704243023050.299987792968750000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[195] = BOOST_MATH_TEST_VALUE(RealType, 24.94737360308047073978402544669654238185405023189173260158888945690205253434326923773189194023738864);
  zs<RealType>[196] = BOOST_MATH_TEST_VALUE(RealType, 3408486046101.099975585937500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[196] = BOOST_MATH_TEST_VALUE(RealType, 25.61414461111548947260493867878972043152010342814420318471604738869697286844100478961752318507519036);
  zs<RealType>[197] = BOOST_MATH_TEST_VALUE(RealType, 3408486046101.599975585937500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[197] = BOOST_MATH_TEST_VALUE(RealType, 25.61414461111563065346677642310736687968026218525976239525273457263820521539063299775063487387395491);
  zs<RealType>[198] = BOOST_MATH_TEST_VALUE(RealType, 3408486046102.099975585937500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[198] = BOOST_MATH_TEST_VALUE(RealType, 25.61414461111577183432861414674405116970646776101208949311878694889082271860331380169436692589290008);
  zs<RealType>[199] = BOOST_MATH_TEST_VALUE(RealType, 3408486046102.599975585937500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[199] = BOOST_MATH_TEST_VALUE(RealType, 25.61414461111591301519045184969977330160478305423319389187668123911964696321646253276250725578473047);
  zs<RealType>[200] = BOOST_MATH_TEST_VALUE(RealType, 3408486046103.099975585937500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[200] = BOOST_MATH_TEST_VALUE(RealType, 25.61414461111605419605228953197453327538127096375508233833690347792194785120108760652931053928666785);
  zs<RealType>[201] = BOOST_MATH_TEST_VALUE(RealType, 6816972092206.699951171875000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[201] = BOOST_MATH_TEST_VALUE(RealType, 26.28156863336777149103602875189240858590017172273907420428241974657782598001971183829692697893931030);
  zs<RealType>[202] = BOOST_MATH_TEST_VALUE(RealType, 6816972092207.199951171875000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[202] = BOOST_MATH_TEST_VALUE(RealType, 26.28156863336784214888845714766036358708919247411311945160997461946071687776487761293647943181961882);
  zs<RealType>[203] = BOOST_MATH_TEST_VALUE(RealType, 6816972092207.699951171875000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[203] = BOOST_MATH_TEST_VALUE(RealType, 26.28156863336791280674088553825278629338313564287800253072882363885000489322789589683564459399860775);
  zs<RealType>[204] = BOOST_MATH_TEST_VALUE(RealType, 6816972092208.199951171875000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[204] = BOOST_MATH_TEST_VALUE(RealType, 26.28156863336798346459331392366967670478275989571118963232699709422043712865525671390927105068828991);
  zs<RealType>[205] = BOOST_MATH_TEST_VALUE(RealType, 6816972092208.699951171875000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[205] = BOOST_MATH_TEST_VALUE(RealType, 26.28156863336805412244574230391103482128882389929014678023920695547828528484685657699485259769208741);
  zs<RealType>[206] = BOOST_MATH_TEST_VALUE(RealType, 13633944184417.89990234375000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[206] = BOOST_MATH_TEST_VALUE(RealType, 26.94961469479482406519801256975073261795345435020419867463744516732345674294492529228061805959732327);
  zs<RealType>[207] = BOOST_MATH_TEST_VALUE(RealType, 13633944184418.39990234375000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[207] = BOOST_MATH_TEST_VALUE(RealType, 26.94961469479485942625415366326146401152634296784764225669862897123975110273521017330868675363150448);
  zs<RealType>[208] = BOOST_MATH_TEST_VALUE(RealType, 13633944184418.89990234375000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[208] = BOOST_MATH_TEST_VALUE(RealType, 26.94961469479489478731029475547705331300159611152098923590641458690234001429707091782045185453438171);
  zs<RealType>[209] = BOOST_MATH_TEST_VALUE(RealType, 13633944184419.39990234375000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[209] = BOOST_MATH_TEST_VALUE(RealType, 26.94961469479493014836643584639750052237930871016343368359473040533580595453533790563068986805724019);
  zs<RealType>[210] = BOOST_MATH_TEST_VALUE(RealType, 13633944184419.89990234375000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[210] = BOOST_MATH_TEST_VALUE(RealType, 26.94961469479496550942257693602280563965957569271416966065840246264353271487273203593602827136860754);
  zs<RealType>[211] = BOOST_MATH_TEST_VALUE(RealType, 27267888368840.29980468750000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[211] = BOOST_MATH_TEST_VALUE(RealType, 27.61825394658132365321120256576467284757880630504909781214698429280000692432972722344046738139460516);
  zs<RealType>[212] = BOOST_MATH_TEST_VALUE(RealType, 27267888368840.79980468750000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[212] = BOOST_MATH_TEST_VALUE(RealType, 27.61825394658134134906748186542924183870772699960645627424489223278978300139646551878816289033156892);
  zs<RealType>[213] = BOOST_MATH_TEST_VALUE(RealType, 27267888368841.29980468750000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[213] = BOOST_MATH_TEST_VALUE(RealType, 27.61825394658135904492376116476972541669907157102168356799570843777496579434166012376207829201011287);
  zs<RealType>[214] = BOOST_MATH_TEST_VALUE(RealType, 27267888368841.79980468750000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[214] = BOOST_MATH_TEST_VALUE(RealType, 27.61825394658137674078004046378612358155285189678948212439453829367032310042341489097274555293104964);
  zs<RealType>[215] = BOOST_MATH_TEST_VALUE(RealType, 27267888368842.29980468750000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[215] = BOOST_MATH_TEST_VALUE(RealType, 27.61825394658139443663631976247843633326907985440455437378340280356184152787821056110486626668344188);
  zs<RealType>[216] = BOOST_MATH_TEST_VALUE(RealType, 54535776737685.09960937500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[216] = BOOST_MATH_TEST_VALUE(RealType, 28.28745947768658520727223095526336163825405277084208702812195528505679434221769385747727596125338626);
  zs<RealType>[217] = BOOST_MATH_TEST_VALUE(RealType, 54535776737685.59960937500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[217] = BOOST_MATH_TEST_VALUE(RealType, 28.28745947768659406252057469058839388454672293394318546730557386127002114050381424035876354103192624);
  zs<RealType>[218] = BOOST_MATH_TEST_VALUE(RealType, 54535776737686.09960937500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[218] = BOOST_MATH_TEST_VALUE(RealType, 28.28745947768660291776891842583233326734639195028174391562886488418853280961627679416432912568037643);
  zs<RealType>[219] = BOOST_MATH_TEST_VALUE(RealType, 54535776737686.59960937500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[219] = BOOST_MATH_TEST_VALUE(RealType, 28.28745947768661177301726216099517978665306130590007248679624798101760603565428399982488746458503134);
  zs<RealType>[220] = BOOST_MATH_TEST_VALUE(RealType, 54535776737687.09960937500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[220] = BOOST_MATH_TEST_VALUE(RealType, 28.28745947768662062826560589607693344246673248684048129447128687164847423418296378412381527867603611);
  zs<RealType>[221] = BOOST_MATH_TEST_VALUE(RealType, 109071553475374.6992187500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[221] = BOOST_MATH_TEST_VALUE(RealType, 28.95720614666117150245004622760057983418314410522200232704276775862146736266761022246497551058785007);
  zs<RealType>[222] = BOOST_MATH_TEST_VALUE(RealType, 109071553475375.1992187500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[222] = BOOST_MATH_TEST_VALUE(RealType, 28.95720614666117593357355743548739761689791124679984859813361369105181180764744313352061153147051630);
  zs<RealType>[223] = BOOST_MATH_TEST_VALUE(RealType, 109071553475375.6992187500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[223] = BOOST_MATH_TEST_VALUE(RealType, 28.95720614666118036469706864335392511390934879372874136387705864492100434790697368864211343956938842);
  zs<RealType>[224] = BOOST_MATH_TEST_VALUE(RealType, 109071553475376.1992187500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[224] = BOOST_MATH_TEST_VALUE(RealType, 28.95720614666118479582057985120016232521745693192563248069260165217440327859683071396461341530343065);
  zs<RealType>[225] = BOOST_MATH_TEST_VALUE(RealType, 109071553475376.6992187500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[225] = BOOST_MATH_TEST_VALUE(RealType, 28.95720614666118922694409105902610925082223584730747380499718597765232850574002390696473010380772407);
  zs<RealType>[226] = BOOST_MATH_TEST_VALUE(RealType, 218143106950753.8984375000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[226] = BOOST_MATH_TEST_VALUE(RealType, 29.62747043118774188445791042660254784305836995418567274505820949514291909017768623167600827442630858);
  zs<RealType>[227] = BOOST_MATH_TEST_VALUE(RealType, 218143106950754.3984375000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[227] = BOOST_MATH_TEST_VALUE(RealType, 29.62747043118774410169407734916144601996690190995667210727468326746331358762853812415838063247614151);
  zs<RealType>[228] = BOOST_MATH_TEST_VALUE(RealType, 218143106950754.8984375000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[228] = BOOST_MATH_TEST_VALUE(RealType, 29.62747043118774631893024427171526754670674911118564686113352807043233143508066106374847716587931185);
  zs<RealType>[229] = BOOST_MATH_TEST_VALUE(RealType, 218143106950755.3984375000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[229] = BOOST_MATH_TEST_VALUE(RealType, 29.62747043118774853616641119426401242327791158113151699337805013921071479703774514043229472570138003);
  zs<RealType>[230] = BOOST_MATH_TEST_VALUE(RealType, 218143106950755.8984375000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[230] = BOOST_MATH_TEST_VALUE(RealType, 29.62747043118775075340257811680768064968038934305320249075139583789870247295334960013555124352240919);
  zs<RealType>[231] = BOOST_MATH_TEST_VALUE(RealType, 436286213901512.2968750000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[231] = BOOST_MATH_TEST_VALUE(RealType, 30.29823029316507210180178060183512748657385641403633979025174401415202721913219462712200443246592017);
  zs<RealType>[232] = BOOST_MATH_TEST_VALUE(RealType, 436286213901512.7968750000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[232] = BOOST_MATH_TEST_VALUE(RealType, 30.29823029316507321122179075661323950744341840810124864190846957886463039301370908060223378776053761);
  zs<RealType>[233] = BOOST_MATH_TEST_VALUE(RealType, 436286213901513.2968750000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[233] = BOOST_MATH_TEST_VALUE(RealType, 30.29823029316507432064180091139008139023924952729670215908087118481612362461688278748768609693129214);
  zs<RealType>[234] = BOOST_MATH_TEST_VALUE(RealType, 436286213901513.7968750000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[234] = BOOST_MATH_TEST_VALUE(RealType, 30.29823029316507543006181106616565313496134977453237197516164536262012622641950793744512740976903471);
  zs<RealType>[235] = BOOST_MATH_TEST_VALUE(RealType, 436286213901514.2968750000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[235] = BOOST_MATH_TEST_VALUE(RealType, 30.29823029316507653948182122093995474160971915271792972354347864284901025700020408100899889264653839);
  zs<RealType>[236] = BOOST_MATH_TEST_VALUE(RealType, 872572427803029.0937500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[236] = BOOST_MATH_TEST_VALUE(RealType, 30.96946505745926454870573870105381204121080031682700579571979417608023815834802026646642551756529303);
  zs<RealType>[237] = BOOST_MATH_TEST_VALUE(RealType, 872572427803029.5937500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[237] = BOOST_MATH_TEST_VALUE(RealType, 30.96946505745926510380014769927740975753101903356685400661074674057071774744000291503524870439239858);
  zs<RealType>[238] = BOOST_MATH_TEST_VALUE(RealType, 872572427803030.0937500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[238] = BOOST_MATH_TEST_VALUE(RealType, 30.96946505745926565889455669750068970579501127050404305753044873046035266500692837850798788777465676);
  zs<RealType>[239] = BOOST_MATH_TEST_VALUE(RealType, 872572427803030.5937500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[239] = BOOST_MATH_TEST_VALUE(RealType, 30.96946505745926621398896569572365188600277702800255782702903047591596266870263378125202082655252570);
  zs<RealType>[240] = BOOST_MATH_TEST_VALUE(RealType, 872572427803031.0937500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[240] = BOOST_MATH_TEST_VALUE(RealType, 30.96946505745926676908337469394629629815431630642638319365662168161753441065238424482444892944732090);
  zs<RealType>[241] = BOOST_MATH_TEST_VALUE(RealType, 1745144855606062.687500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[241] = BOOST_MATH_TEST_VALUE(RealType, 31.64115530270368555045568380920065773046212688909377117164466588005747217968807471779219205518025563);
  zs<RealType>[242] = BOOST_MATH_TEST_VALUE(RealType, 1745144855606063.187500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[242] = BOOST_MATH_TEST_VALUE(RealType, 31.64115530270368582818730773733152774326033355552322481669720170248660524963440150598888428761058290);
  zs<RealType>[243] = BOOST_MATH_TEST_VALUE(RealType, 1745144855606063.687500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[243] = BOOST_MATH_TEST_VALUE(RealType, 31.64115530270368610591893166546231825808704138452046802967409474774871621840817874673003149159950917);
  zs<RealType>[244] = BOOST_MATH_TEST_VALUE(RealType, 1745144855606064.187500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[244] = BOOST_MATH_TEST_VALUE(RealType, 31.64115530270368638365055559359302927494225037613103195710423753246607554009944184530654853462065205);
  zs<RealType>[245] = BOOST_MATH_TEST_VALUE(RealType, 1745144855606064.687500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[245] = BOOST_MATH_TEST_VALUE(RealType, 31.64115530270368666138217952172366079382596053040044774551652253413904860066772832705321064573507936);
  zs<RealType>[246] = BOOST_MATH_TEST_VALUE(RealType, 3490289711212129.875000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[246] = BOOST_MATH_TEST_VALUE(RealType, 32.31328276274725912255296233554695621993095527834620867411002883617418802445848848920754168937963900);
  zs<RealType>[247] = BOOST_MATH_TEST_VALUE(RealType, 3490289711212130.375000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[247] = BOOST_MATH_TEST_VALUE(RealType, 32.31328276274725926150732198502036790376252498771483422694718006966861906633408209207371317438042436);
  zs<RealType>[248] = BOOST_MATH_TEST_VALUE(RealType, 3490289711212130.875000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[248] = BOOST_MATH_TEST_VALUE(RealType, 32.31328276274725940046168163449375969968196897954449339669242357827673003495386979661882340839494652);
  zs<RealType>[249] = BOOST_MATH_TEST_VALUE(RealType, 3490289711212131.375000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[249] = BOOST_MATH_TEST_VALUE(RealType, 32.31328276274725953941604128396713160768928725384088153564235657069057463593847925585888659769685682);
  zs<RealType>[250] = BOOST_MATH_TEST_VALUE(RealType, 3490289711212131.875000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[250] = BOOST_MATH_TEST_VALUE(RealType, 32.31328276274725967837040093344048362778447981060969399609357625315535190876608557675610677996463049);
  zs<RealType>[251] = BOOST_MATH_TEST_VALUE(RealType, 6980579422424264.250000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[251] = BOOST_MATH_TEST_VALUE(RealType, 32.98583023753604679088719826542088155377570542413337629407446260913838808164256711555296996403852860);
  zs<RealType>[252] = BOOST_MATH_TEST_VALUE(RealType, 6980579422424264.750000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[252] = BOOST_MATH_TEST_VALUE(RealType, 32.98583023753604686040692677719047881106035406644182281598485405113601440205771668223737335876041818);
  zs<RealType>[253] = BOOST_MATH_TEST_VALUE(RealType, 6980579422424265.250000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[253] = BOOST_MATH_TEST_VALUE(RealType, 32.98583023753604692992665528896007109314624214905869648472859408505262279189002691227175712262681487);
  zs<RealType>[254] = BOOST_MATH_TEST_VALUE(RealType, 6980579422424265.750000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[254] = BOOST_MATH_TEST_VALUE(RealType, 32.98583023753604699944638380072965840003336967198470969416928902364624408970718516308377862696183264);
  zs<RealType>[255] = BOOST_MATH_TEST_VALUE(RealType, 6980579422424266.250000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[255] = BOOST_MATH_TEST_VALUE(RealType, 32.98583023753604706896611231249924073172173663522057483817054517952187667696583584412183600018203736);
  zs<RealType>[256] = BOOST_MATH_TEST_VALUE(RealType, 13961158844848533.00000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[256] = BOOST_MATH_TEST_VALUE(RealType, 33.65878151237097095124298518143702077667858405885409410230992311053881919684167704487215990113228857);
  zs<RealType>[257] = BOOST_MATH_TEST_VALUE(RealType, 13961158844848533.50000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[257] = BOOST_MATH_TEST_VALUE(RealType, 33.65878151237097098602331016061647350048233624732159922625861798362865689925616857240391773797386011);
  zs<RealType>[258] = BOOST_MATH_TEST_VALUE(RealType, 13961158844848534.00000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[258] = BOOST_MATH_TEST_VALUE(RealType, 33.65878151237097102080363513979592497971278809664223503486445169932919366667254889157695575939580298);
  zs<RealType>[259] = BOOST_MATH_TEST_VALUE(RealType, 13961158844848534.50000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[259] = BOOST_MATH_TEST_VALUE(RealType, 33.65878151237097105558396011897537521436993960681609063435549611864140613496327710388308960838614952);
  zs<RealType>[260] = BOOST_MATH_TEST_VALUE(RealType, 13961158844848535.00000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[260] = BOOST_MATH_TEST_VALUE(RealType, 33.65878151237097109036428509815482420445379077784325513095982310255670016959177612483326392564338403);
  zs<RealType>[261] = BOOST_MATH_TEST_VALUE(RealType, 27922317689697070.50000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[261] = BOOST_MATH_TEST_VALUE(RealType, 34.33212128461954182192055400546316699688336088176596379963232567718408755304304153654131361648983188);
  zs<RealType>[262] = BOOST_MATH_TEST_VALUE(RealType, 27922317689697071.00000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[262] = BOOST_MATH_TEST_VALUE(RealType, 34.33212128461954183932056272117319130023138040040443817185436738411951668715588692235416442685371045);
  zs<RealType>[263] = BOOST_MATH_TEST_VALUE(RealType, 27922317689697071.50000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[263] = BOOST_MATH_TEST_VALUE(RealType, 34.33212128461954185672057143688321529225011483876409341147922608312437009263576524224673465664622416);
  zs<RealType>[264] = BOOST_MATH_TEST_VALUE(RealType, 27922317689697072.00000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[264] = BOOST_MATH_TEST_VALUE(RealType, 34.33212128461954187412058015259323897293956419684494066363206316215367067849034858025290827895879418);
  zs<RealType>[265] = BOOST_MATH_TEST_VALUE(RealType, 27922317689697072.50000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[265] = BOOST_MATH_TEST_VALUE(RealType, 34.33212128461954189152058886830326234229972847464699107343804000916184280580506932042040723667339789);
  zs<RealType>[266] = BOOST_MATH_TEST_VALUE(RealType, 55844635379394145.50000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[266] = BOOST_MATH_TEST_VALUE(RealType, 35.00583509707518695935331739482145723160151536139963974678105921205914075721658873691195091665811270);
  zs<RealType>[267] = BOOST_MATH_TEST_VALUE(RealType, 55844635379394146.00000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[267] = BOOST_MATH_TEST_VALUE(RealType, 35.00583509707518696805806331425691633367772960837479285214203859766210400707178436995199558318407295);
  zs<RealType>[268] = BOOST_MATH_TEST_VALUE(RealType, 55844635379394146.50000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[268] = BOOST_MATH_TEST_VALUE(RealType, 35.00583509707518697676280923369237535787688894919552843836080956969078417834382970920191457114876432);
  zs<RealType>[269] = BOOST_MATH_TEST_VALUE(RealType, 55844635379394147.00000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[269] = BOOST_MATH_TEST_VALUE(RealType, 35.00583509707518698546755515312783430419899338386184789940110157674808234768343577335618132616959765);
  zs<RealType>[270] = BOOST_MATH_TEST_VALUE(RealType, 55844635379394147.50000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[270] = BOOST_MATH_TEST_VALUE(RealType, 35.00583509707518699417230107256329317264404291237375262922664406743686215997702269925047177260404215);
  zs<RealType>[271] = BOOST_MATH_TEST_VALUE(RealType, 111689270758788295.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[271] = BOOST_MATH_TEST_VALUE(RealType, 35.67990927725728324006370419405415632798743326857723995400791753575138185111578870305633556551072352);
  zs<RealType>[272] = BOOST_MATH_TEST_VALUE(RealType, 111689270758788296.0000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[272] = BOOST_MATH_TEST_VALUE(RealType, 35.67990927725728324441836204273681992689740132873188907673425746295594512007334247458414132014763631);
  zs<RealType>[273] = BOOST_MATH_TEST_VALUE(RealType, 111689270758788296.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[273] = BOOST_MATH_TEST_VALUE(RealType, 35.67990927725728324877301989141948350632733717201783214881466640664746188099504650620747906266396850);
  zs<RealType>[274] = BOOST_MATH_TEST_VALUE(RealType, 111689270758788297.0000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[274] = BOOST_MATH_TEST_VALUE(RealType, 35.67990927725728325312767774010214706627724079843506934459363078248730870402916016710439865808348504);
  zs<RealType>[275] = BOOST_MATH_TEST_VALUE(RealType, 111689270758788297.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[275] = BOOST_MATH_TEST_VALUE(RealType, 35.67990927725728325748233558878481060674711220798360083841563700613685981848455744088126373390197417);
  zs<RealType>[276] = BOOST_MATH_TEST_VALUE(RealType, 223378541517576595.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[276] = BOOST_MATH_TEST_VALUE(RealType, 36.35433088203076284552001564306302504927318242547212268948185919007001226970738285875013148048139909);
  zs<RealType>[277] = BOOST_MATH_TEST_VALUE(RealType, 223378541517576596.0000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[277] = BOOST_MATH_TEST_VALUE(RealType, 36.35433088203076284769844633707581493482388002617376992599930776877134475450868721982023076025880187);
  zs<RealType>[278] = BOOST_MATH_TEST_VALUE(RealType, 223378541517576596.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[278] = BOOST_MATH_TEST_VALUE(RealType, 36.35433088203076284987687703108860481550197556889556172047264008790594215563414633410406449425201228);
  zs<RealType>[279] = BOOST_MATH_TEST_VALUE(RealType, 223378541517576597.0000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[279] = BOOST_MATH_TEST_VALUE(RealType, 36.35433088203076285205530772510139469130746905363749809470683776684452160660198665349883658913823376);
  zs<RealType>[280] = BOOST_MATH_TEST_VALUE(RealType, 223378541517576597.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[280] = BOOST_MATH_TEST_VALUE(RealType, 36.35433088203076285423373841911418456224036048039957907050688242495780009454648985107688288289511577);
  zs<RealType>[281] = BOOST_MATH_TEST_VALUE(RealType, 446757083035153195.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[281] = BOOST_MATH_TEST_VALUE(RealType, 37.02908764699829400134712267319905290193660562118300513507830516933879565438304668183143470276536696);
  zs<RealType>[282] = BOOST_MATH_TEST_VALUE(RealType, 446757083035153196.0000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[282] = BOOST_MATH_TEST_VALUE(RealType, 37.02908764699829400243686962505878682539242575730874103256160022984465712010716817361916571773853546);
  zs<RealType>[283] = BOOST_MATH_TEST_VALUE(RealType, 446757083035153196.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[283] = BOOST_MATH_TEST_VALUE(RealType, 37.02908764699829400352661657691852074762947010360082635121697561514628341391528501142658520743630365);
  zs<RealType>[284] = BOOST_MATH_TEST_VALUE(RealType, 446757083035153197.0000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[284] = BOOST_MATH_TEST_VALUE(RealType, 37.02908764699829400461636352877825466864773866005926109377149011203944059666845076630112677861392210);
  zs<RealType>[285] = BOOST_MATH_TEST_VALUE(RealType, 446757083035153197.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[285] = BOOST_MATH_TEST_VALUE(RealType, 37.02908764699829400570611048063798858844723142668404526295220250731989472007381590773195282652560254);
  zs<RealType>[286] = BOOST_MATH_TEST_VALUE(RealType, 893514166070306395.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[286] = BOOST_MATH_TEST_VALUE(RealType, 37.70416794018226555320136342385675657230684506000440195815556999407621228993843426869578896616407720);
  zs<RealType>[287] = BOOST_MATH_TEST_VALUE(RealType, 893514166070306396.0000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[287] = BOOST_MATH_TEST_VALUE(RealType, 37.70416794018226555374649355515368083078169249852645520316613216452931331516160499635557395351284563);
  zs<RealType>[288] = BOOST_MATH_TEST_VALUE(RealType, 893514166070306396.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[288] = BOOST_MATH_TEST_VALUE(RealType, 37.70416794018226555429162368645060508895169517366721864271816097307727758099155612982661657855356471);
  zs<RealType>[289] = BOOST_MATH_TEST_VALUE(RealType, 893514166070306397.0000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[289] = BOOST_MATH_TEST_VALUE(RealType, 37.70416794018226555483675381774752934681685308542669227715271188344812250649110477219885655515210238);
  zs<RealType>[290] = BOOST_MATH_TEST_VALUE(RealType, 893514166070306397.5000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[290] = BOOST_MATH_TEST_VALUE(RealType, 37.70416794018226555538188394904445360437716623380487610681084035936986551015065343719767859121005169);
  zs<RealType>[291] = BOOST_MATH_TEST_VALUE(RealType, 1787028332140612795.500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[291] = BOOST_MATH_TEST_VALUE(RealType, 38.37956071956958972598482293827813436342029687218073991385854219392107138609564999755336816382782882);
  zs<RealType>[292] = BOOST_MATH_TEST_VALUE(RealType, 1787028332140612796.000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[292] = BOOST_MATH_TEST_VALUE(RealType, 38.37956071956958972625751198813595667063367232609526061670475810039080545891532051379509177129548620);
  zs<RealType>[293] = BOOST_MATH_TEST_VALUE(RealType, 1787028332140612796.500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[293] = BOOST_MATH_TEST_VALUE(RealType, 38.37956071956958972653020103799377897777080019019814937016569594146159440469991463659659940941732350);
  zs<RealType>[294] = BOOST_MATH_TEST_VALUE(RealType, 1787028332140612797.000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[294] = BOOST_MATH_TEST_VALUE(RealType, 38.37956071956958972680289008785160128483168046448940617428400853168904877026810016036680752500851451);
  zs<RealType>[295] = BOOST_MATH_TEST_VALUE(RealType, 1787028332140612797.500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[295] = BOOST_MATH_TEST_VALUE(RealType, 38.37956071956958972707557913770942359181631314896903102910234868562877910240275114213945213826099662);
  zs<RealType>[296] = BOOST_MATH_TEST_VALUE(RealType, 3574056664281225595.500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[296] = BOOST_MATH_TEST_VALUE(RealType, 39.05525549414090357397288405889113661262090376242531189140228166174913957175516846453031303867939318);
  zs<RealType>[297] = BOOST_MATH_TEST_VALUE(RealType, 3574056664281225596.000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[297] = BOOST_MATH_TEST_VALUE(RealType, 39.05525549414090357410928851167839535330306428730324674240848562357412341096307490721136888569495897);
  zs<RealType>[298] = BOOST_MATH_TEST_VALUE(RealType, 3574056664281225596.500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[298] = BOOST_MATH_TEST_VALUE(RealType, 39.05525549414090357424569296446565409396615412474444985847532731674289519382422208292858248536499652);
  zs<RealType>[299] = BOOST_MATH_TEST_VALUE(RealType, 3574056664281225597.000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[299] = BOOST_MATH_TEST_VALUE(RealType, 39.05525549414090357438209741725291283461017327474892123960814086338854298164097945903007554014878729);
  zs<RealType>[300] = BOOST_MATH_TEST_VALUE(RealType, 3574056664281225597.500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[300] = BOOST_MATH_TEST_VALUE(RealType, 39.05525549414090357451850187004017157523512173731666088581226038564415483571347831884995089329885967);
  zs<RealType>[301] = BOOST_MATH_TEST_VALUE(RealType, 7148113328562451195.500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[301] = BOOST_MATH_TEST_VALUE(RealType, 39.73124228804812727783498401939051909927376255844770721021129458386841998457593172268879366938255929);
  zs<RealType>[302] = BOOST_MATH_TEST_VALUE(RealType, 7148113328562451196.000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[302] = BOOST_MATH_TEST_VALUE(RealType, 39.73124228804812727790321522786904454299244826066833347409818515980037701571602976213801388272915974);
  zs<RealType>[303] = BOOST_MATH_TEST_VALUE(RealType, 7148113328562451196.500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[303] = BOOST_MATH_TEST_VALUE(RealType, 39.73124228804812727797144643634756998670636416712076247668418626969651461616512047870718042117101998);
  zs<RealType>[304] = BOOST_MATH_TEST_VALUE(RealType, 7148113328562451197.000000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[304] = BOOST_MATH_TEST_VALUE(RealType, 39.73124228804812727803967764482609543041551027780499421796996498319224892410706675338130329932555167);
  zs<RealType>[305] = BOOST_MATH_TEST_VALUE(RealType, 7148113328562451197.500000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[305] = BOOST_MATH_TEST_VALUE(RealType, 39.73124228804812727810790885330462087411988659272102869795618836992299607772559151572452093096440915);
  zs<RealType>[306] = BOOST_MATH_TEST_VALUE(RealType, 14296226657124902395.50000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[306] = BOOST_MATH_TEST_VALUE(RealType, 40.40751160764139677831479038829047423830224736434558035742829431520714244678484344822558100019565300);
  zs<RealType>[307] = BOOST_MATH_TEST_VALUE(RealType, 14296226657124902396.00000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[307] = BOOST_MATH_TEST_VALUE(RealType, 40.40751160764139677834892001619303911408381392078589832744493031890002310138573922908458466967872121);
  zs<RealType>[308] = BOOST_MATH_TEST_VALUE(RealType, 14296226657124902396.50000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[308] = BOOST_MATH_TEST_VALUE(RealType, 40.40751160764139677838304964409560398986418751480292907711652892277321787302547236270065870054677767);
  zs<RealType>[309] = BOOST_MATH_TEST_VALUE(RealType, 14296226657124902397.00000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[309] = BOOST_MATH_TEST_VALUE(RealType, 40.40751160764139677841717927199816886564336814639667260644317354730947215019219733421649211897790576);
  zs<RealType>[310] = BOOST_MATH_TEST_VALUE(RealType, 14296226657124902397.50000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[310] = BOOST_MATH_TEST_VALUE(RealType, 40.40751160764139677845130889990073374142135581556712891542494761299153132137405987788970971812438268);
  zs<RealType>[311] = BOOST_MATH_TEST_VALUE(RealType, 28592453314249804795.50000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[311] = BOOST_MATH_TEST_VALUE(RealType, 41.08405441107886601512650396438190311342038447614710861375772179966198427428166270594434592188846742);
  zs<RealType>[312] = BOOST_MATH_TEST_VALUE(RealType, 28592453314249804796.00000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[312] = BOOST_MATH_TEST_VALUE(RealType, 41.08405441107886601514357556751058240524595848620754197445685531919168072376579651061145028382528801);
  zs<RealType>[313] = BOOST_MATH_TEST_VALUE(RealType, 28592453314249804796.50000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[313] = BOOST_MATH_TEST_VALUE(RealType, 41.08405441107886601516064717063926169707123413145503282985377893953090450589428836799851249587190162);
  zs<RealType>[314] = BOOST_MATH_TEST_VALUE(RealType, 28592453314249804797.00000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[314] = BOOST_MATH_TEST_VALUE(RealType, 41.08405441107886601517771877376794098889621141188958117994850309268653710559007455198782311971328872);
  zs<RealType>[315] = BOOST_MATH_TEST_VALUE(RealType, 28592453314249804797.50000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[315] = BOOST_MATH_TEST_VALUE(RealType, 41.08405441107886601519479037689662028072089032751118702474103821066546000777609078929437453810254475);
  zs<RealType>[316] = BOOST_MATH_TEST_VALUE(RealType, 57184906628499609595.50000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[316] = BOOST_MATH_TEST_VALUE(RealType, 41.76086208028139568641375217383589970289192285524027455320442791145685452599382947676049441268469929);
  zs<RealType>[317] = BOOST_MATH_TEST_VALUE(RealType, 57184906628499609596.00000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[317] = BOOST_MATH_TEST_VALUE(RealType, 41.76086208028139568642229126383956807166540537938630285140147251930665093550545985675206920712567685);
  zs<RealType>[318] = BOOST_MATH_TEST_VALUE(RealType, 57184906628499609596.50000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[318] = BOOST_MATH_TEST_VALUE(RealType, 41.76086208028139568643083035384323644043881328226871966400896425186789096638830671067070738275427294);
  zs<RealType>[319] = BOOST_MATH_TEST_VALUE(RealType, 57184906628499609597.00000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[319] = BOOST_MATH_TEST_VALUE(RealType, 41.76086208028139568643936944384690480921214656388752499102690441367931200542104387248813871592477758);
  zs<RealType>[320] = BOOST_MATH_TEST_VALUE(RealType, 57184906628499609597.50000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[320] = BOOST_MATH_TEST_VALUE(RealType, 41.76086208028139568644790853385057317798540522424271883245529430927965143938234514196380710928511911);
  zs<RealType>[321] = BOOST_MATH_TEST_VALUE(RealType, 114369813256999219195.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[321] = BOOST_MATH_TEST_VALUE(RealType, 42.43792639501924322372868984974754605356116106151045232696170539516224623515519078402644518190519926);
  zs<RealType>[322] = BOOST_MATH_TEST_VALUE(RealType, 114369813256999219196.0000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[322] = BOOST_MATH_TEST_VALUE(RealType, 42.43792639501924322373296098832611092973985577032241411056103015380922288812869321261489035558578068);
  zs<RealType>[323] = BOOST_MATH_TEST_VALUE(RealType, 114369813256999219196.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[323] = BOOST_MATH_TEST_VALUE(RealType, 42.43792639501924322373723212690467580591853181653964600305518073422329365062099792146842039711851587);
  zs<RealType>[324] = BOOST_MATH_TEST_VALUE(RealType, 114369813256999219197.0000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[324] = BOOST_MATH_TEST_VALUE(RealType, 42.43792639501924322374150326548324068209718920016214800444415729953689875272821439439139550720636184);
  zs<RealType>[325] = BOOST_MATH_TEST_VALUE(RealType, 114369813256999219197.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[325] = BOOST_MATH_TEST_VALUE(RealType, 42.43792639501924322374577440406180555827582792118992011472796001288247842454645211304904107994414835);
  zs<RealType>[326] = BOOST_MATH_TEST_VALUE(RealType, 228739626513998438395.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[326] = BOOST_MATH_TEST_VALUE(RealType, 43.11523950893999669327806140448370484701263856771509543516859403088443747042918192039960542776140113);
  zs<RealType>[327] = BOOST_MATH_TEST_VALUE(RealType, 228739626513998438396.0000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[327] = BOOST_MATH_TEST_VALUE(RealType, 43.11523950893999669328019774638303987145009950720434908964235203327796824314750703851313533491796197);
  zs<RealType>[328] = BOOST_MATH_TEST_VALUE(RealType, 228739626513998438396.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[328] = BOOST_MATH_TEST_VALUE(RealType, 43.11523950893999669328233408828237489588755577928156076930831411008263669948642260552727690669574065);
  zs<RealType>[329] = BOOST_MATH_TEST_VALUE(RealType, 228739626513998438397.0000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[329] = BOOST_MATH_TEST_VALUE(RealType, 43.11523950893999669328447043018170992032500738394673047416648028169788047545599558661614805384499252);
  zs<RealType>[330] = BOOST_MATH_TEST_VALUE(RealType, 228739626513998438397.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[330] = BOOST_MATH_TEST_VALUE(RealType, 43.11523950893999669328660677208104494476245432119985820421685056852313720706629294682011828585641323);
  zs<RealType>[331] = BOOST_MATH_TEST_VALUE(RealType, 457479253027996876795.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[331] = BOOST_MATH_TEST_VALUE(RealType, 43.79279392736654193606989766502723445979048572229231465829706451573324407857444527395822448178986007);
  zs<RealType>[332] = BOOST_MATH_TEST_VALUE(RealType, 457479253027996876796.0000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[332] = BOOST_MATH_TEST_VALUE(RealType, 43.79279392736654193607096621073065351333846093585545869129269708118665210449739974464793810457198837);
  zs<RealType>[333] = BOOST_MATH_TEST_VALUE(RealType, 457479253027996876796.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[333] = BOOST_MATH_TEST_VALUE(RealType, 43.79279392736654193607203475643407256688643498213820013963448351011348495071834636284671248086199837);
  zs<RealType>[334] = BOOST_MATH_TEST_VALUE(RealType, 457479253027996876797.0000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[334] = BOOST_MATH_TEST_VALUE(RealType, 43.79279392736654193607310330213749162043440786114053900332242380506462727521879656021426226958496105);
  zs<RealType>[335] = BOOST_MATH_TEST_VALUE(RealType, 457479253027996876797.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[335] = BOOST_MATH_TEST_VALUE(RealType, 43.79279392736654193607417184784091067398237957286247528235651796859096373598026176840193967666274999);
  zs<RealType>[336] = BOOST_MATH_TEST_VALUE(RealType, 914958506055993753595.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[336] = BOOST_MATH_TEST_VALUE(RealType, 44.47058248671115033941120173207718823699817102786625650029939710664855583164086060137262356501253929);
  zs<RealType>[337] = BOOST_MATH_TEST_VALUE(RealType, 914958506055993753596.0000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[337] = BOOST_MATH_TEST_VALUE(RealType, 44.47058248671115033941173618678344688398957490049536600533594487394087848036718134980749301722731352);
  zs<RealType>[338] = BOOST_MATH_TEST_VALUE(RealType, 914958506055993753596.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[338] = BOOST_MATH_TEST_VALUE(RealType, 44.47058248671115033941227064148970553098097848120073848954722626642651975773633390627303626978835838);
  zs<RealType>[339] = BOOST_MATH_TEST_VALUE(RealType, 914958506055993753597.0000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[339] = BOOST_MATH_TEST_VALUE(RealType, 44.47058248671115033941280509619596417797238176998237395293324128442445599007428498582436405251992611);
  zs<RealType>[340] = BOOST_MATH_TEST_VALUE(RealType, 914958506055993753597.5000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[340] = BOOST_MATH_TEST_VALUE(RealType, 44.47058248671115033941333955090222282496378476684027239549398992825366350370700130351606424938744046);
  zs<RealType>[341] = BOOST_MATH_TEST_VALUE(RealType, 1829917012111987507195.500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[341] = BOOST_MATH_TEST_VALUE(RealType, 45.14859833536711018965056513946129162668050892446138815518047627051292293694179480812556619549313064);
  zs<RealType>[342] = BOOST_MATH_TEST_VALUE(RealType, 1829917012111987507196.000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[342] = BOOST_MATH_TEST_VALUE(RealType, 45.14859833536711018965083245509992947480958471611883906387208610588018636449042232643595158517689720);
  zs<RealType>[343] = BOOST_MATH_TEST_VALUE(RealType, 1829917012111987507196.500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[343] = BOOST_MATH_TEST_VALUE(RealType, 45.14859833536711018965109977073856732293866043477021415438855308555056542965567216193304047624510243);
  zs<RealType>[344] = BOOST_MATH_TEST_VALUE(RealType, 1829917012111987507197.000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[344] = BOOST_MATH_TEST_VALUE(RealType, 45.14859833536711018965136708637720517106773608041551342672987720956394620826737050999033807936751171);
  zs<RealType>[345] = BOOST_MATH_TEST_VALUE(RealType, 1829917012111987507197.500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[345] = BOOST_MATH_TEST_VALUE(RealType, 45.14859833536711018965163440201584301919681165305473688089605847796021477615534356598131691567620401);
  zs<RealType>[346] = BOOST_MATH_TEST_VALUE(RealType, 3659834024223975014395.500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[346] = BOOST_MATH_TEST_VALUE(RealType, 45.82683491595294384485628384441561575887407603040339098125590070863967384748247436205190625734118932);
  zs<RealType>[347] = BOOST_MATH_TEST_VALUE(RealType, 3659834024223975014396.000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[347] = BOOST_MATH_TEST_VALUE(RealType, 45.82683491595294384485641754511313063272748535532950445484624262655288217284652009573499125927466059);
  zs<RealType>[348] = BOOST_MATH_TEST_VALUE(RealType, 3659834024223975014396.500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[348] = BOOST_MATH_TEST_VALUE(RealType, 45.82683491595294384485655124581064550658089466199799712956218289431848176597105298416189805067325255);
  zs<RealType>[349] = BOOST_MATH_TEST_VALUE(RealType, 3659834024223975014397.000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[349] = BOOST_MATH_TEST_VALUE(RealType, 45.82683491595294384485668494650816038043430395040886900540372151194146008896781208120907682446939362);
  zs<RealType>[350] = BOOST_MATH_TEST_VALUE(RealType, 3659834024223975014397.500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[350] = BOOST_MATH_TEST_VALUE(RealType, 45.82683491595294384485681864720567525428771322056212008237085847942680460394853644075297572979160046);
  zs<RealType>[351] = BOOST_MATH_TEST_VALUE(RealType, 7319668048447950028795.500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[351] = BOOST_MATH_TEST_VALUE(RealType, 46.50528594879636847126393241245457792327323285439769210757132628000124992150426204283421914567175062);
  zs<RealType>[352] = BOOST_MATH_TEST_VALUE(RealType, 7319668048447950028796.000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[352] = BOOST_MATH_TEST_VALUE(RealType, 46.50528594879636847126399928363674654174459322702292762434851831465004062253014895509105956594317070);
  zs<RealType>[353] = BOOST_MATH_TEST_VALUE(RealType, 7319668048447950028796.500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[353] = BOOST_MATH_TEST_VALUE(RealType, 46.50528594879636847126406615481891516021595359508227640114777678096555406499545632733378131414467863);
  zs<RealType>[354] = BOOST_MATH_TEST_VALUE(RealType, 7319668048447950028797.000000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[354] = BOOST_MATH_TEST_VALUE(RealType, 46.50528594879636847126413302600108377868731395857573843796910167894841388830471951540349934300433290);
  zs<RealType>[355] = BOOST_MATH_TEST_VALUE(RealType, 7319668048447950028797.500000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[355] = BOOST_MATH_TEST_VALUE(RealType, 46.50528594879636847126419989718325239715867431750331373481249300859924373186247387514132847746950458);
  zs<RealType>[356] = BOOST_MATH_TEST_VALUE(RealType, 14639336096895900057595.50000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[356] = BOOST_MATH_TEST_VALUE(RealType, 47.18394541655595353726859251633008985542795093110678176543525265373739682696782954566791223497853278);
  zs<RealType>[357] = BOOST_MATH_TEST_VALUE(RealType, 14639336096895900057596.00000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[357] = BOOST_MATH_TEST_VALUE(RealType, 47.18394541655595353726862596204760125343156619941158006313108818081160492017518779133670783309629777);
  zs<RealType>[358] = BOOST_MATH_TEST_VALUE(RealType, 14639336096895900057596.50000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[358] = BOOST_MATH_TEST_VALUE(RealType, 47.18394541655595353726865940776511265143518146657454680704151512746306706865464848189500564360309989);
  zs<RealType>[359] = BOOST_MATH_TEST_VALUE(RealType, 14639336096895900057597.00000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[359] = BOOST_MATH_TEST_VALUE(RealType, 47.18394541655595353726869285348262404943879673259568199716653349369186125242127832914687715856217447);
  zs<RealType>[360] = BOOST_MATH_TEST_VALUE(RealType, 14639336096895900057597.50000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[360] = BOOST_MATH_TEST_VALUE(RealType, 47.18394541655595353726872629920013544744241199747498563350614327949806545149014404489639386204785740);
  zs<RealType>[361] = BOOST_MATH_TEST_VALUE(RealType, 29278672193791800115195.50000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[361] = BOOST_MATH_TEST_VALUE(RealType, 47.86280754988806283869898641897124874506124538863135227082696257498669984107495807372390982964298765);
  zs<RealType>[362] = BOOST_MATH_TEST_VALUE(RealType, 29278672193791800115196.00000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[362] = BOOST_MATH_TEST_VALUE(RealType, 47.86280754988806283869900314675402022865409817077027470411443725892317844329022583627504362168195643);
  zs<RealType>[363] = BOOST_MATH_TEST_VALUE(RealType, 29278672193791800115196.50000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[363] = BOOST_MATH_TEST_VALUE(RealType, 47.86280754988806283869901987453679171224695095262365180124915574419262646462548365901592461409111822);
  zs<RealType>[364] = BOOST_MATH_TEST_VALUE(RealType, 29278672193791800115197.00000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[364] = BOOST_MATH_TEST_VALUE(RealType, 47.86280754988806283869903660231956319583980373419148356223111803079505365563012583778554165327574562);
  zs<RealType>[365] = BOOST_MATH_TEST_VALUE(RealType, 29278672193791800115197.50000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[365] = BOOST_MATH_TEST_VALUE(RealType, 47.86280754988806283869905333010233467943265651547376998706032411873046976685354666842288358514164676);
  zs<RealType>[366] = BOOST_MATH_TEST_VALUE(RealType, 58557344387583600230395.50000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[366] = BOOST_MATH_TEST_VALUE(RealType, 48.54186681407528073135559278566008113536709515501428437696966434378641333376283319594991159197529021);
  zs<RealType>[367] = BOOST_MATH_TEST_VALUE(RealType, 58557344387583600230396.00000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[367] = BOOST_MATH_TEST_VALUE(RealType, 48.54186681407528073135560115194668751765523308763717061100258732052320085375724211692206634017417835);
  zs<RealType>[368] = BOOST_MATH_TEST_VALUE(RealType, 58557344387583600230396.50000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[368] = BOOST_MATH_TEST_VALUE(RealType, 48.54186681407528073135560951823329389994337102018864925304218198091079593677878710603131163142362874);
  zs<RealType>[369] = BOOST_MATH_TEST_VALUE(RealType, 58557344387583600230397.00000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[369] = BOOST_MATH_TEST_VALUE(RealType, 48.54186681407528073135561788451990028223150895266872030308844832494919980201645683875988736222168974);
  zs<RealType>[370] = BOOST_MATH_TEST_VALUE(RealType, 58557344387583600230397.50000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[370] = BOOST_MATH_TEST_VALUE(RealType, 48.54186681407528073135562625080650666451964688507738376114138635263841366865923999059003342903518356);
  zs<RealType>[371] = BOOST_MATH_TEST_VALUE(RealType, 117114688775167200460795.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[371] = BOOST_MATH_TEST_VALUE(RealType, 49.22111789654023166917393998803300413713566990752077661423207867413014717692257524895032484088002638);
  zs<RealType>[372] = BOOST_MATH_TEST_VALUE(RealType, 117114688775167200460796.0000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[372] = BOOST_MATH_TEST_VALUE(RealType, 49.22111789654023166917394417234185551157598495411034248495095320790548697334562191650844059041476016);
  zs<RealType>[373] = BOOST_MATH_TEST_VALUE(RealType, 117114688775167200460796.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[373] = BOOST_MATH_TEST_VALUE(RealType, 49.22111789654023166917394835665070688601630000068205128804849669368418438086966818342794853392747975);
  zs<RealType>[374] = BOOST_MATH_TEST_VALUE(RealType, 117114688775167200460797.0000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[374] = BOOST_MATH_TEST_VALUE(RealType, 49.22111789654023166917395254095955826045661504723590302352470913146623955193835326180329431243220210);
  zs<RealType>[375] = BOOST_MATH_TEST_VALUE(RealType, 117114688775167200460797.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[375] = BOOST_MATH_TEST_VALUE(RealType, 49.22111789654023166917395672526840963489693009377189769137959052125165263899531636372892356694099191);
  zs<RealType>[376] = BOOST_MATH_TEST_VALUE(RealType, 234229377550334400921595.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[376] = BOOST_MATH_TEST_VALUE(RealType, 49.90055569517561383066060297122022964231700635525977853025814596096151371140753421576539605970700861);
  zs<RealType>[377] = BOOST_MATH_TEST_VALUE(RealType, 234229377550334400921596.0000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[377] = BOOST_MATH_TEST_VALUE(RealType, 49.90055569517561383066060506394202934831808914814125072797602761455168951650504013758145865501313880);
  zs<RealType>[378] = BOOST_MATH_TEST_VALUE(RealType, 234229377550334400921596.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[378] = BOOST_MATH_TEST_VALUE(RealType, 49.90055569517561383066060715666382905431917194101825740114881990816754906595137018341539085351050643);
  zs<RealType>[379] = BOOST_MATH_TEST_VALUE(RealType, 234229377550334400921597.0000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[379] = BOOST_MATH_TEST_VALUE(RealType, 49.90055569517561383066060924938562876032025473389079854977652284180909237880745343386717050876814609);
  zs<RealType>[380] = BOOST_MATH_TEST_VALUE(RealType, 234229377550334400921597.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[380] = BOOST_MATH_TEST_VALUE(RealType, 49.90055569517561383066061134210742846632133752675887417385913641547631947413421896953677547435497030);
  zs<RealType>[381] = BOOST_MATH_TEST_VALUE(RealType, 468458755100668801843195.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[381] = BOOST_MATH_TEST_VALUE(RealType, 50.58017530742747300164561317487206707303802427632079894747824628285067346330316982070607470750276538);
  zs<RealType>[382] = BOOST_MATH_TEST_VALUE(RealType, 468458755100668801843196.0000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[382] = BOOST_MATH_TEST_VALUE(RealType, 50.58017530742747300164561422150925313668179339445554345979082699726978571301972512528171705027852141);
  zs<RealType>[383] = BOOST_MATH_TEST_VALUE(RealType, 468458755100668801843196.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[383] = BOOST_MATH_TEST_VALUE(RealType, 50.58017530742747300164561526814643920032556251258917128490508500217173303906663500224210793499988710);
  zs<RealType>[384] = BOOST_MATH_TEST_VALUE(RealType, 468458755100668801843197.0000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[384] = BOOST_MATH_TEST_VALUE(RealType, 50.58017530742747300164561631478362526396933163072168242282102029755651544382718152403729363436471691);
  zs<RealType>[385] = BOOST_MATH_TEST_VALUE(RealType, 468458755100668801843197.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[385] = BOOST_MATH_TEST_VALUE(RealType, 50.58017530742747300164561736142081132761310074885307687353863288342413292968464676311732042107085768);
  zs<RealType>[386] = BOOST_MATH_TEST_VALUE(RealType, 936917510201337603686395.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[386] = BOOST_MATH_TEST_VALUE(RealType, 51.25997202007432193650352849612123815153250964411406464644865827787863050268704653756510450468474031);
  zs<RealType>[387] = BOOST_MATH_TEST_VALUE(RealType, 936917510201337603686396.0000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[387] = BOOST_MATH_TEST_VALUE(RealType, 51.25997202007432193650352901957441589305691661161083072884708823213628905949128284896989478240192251);
  zs<RealType>[388] = BOOST_MATH_TEST_VALUE(RealType, 936917510201337603686396.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[388] = BOOST_MATH_TEST_VALUE(RealType, 51.25997202007432193650352954302759363458132357910731756493593984931372662548695557807862606879386793);
  zs<RealType>[389] = BOOST_MATH_TEST_VALUE(RealType, 936917510201337603686397.0000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[389] = BOOST_MATH_TEST_VALUE(RealType, 51.25997202007432193650353006648077137610573054660352515471521312941094320097205602449555540058022069);
  zs<RealType>[390] = BOOST_MATH_TEST_VALUE(RealType, 936917510201337603686397.5000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[390] = BOOST_MATH_TEST_VALUE(RealType, 51.25997202007432193650353058993394911763013751409945349818490807242793878624457548782493981448062444);
  zs<RealType>[391] = BOOST_MATH_TEST_VALUE(RealType, 1873835020402675207372795.500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[391] = BOOST_MATH_TEST_VALUE(RealType, 51.93994129964973319883394011661732720227488151829956542922851698538021522622862634588975916535124465);
  zs<RealType>[392] = BOOST_MATH_TEST_VALUE(RealType, 1873835020402675207372796.000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[392] = BOOST_MATH_TEST_VALUE(RealType, 51.93994129964973319883394037840949666263721928778536979472743471765062641780884974428552475054902084);
  zs<RealType>[393] = BOOST_MATH_TEST_VALUE(RealType, 1873835020402675207372796.500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[393] = BOOST_MATH_TEST_VALUE(RealType, 51.93994129964973319883394064020166612299955705727110433050351349588454562078192850302725642051437996);
  zs<RealType>[394] = BOOST_MATH_TEST_VALUE(RealType, 1873835020402675207372797.000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[394] = BOOST_MATH_TEST_VALUE(RealType, 51.93994129964973319883394090199383558336189482675676903655675332008197283518512140013107034885992383);
  zs<RealType>[395] = BOOST_MATH_TEST_VALUE(RealType, 1873835020402675207372797.500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[395] = BOOST_MATH_TEST_VALUE(RealType, 51.93994129964973319883394116378600504372423259624236391288715419024290806105568721361308270919825427);
  zs<RealType>[396] = BOOST_MATH_TEST_VALUE(RealType, 3747670040805350414745595.500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[396] = BOOST_MATH_TEST_VALUE(RealType, 52.62007878346056146247780063098329226609433897361719442410811576805026853884099017649824976199940727);
  zs<RealType>[397] = BOOST_MATH_TEST_VALUE(RealType, 3747670040805350414745596.000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[397] = BOOST_MATH_TEST_VALUE(RealType, 52.62007878346056146247780076191134345038026463351561571010291457420668319272155773248903735412098606);
  zs<RealType>[398] = BOOST_MATH_TEST_VALUE(RealType, 3747670040805350414745596.500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[398] = BOOST_MATH_TEST_VALUE(RealType, 52.62007878346056146247780089283939463466619029341401953424656920848830320946683691731827935418315374);
  zs<RealType>[399] = BOOST_MATH_TEST_VALUE(RealType, 3747670040805350414745597.000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[399] = BOOST_MATH_TEST_VALUE(RealType, 52.62007878346056146247780102376744581895211595331240589653907967089512858908148627964024886502244786);
  zs<RealType>[400] = BOOST_MATH_TEST_VALUE(RealType, 3747670040805350414745597.500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[400] = BOOST_MATH_TEST_VALUE(RealType, 52.62007878346056146247780115469549700323804161321077479698044596142715933157016436810921898947540595);
  zs<RealType>[401] = BOOST_MATH_TEST_VALUE(RealType, 7495340081610700829491195.500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[401] = BOOST_MATH_TEST_VALUE(RealType, 53.30038027115703847068472014163526750381732629683760414977652728398062761064036427478928134573151026);
  zs<RealType>[402] = BOOST_MATH_TEST_VALUE(RealType, 7495340081610700829491196.000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[402] = BOOST_MATH_TEST_VALUE(RealType, 53.30038027115703847068472020711487963758347144588443668839144673170417842073148009181357862290045625);
  zs<RealType>[403] = BOOST_MATH_TEST_VALUE(RealType, 7495340081610700829491196.500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[403] = BOOST_MATH_TEST_VALUE(RealType, 53.30038027115703847068472027259449177134961659493126486046636359084837495933357183600363141237458038);
  zs<RealType>[404] = BOOST_MATH_TEST_VALUE(RealType, 7495340081610700829491197.000000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[404] = BOOST_MATH_TEST_VALUE(RealType, 53.30038027115703847068472033807410390511576174397808866600127786141321722644722197229302630959626241);
  zs<RealType>[405] = BOOST_MATH_TEST_VALUE(RealType, 7495340081610700829491197.500000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[405] = BOOST_MATH_TEST_VALUE(RealType, 53.30038027115703847068472040355371603888190689302490810499618954339870522207301296561534991000788215);
  zs<RealType>[406] = BOOST_MATH_TEST_VALUE(RealType, 14990680163221401658982395.50000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[406] = BOOST_MATH_TEST_VALUE(RealType, 53.98084171681467787755159106988030207256464803953686021292403364974364396941211684129467560225025844);
  zs<RealType>[407] = BOOST_MATH_TEST_VALUE(RealType, 14990680163221401658982396.00000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[407] = BOOST_MATH_TEST_VALUE(RealType, 53.98084171681467787755159110262771031569979287106207337111164630527719599256075269757642116173324604);
  zs<RealType>[408] = BOOST_MATH_TEST_VALUE(RealType, 14990680163221401658982396.50000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[408] = BOOST_MATH_TEST_VALUE(RealType, 53.98084171681467787755159113537511855883493770258728543740166820971925093788175661756375535881272212);
  zs<RealType>[409] = BOOST_MATH_TEST_VALUE(RealType, 14990680163221401658982397.00000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[409] = BOOST_MATH_TEST_VALUE(RealType, 53.98084171681467787755159116812252680197008253411249641179409936306980880537520142720731872132566761);
  zs<RealType>[410] = BOOST_MATH_TEST_VALUE(RealType, 14990680163221401658982397.50000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[410] = BOOST_MATH_TEST_VALUE(RealType, 53.98084171681467787755159120086993504510522736563770629428893976532886959504115995245775177710906343);
  zs<RealType>[411] = BOOST_MATH_TEST_VALUE(RealType, 29981360326442803317964795.50000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[411] = BOOST_MATH_TEST_VALUE(RealType, 54.66145922149126997883966829050076057809819624810698856060190684715860519885138243117049542515690575);
  zs<RealType>[412] = BOOST_MATH_TEST_VALUE(RealType, 29981360326442803317964796.00000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[412] = BOOST_MATH_TEST_VALUE(RealType, 54.66145922149126997883966830687817369095124791690640312773832665397033630245360990420711923237519040);
  zs<RealType>[413] = BOOST_MATH_TEST_VALUE(RealType, 29981360326442803317964796.50000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[413] = BOOST_MATH_TEST_VALUE(RealType, 54.66145922149126997883966832325558680380429958570581742183631816048451505614209676496718115749779207);
  zs<RealType>[414] = BOOST_MATH_TEST_VALUE(RealType, 29981360326442803317964797.00000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[414] = BOOST_MATH_TEST_VALUE(RealType, 54.66145922149126997883966833963299991665735125450523144289588136670114145991685211886837782686269634);
  zs<RealType>[415] = BOOST_MATH_TEST_VALUE(RealType, 29981360326442803317964797.50000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[415] = BOOST_MATH_TEST_VALUE(RealType, 54.66145922149126997883966835601041302951040292330464519091701627262021551377788507132840586680788883);
  zs<RealType>[416] = BOOST_MATH_TEST_VALUE(RealType, 59962720652885606635929595.50000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[416] = BOOST_MATH_TEST_VALUE(RealType, 55.34222902622527492285082008163468252474529527756164189713179328516307078639643598839828067574841328);
  zs<RealType>[417] = BOOST_MATH_TEST_VALUE(RealType, 59962720652885606635929596.00000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[417] = BOOST_MATH_TEST_VALUE(RealType, 55.34222902622527492285082008982519917191333658353569747274840211384876036940931564577326778739937441);
  zs<RealType>[418] = BOOST_MATH_TEST_VALUE(RealType, 59962720652885606635929596.50000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[418] = BOOST_MATH_TEST_VALUE(RealType, 55.34222902622527492285082009801571581908137788950975298008978578466315835299634785170757312736330548);
  zs<RealType>[419] = BOOST_MATH_TEST_VALUE(RealType, 59962720652885606635929597.00000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[419] = BOOST_MATH_TEST_VALUE(RealType, 55.34222902622527492285082010620623246624941919548380841915594429760626473715753374464347315926011244);
  zs<RealType>[420] = BOOST_MATH_TEST_VALUE(RealType, 59962720652885606635929597.50000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[420] = BOOST_MATH_TEST_VALUE(RealType, 55.34222902622527492285082011439674911341746050145786378994687765267807952189287446302324434670970122);
  zs<RealType>[421] = BOOST_MATH_TEST_VALUE(RealType, 119925441305771213271859195.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[421] = BOOST_MATH_TEST_VALUE(RealType, 56.02314750544467013866005744109529240544406880337828236840721418957676877238000505091488001795651201);
  zs<RealType>[422] = BOOST_MATH_TEST_VALUE(RealType, 119925441305771213271859196.0000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[422] = BOOST_MATH_TEST_VALUE(RealType, 56.02314750544467013866005744519143435523841317719330604019896557212385538515430219236320724445839119);
  zs<RealType>[423] = BOOST_MATH_TEST_VALUE(RealType, 119925441305771213271859196.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[423] = BOOST_MATH_TEST_VALUE(RealType, 56.02314750544467013866005744928757630503275755100832969491810004678229237746659550758636675578428967);
  zs<RealType>[424] = BOOST_MATH_TEST_VALUE(RealType, 119925441305771213271859197.0000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[424] = BOOST_MATH_TEST_VALUE(RealType, 56.02314750544467013866005745338371825482710192482335333256461761355207974931688513892197266161560978);
  zs<RealType>[425] = BOOST_MATH_TEST_VALUE(RealType, 119925441305771213271859197.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[425] = BOOST_MATH_TEST_VALUE(RealType, 56.02314750544467013866005745747986020462144629863837695313851827243321750070517122870763907163375388);
  zs<RealType>[426] = BOOST_MATH_TEST_VALUE(RealType, 239850882611542426543718395.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[426] = BOOST_MATH_TEST_VALUE(RealType, 56.70421116075780244670574964727179808543930859767697217112818536350797368921774672172293682473225055);
  zs<RealType>[427] = BOOST_MATH_TEST_VALUE(RealType, 239850882611542426543718396.0000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[427] = BOOST_MATH_TEST_VALUE(RealType, 56.70421116075780244670574964932030053738025902058853016475070045198068571647010983824313512015423280);
  zs<RealType>[428] = BOOST_MATH_TEST_VALUE(RealType, 239850882611542426543718396.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[428] = BOOST_MATH_TEST_VALUE(RealType, 56.70421116075780244670574965136880298932120944350008815410413130287269798806862098501994253827046627);
  zs<RealType>[429] = BOOST_MATH_TEST_VALUE(RealType, 239850882611542426543718397.0000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[429] = BOOST_MATH_TEST_VALUE(RealType, 56.70421116075780244670574965341730544126215986641164613918847791618401050401328017984950518641062487);
  zs<RealType>[430] = BOOST_MATH_TEST_VALUE(RealType, 239850882611542426543718397.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[430] = BOOST_MATH_TEST_VALUE(RealType, 56.70421116075780244670574965546580789320311028932320412000374029191462326430408744052796917190438247);
  zs<RealType>[431] = BOOST_MATH_TEST_VALUE(RealType, 479701765223084853087436795.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[431] = BOOST_MATH_TEST_VALUE(RealType, 57.38541661510006336367991531277886245290205731446363101869106286487064764410271386990754234755642855);
  zs<RealType>[432] = BOOST_MATH_TEST_VALUE(RealType, 479701765223084853087436796.0000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[432] = BOOST_MATH_TEST_VALUE(RealType, 57.38541661510006336367991531380332442757645839844021552846525681769285188952591218983091633569244771);
  zs<RealType>[433] = BOOST_MATH_TEST_VALUE(RealType, 479701765223084853087436796.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[433] = BOOST_MATH_TEST_VALUE(RealType, 57.38541661510006336367991531482778640225085948241680003717195267123172907693535943393631252233528007);
  zs<RealType>[434] = BOOST_MATH_TEST_VALUE(RealType, 479701765223084853087436797.0000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[434] = BOOST_MATH_TEST_VALUE(RealType, 57.38541661510006336367991531585224837692526056639338454481115042548727920633105560444873053490520850);
  zs<RealType>[435] = BOOST_MATH_TEST_VALUE(RealType, 479701765223084853087436797.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[435] = BOOST_MATH_TEST_VALUE(RealType, 57.38541661510006336367991531687671035159966165036996905138285008045950227771300070359317000082251587);
  zs<RealType>[436] = BOOST_MATH_TEST_VALUE(RealType, 959403530446169706174873595.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[436] = BOOST_MATH_TEST_VALUE(RealType, 58.06676060721227034055554057973484251633179872231742522867479304887734423959008177810507929372980502);
  zs<RealType>[437] = BOOST_MATH_TEST_VALUE(RealType, 959403530446169706174873596.0000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[437] = BOOST_MATH_TEST_VALUE(RealType, 58.06676060721227034055554058024717646818581642633527555088229229848978856650264562082058837873668151);
  zs<RealType>[438] = BOOST_MATH_TEST_VALUE(RealType, 959403530446169706174873596.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[438] = BOOST_MATH_TEST_VALUE(RealType, 58.06676060721227034055554058075951042003983413035312587282286158171800872330936800027595921615283052);
  zs<RealType>[439] = BOOST_MATH_TEST_VALUE(RealType, 959403530446169706174873597.0000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[439] = BOOST_MATH_TEST_VALUE(RealType, 58.06676060721227034055554058127184437189385183437097619449650089856200471001024891674937552098289649);
  zs<RealType>[440] = BOOST_MATH_TEST_VALUE(RealType, 959403530446169706174873597.5000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[440] = BOOST_MATH_TEST_VALUE(RealType, 58.06676060721227034055554058178417832374786953838882651590321024902177652660528837051902100823152388);
  zs<RealType>[441] = BOOST_MATH_TEST_VALUE(RealType, 1918807060892339412349747195.500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[441] = BOOST_MATH_TEST_VALUE(RealType, 58.74823998642851741200079363299726206609746705489474024632685397908027036826962303868666955918111538);
  zs<RealType>[442] = BOOST_MATH_TEST_VALUE(RealType, 1918807060892339412349747196.000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[442] = BOOST_MATH_TEST_VALUE(RealType, 58.74823998642851741200079363325347935999834589083288276642972035197649807101708168936227006895175343);
  zs<RealType>[443] = BOOST_MATH_TEST_VALUE(RealType, 1918807060892339412349747196.500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[443] = BOOST_MATH_TEST_VALUE(RealType, 58.74823998642851741200079363350969665389922472677102528646584069120432472031910137509227273894302196);
  zs<RealType>[444] = BOOST_MATH_TEST_VALUE(RealType, 1918807060892339412349747197.000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[444] = BOOST_MATH_TEST_VALUE(RealType, 58.74823998642851741200079363376591394780010356270916780643521499676375031617568209591145770868885078);
  zs<RealType>[445] = BOOST_MATH_TEST_VALUE(RealType, 1918807060892339412349747197.500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[445] = BOOST_MATH_TEST_VALUE(RealType, 58.74823998642851741200079363402213124170098239864731032633784326865477485858682385185460511772316968);
  zs<RealType>[446] = BOOST_MATH_TEST_VALUE(RealType, 3837614121784678824699494395.500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[446] = BOOST_MATH_TEST_VALUE(RealType, 59.42985170775297409538727174934638672334406646723725983740951105026349221807009071850919012663041550);
  zs<RealType>[447] = BOOST_MATH_TEST_VALUE(RealType, 3837614121784678824699494396.000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[447] = BOOST_MATH_TEST_VALUE(RealType, 59.42985170775297409538727174947451996655623151231730299282486521387670139214046503255303771865999613);
  zs<RealType>[448] = BOOST_MATH_TEST_VALUE(RealType, 3837614121784678824699494396.500000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[448] = BOOST_MATH_TEST_VALUE(RealType, 59.42985170775297409538727174960265320976839655739734614822352956044016003363242954851814317386540390);
  zs<RealType>[449] = BOOST_MATH_TEST_VALUE(RealType, 3837614121784678824699494397.000000000000000000000000000000000000000000000000000000000000000000000000);
  ws<RealType>[449] = BOOST_MATH_TEST_VALUE(RealType, 59.42985170775297409538727174973078645298056160247738930360550408995386814254598426640885488605351946);
};
// End of lambert_w_mp_high_values.ipp 
