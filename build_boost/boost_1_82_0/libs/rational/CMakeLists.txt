# Generated by `boostdep --cmake rational`
# Copyright 2020 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.5...3.16)

project(boost_rational VERSION "${BOOST_SUPERPROJECT_VERSION}" LANGUAGES CXX)

add_library(boost_rational INTERFACE)
add_library(Boost::rational ALIAS boost_rational)

target_include_directories(boost_rational INTERFACE include)

target_link_libraries(boost_rational
  INTERFACE
    Boost::assert
    Boost::config
    Boost::core
    Boost::integer
    Boost::static_assert
    Boost::throw_exception
    Boost::type_traits
    Boost::utility
)

if(BUILD_TESTING AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")

  add_subdirectory(test)

endif()

