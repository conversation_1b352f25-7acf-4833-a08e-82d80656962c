Rational, part of collection of the [Boost C++ Libraries](http://github.com/boostorg), provides an implementation of rational numbers.

### License

Distributed under the [Boost Software License, Version 1.0](http://www.boost.org/LICENSE_1_0.txt).

### Properties

* C++03
* Header-Only

### Build Status

Branch          | Travis | Appveyor | Coverity Scan | codecov.io | Deps | Docs | Tests |
:-------------: | ------ | -------- | ------------- | ---------- | ---- | ---- | ----- |
[`master`](https://github.com/boostorg/rational/tree/master) | [![Build Status](https://travis-ci.org/boostorg/rational.svg?branch=master)](https://travis-ci.org/boostorg/rational) | [![Build status](https://ci.appveyor.com/api/projects/status/8a2on7yb2xck80fa/branch/master?svg=true)](https://ci.appveyor.com/project/jeking3/rational-lqu73/branch/master) | [![Coverity Scan Build Status](https://scan.coverity.com/projects/16002/badge.svg)](https://scan.coverity.com/projects/boostorg-rational) | [![codecov](https://codecov.io/gh/boostorg/rational/branch/master/graph/badge.svg)](https://codecov.io/gh/boostorg/rational/branch/master)| [![Deps](https://img.shields.io/badge/deps-master-brightgreen.svg)](https://pdimov.github.io/boostdep-report/master/rational.html) | [![Documentation](https://img.shields.io/badge/docs-master-brightgreen.svg)](http://www.boost.org/doc/libs/master/doc/html/rational.html) | [![Enter the Matrix](https://img.shields.io/badge/matrix-master-brightgreen.svg)](http://www.boost.org/development/tests/master/developer/rational.html)
[`develop`](https://github.com/boostorg/rational/tree/develop) | [![Build Status](https://travis-ci.org/boostorg/rational.svg?branch=develop)](https://travis-ci.org/boostorg/rational) | [![Build status](https://ci.appveyor.com/api/projects/status/8a2on7yb2xck80fa/branch/develop?svg=true)](https://ci.appveyor.com/project/jeking3/rational-lqu73/branch/develop) | [![Coverity Scan Build Status](https://scan.coverity.com/projects/16002/badge.svg)](https://scan.coverity.com/projects/boostorg-rational) | [![codecov](https://codecov.io/gh/boostorg/rational/branch/develop/graph/badge.svg)](https://codecov.io/gh/boostorg/rational/branch/develop) | [![Deps](https://img.shields.io/badge/deps-develop-brightgreen.svg)](https://pdimov.github.io/boostdep-report/develop/rational.html) | [![Documentation](https://img.shields.io/badge/docs-develop-brightgreen.svg)](http://www.boost.org/doc/libs/develop/doc/html/rational.html) | [![Enter the Matrix](https://img.shields.io/badge/matrix-develop-brightgreen.svg)](http://www.boost.org/development/tests/develop/developer/rational.html)

### Directories

| Name        | Purpose                        |
| ----------- | ------------------------------ |
| `include`   | header                         |
| `test`      | unit tests                     |

### More information

* [Ask questions](http://stackoverflow.com/questions/ask?tags=c%2B%2B,boost,boost-rational)
* [Report bugs](https://github.com/boostorg/rational/issues): Be sure to mention Boost version, platform and compiler you're using. A small compilable code sample to reproduce the problem is always good as well.
* Submit your patches as pull requests against **develop** branch. Note that by submitting patches you agree to license your modifications under the [Boost Software License, Version 1.0](http://www.boost.org/LICENSE_1_0.txt).
* Discussions about the library are held on the [Boost developers mailing list](http://www.boost.org/community/groups.html#main). Be sure to read the [discussion policy](http://www.boost.org/community/policy.html) before posting and add the `[rational]` tag at the beginning of the subject line.

