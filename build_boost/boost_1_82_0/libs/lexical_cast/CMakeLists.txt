# Generated by `boostdep --cmake lexical_cast`
# Copyright 2020 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.5...3.16)

project(boost_lexical_cast VERSION "${BOOST_SUPERPROJECT_VERSION}" LANGUAGES CXX)

add_library(boost_lexical_cast INTERFACE)
add_library(Boost::lexical_cast ALIAS boost_lexical_cast)

target_include_directories(boost_lexical_cast INTERFACE include)

target_link_libraries(boost_lexical_cast
  INTERFACE
    Boost::array
    Boost::assert
    Boost::config
    Boost::container
    Boost::core
    Boost::integer
    Boost::numeric_conversion
    Boost::range
    Boost::static_assert
    Boost::throw_exception
    Boost::type_traits
)

if(BUILD_TESTING AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")

  add_subdirectory(test)

endif()

