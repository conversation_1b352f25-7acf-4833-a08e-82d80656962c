# Copyright 2018 <PERSON>
# Copyright 2018 <PERSON><PERSON>
# Distributed under the Boost Software License, Version 1.0.
# See accompanying file LICENSE_1_0.txt or copy at https://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.5...3.20)

project(boost_iterator VERSION "${BOOST_SUPERPROJECT_VERSION}" LANGUAGES CXX)

add_library(boost_iterator INTERFACE)
add_library(Boost::iterator ALIAS boost_iterator)

target_include_directories(boost_iterator INTERFACE include)

target_link_libraries(boost_iterator
    INTERFACE
        Boost::assert
        Boost::concept_check
        Boost::config
        Boost::conversion
        Boost::core
        Boost::detail
        Boost::function_types
        Boost::fusion
        Boost::mpl
        Boost::optional
        Boost::smart_ptr
        Boost::static_assert
        Boost::type_traits
        Boost::utility
)
