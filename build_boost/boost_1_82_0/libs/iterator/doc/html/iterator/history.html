<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>History</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.Iterator">
<link rel="up" href="../index.html" title="Chapter 1. Boost.Iterator">
<link rel="prev" href="upgrading.html" title="Upgrading from the old Boost Iterator Adaptor Library">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="upgrading.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="iterator.history"></a><a class="link" href="history.html" title="History">History</a>
</h2></div></div></div>
<p>
      In 2000 Dave Abrahams was writing an iterator for a container of pointers,
      which would access the pointed-to elements when dereferenced. Naturally, being
      a library writer, he decided to generalize the idea and the Boost Iterator
      Adaptor library was born. Dave was inspired by some writings of Andrei Alexandrescu
      and chose a policy based design (though he probably didn't capture Andrei's
      idea very well - there was only one policy class for all the iterator's orthogonal
      properties). Soon Jeremy Siek realized he would need the library and they worked
      together to produce a "Boostified" version, which was reviewed and
      accepted into the library. They wrote a paper and made several important revisions
      of the code.
    </p>
<p>
      Eventually, several shortcomings of the older library began to make the need
      for a rewrite apparent. Dave and Jeremy started working at the Santa Cruz C++
      committee meeting in 2002, and had quickly generated a working prototype. At
      the urging of Mat Marcus, they decided to use the GenVoca/CRTP pattern approach,
      and moved the policies into the iterator class itself. Thomas Witt expressed
      interest and became the voice of strict compile-time checking for the project,
      adding uses of the SFINAE technique to eliminate false converting constructors
      and operators from the overload set. He also recognized the need for a separate
      <code class="computeroutput"><span class="identifier">iterator_facade</span></code>, and factored
      it out of <code class="computeroutput"><span class="identifier">iterator_adaptor</span></code>.
      Finally, after a near-complete rewrite of the prototype, they came up with
      the library you see today.
    </p>
<div class="blockquote"><blockquote class="blockquote"><p>
        [Coplien, 1995] Coplien, J., Curiously Recurring Template Patterns, C++ Report,
        February 1995, pp. 24-27.
      </p></blockquote></div>
</div>
<div class="copyright-footer">Copyright © 2003, 2005 David Abrahams Jeremy Siek Thomas
      Witt<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at &lt;ulink url="http://www.boost.org/LICENSE_1_0.txt"&gt;
        http://www.boost.org/LICENSE_1_0.txt &lt;/ulink&gt;)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="upgrading.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
</body>
</html>
