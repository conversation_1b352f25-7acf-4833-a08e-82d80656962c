<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Algorithms</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.Iterator">
<link rel="up" href="../index.html" title="Chapter 1. Boost.Iterator">
<link rel="prev" href="utilities/traits.html" title="Type Traits">
<link rel="next" href="algorithms/distance.html" title="Function template distance()">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="utilities/traits.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="algorithms/distance.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="iterator.algorithms"></a><a class="link" href="algorithms.html" title="Algorithms">Algorithms</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="algorithms.html#iterator.algorithms.advance">Function template <code class="computeroutput"><span class="identifier">advance</span><span class="special">()</span></code></a></span></dt>
<dt><span class="section"><a href="algorithms/distance.html">Function template <code class="computeroutput"><span class="identifier">distance</span><span class="special">()</span></code></a></span></dt>
<dt><span class="section"><a href="algorithms/next_prior.html">Function templates <code class="computeroutput"><span class="identifier">next</span><span class="special">()</span></code> and
      <code class="computeroutput"><span class="identifier">prior</span><span class="special">()</span></code></a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="iterator.algorithms.advance"></a><a class="link" href="algorithms.html#iterator.algorithms.advance" title="Function template advance()">Function template <code class="computeroutput"><span class="identifier">advance</span><span class="special">()</span></code></a>
</h3></div></div></div>
<p>
        The <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">iterators</span><span class="special">::</span><span class="identifier">advance</span></code> function template is an adapted
        version of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">advance</span></code> for the Boost iterator <a class="link" href="concepts/traversal.html" title="Traversal">traversal
        concepts</a>.
      </p>
<h5>
<a name="iterator.algorithms.advance.h0"></a>
        <span class="phrase"><a name="iterator.algorithms.advance.header"></a></span><a class="link" href="algorithms.html#iterator.algorithms.advance.header">Header</a>
      </h5>
<pre class="programlisting"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">iterator</span><span class="special">/</span><span class="identifier">advance</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<h5>
<a name="iterator.algorithms.advance.h1"></a>
        <span class="phrase"><a name="iterator.algorithms.advance.synopsis"></a></span><a class="link" href="algorithms.html#iterator.algorithms.advance.synopsis">Synopsis</a>
      </h5>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Distance</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span> <span class="keyword">void</span> <span class="identifier">advance</span><span class="special">(</span><span class="identifier">Iterator</span><span class="special">&amp;</span> <span class="identifier">it</span><span class="special">,</span> <span class="identifier">Distance</span> <span class="identifier">n</span><span class="special">);</span>
</pre>
<h5>
<a name="iterator.algorithms.advance.h2"></a>
        <span class="phrase"><a name="iterator.algorithms.advance.description"></a></span><a class="link" href="algorithms.html#iterator.algorithms.advance.description">Description</a>
      </h5>
<p>
        Moves <code class="computeroutput"><span class="identifier">it</span></code> forward by <code class="computeroutput"><span class="identifier">n</span></code> increments (or backward by <code class="computeroutput"><span class="special">|</span><span class="identifier">n</span><span class="special">|</span></code>
        decrements if <code class="computeroutput"><span class="identifier">n</span></code> is negative).
      </p>
<h5>
<a name="iterator.algorithms.advance.h3"></a>
        <span class="phrase"><a name="iterator.algorithms.advance.requirements"></a></span><a class="link" href="algorithms.html#iterator.algorithms.advance.requirements">Requirements</a>
      </h5>
<p>
        <code class="computeroutput"><span class="identifier">Iterator</span></code> should model Incrementable
        Iterator.
      </p>
<h5>
<a name="iterator.algorithms.advance.h4"></a>
        <span class="phrase"><a name="iterator.algorithms.advance.preconditions"></a></span><a class="link" href="algorithms.html#iterator.algorithms.advance.preconditions">Preconditions</a>
      </h5>
<p>
        Let <code class="computeroutput"><span class="identifier">it</span></code><sub><code class="computeroutput"><span class="identifier">i</span></code></sub> be
        the iterator obtained by incrementing (or decrementing if <code class="computeroutput"><span class="identifier">n</span></code>
        is negative) <code class="computeroutput"><span class="identifier">it</span></code> by <code class="computeroutput"><span class="identifier">i</span></code>. All the iterators <code class="computeroutput"><span class="identifier">it</span></code><sub><code class="computeroutput"><span class="identifier">i</span></code></sub> for <code class="computeroutput"><span class="identifier">i</span></code>
        = 0, 1, 2, ..., <code class="computeroutput"><span class="special">|</span><span class="identifier">n</span><span class="special">|</span></code> should be valid.
      </p>
<p>
        If <code class="computeroutput"><span class="identifier">Iterator</span></code> does not model
        <a class="link" href="concepts/traversal.html#iterator.concepts.traversal.bidirectional" title="Bidirectional Traversal Concept">Bidirectional Traversal
        Iterator</a>, <code class="computeroutput"><span class="identifier">n</span></code> should
        be non-negative.
      </p>
<h5>
<a name="iterator.algorithms.advance.h5"></a>
        <span class="phrase"><a name="iterator.algorithms.advance.complexity"></a></span><a class="link" href="algorithms.html#iterator.algorithms.advance.complexity">Complexity</a>
      </h5>
<p>
        If <code class="computeroutput"><span class="identifier">Iterator</span></code> models <a class="link" href="concepts/traversal.html#iterator.concepts.traversal.random_access" title="Random Access Traversal Concept">Random Access Traversal
        Iterator</a>, it takes constant time; otherwise it takes linear time.
      </p>
<h5>
<a name="iterator.algorithms.advance.h6"></a>
        <span class="phrase"><a name="iterator.algorithms.advance.notes"></a></span><a class="link" href="algorithms.html#iterator.algorithms.advance.notes">Notes</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            This function is not a customization point and is protected against being
            found by argument-dependent lookup (ADL).
          </li>
<li class="listitem">
            This function is <code class="computeroutput"><span class="keyword">constexpr</span></code>
            only in C++14 or later.
          </li>
</ul></div>
<h5>
<a name="iterator.algorithms.advance.h7"></a>
        <span class="phrase"><a name="iterator.algorithms.advance.acknowledgements"></a></span><a class="link" href="algorithms.html#iterator.algorithms.advance.acknowledgements">Acknowledgements</a>
      </h5>
<p>
        Contributed by Michel Morin.
      </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2003, 2005 David Abrahams Jeremy Siek Thomas
      Witt<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at &lt;ulink url="http://www.boost.org/LICENSE_1_0.txt"&gt;
        http://www.boost.org/LICENSE_1_0.txt &lt;/ulink&gt;)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="utilities/traits.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="algorithms/distance.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
