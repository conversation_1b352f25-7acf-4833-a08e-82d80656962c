<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Transform Iterator</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Iterator">
<link rel="up" href="../specialized.html" title="Specialized Adaptors">
<link rel="prev" href="shared_container.html" title="Shared Container Iterator">
<link rel="next" href="zip.html" title="Zip Iterator">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="shared_container.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../specialized.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="zip.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="iterator.specialized.transform"></a><a class="link" href="transform.html" title="Transform Iterator">Transform Iterator</a>
</h3></div></div></div>
<p>
        The transform iterator adapts an iterator by modifying the <code class="computeroutput"><span class="keyword">operator</span><span class="special">*</span></code>
        to apply a function object to the result of dereferencing the iterator and
        returning the result.
      </p>
<h3>
<a name="iterator.specialized.transform.h0"></a>
        <span class="phrase"><a name="iterator.specialized.transform.example"></a></span><a class="link" href="transform.html#iterator.specialized.transform.example">Example</a>
      </h3>
<p>
        This is a simple example of using the transform_iterators class to generate
        iterators that multiply (or add to) the value returned by dereferencing the
        iterator. It would be cooler to use lambda library in this example.
      </p>
<pre class="programlisting"><span class="keyword">int</span> <span class="identifier">x</span><span class="special">[]</span> <span class="special">=</span> <span class="special">{</span> <span class="number">1</span><span class="special">,</span> <span class="number">2</span><span class="special">,</span> <span class="number">3</span><span class="special">,</span> <span class="number">4</span><span class="special">,</span> <span class="number">5</span><span class="special">,</span> <span class="number">6</span><span class="special">,</span> <span class="number">7</span><span class="special">,</span> <span class="number">8</span> <span class="special">};</span>
<span class="keyword">const</span> <span class="keyword">int</span> <span class="identifier">N</span> <span class="special">=</span> <span class="keyword">sizeof</span><span class="special">(</span><span class="identifier">x</span><span class="special">)/</span><span class="keyword">sizeof</span><span class="special">(</span><span class="keyword">int</span><span class="special">);</span>

<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">binder1st</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">multiplies</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">Function</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">transform_iterator</span><span class="special">&lt;</span><span class="identifier">Function</span><span class="special">,</span> <span class="keyword">int</span><span class="special">*&gt;</span> <span class="identifier">doubling_iterator</span><span class="special">;</span>

<span class="identifier">doubling_iterator</span> <span class="identifier">i</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">bind1st</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">multiplies</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(),</span> <span class="number">2</span><span class="special">)),</span>
  <span class="identifier">i_end</span><span class="special">(</span><span class="identifier">x</span> <span class="special">+</span> <span class="identifier">N</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">bind1st</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">multiplies</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(),</span> <span class="number">2</span><span class="special">));</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"multiplying the array by 2:"</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="keyword">while</span> <span class="special">(</span><span class="identifier">i</span> <span class="special">!=</span> <span class="identifier">i_end</span><span class="special">)</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="special">*</span><span class="identifier">i</span><span class="special">++</span> <span class="special">&lt;&lt;</span> <span class="string">" "</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"adding 4 to each element in the array:"</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">copy</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">make_transform_iterator</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">bind1st</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">plus</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(),</span> <span class="number">4</span><span class="special">)),</span>
  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">make_transform_iterator</span><span class="special">(</span><span class="identifier">x</span> <span class="special">+</span> <span class="identifier">N</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">bind1st</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">plus</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(),</span> <span class="number">4</span><span class="special">)),</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream_iterator</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span><span class="special">,</span> <span class="string">" "</span><span class="special">));</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
</pre>
<p>
        The output is:
      </p>
<pre class="programlisting">multiplying the array by 2:
2 4 6 8 10 12 14 16
adding 4 to each element in the array:
5 6 7 8 9 10 11 12
</pre>
<p>
        The source code for this example can be found <a href="../../../../example/transform_iterator_example.cpp" target="_top">here</a>.
      </p>
<h3>
<a name="iterator.specialized.transform.h1"></a>
        <span class="phrase"><a name="iterator.specialized.transform.reference"></a></span><a class="link" href="transform.html#iterator.specialized.transform.reference">Reference</a>
      </h3>
<h4>
<a name="iterator.specialized.transform.h2"></a>
        <span class="phrase"><a name="iterator.specialized.transform.synopsis"></a></span><a class="link" href="transform.html#iterator.specialized.transform.synopsis">Synopsis</a>
      </h4>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">UnaryFunction</span><span class="special">,</span>
          <span class="keyword">class</span> <span class="identifier">Iterator</span><span class="special">,</span>
          <span class="keyword">class</span> <span class="identifier">Reference</span> <span class="special">=</span> <span class="identifier">use_default</span><span class="special">,</span>
          <span class="keyword">class</span> <span class="identifier">Value</span> <span class="special">=</span> <span class="identifier">use_default</span><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">transform_iterator</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="keyword">typedef</span> <span class="comment">/* see below */</span> <span class="identifier">value_type</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="comment">/* see below */</span> <span class="identifier">reference</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="comment">/* see below */</span> <span class="identifier">pointer</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">difference_type</span> <span class="identifier">difference_type</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="comment">/* see below */</span> <span class="identifier">iterator_category</span><span class="special">;</span>

  <span class="identifier">transform_iterator</span><span class="special">();</span>
  <span class="identifier">transform_iterator</span><span class="special">(</span><span class="identifier">Iterator</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">UnaryFunction</span> <span class="identifier">f</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">F2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">I2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">R2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">V2</span><span class="special">&gt;</span>
  <span class="identifier">transform_iterator</span><span class="special">(</span>
        <span class="identifier">transform_iterator</span><span class="special">&lt;</span><span class="identifier">F2</span><span class="special">,</span> <span class="identifier">I2</span><span class="special">,</span> <span class="identifier">R2</span><span class="special">,</span> <span class="identifier">V2</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">t</span>
      <span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">enable_if_convertible</span><span class="special">&lt;</span><span class="identifier">I2</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">*</span> <span class="special">=</span> <span class="number">0</span>      <span class="comment">// exposition only</span>
      <span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">enable_if_convertible</span><span class="special">&lt;</span><span class="identifier">F2</span><span class="special">,</span> <span class="identifier">UnaryFunction</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">*</span> <span class="special">=</span> <span class="number">0</span> <span class="comment">// exposition only</span>
  <span class="special">);</span>
  <span class="identifier">UnaryFunction</span> <span class="identifier">functor</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">Iterator</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">base</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">reference</span> <span class="keyword">operator</span><span class="special">*()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">transform_iterator</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">++();</span>
  <span class="identifier">transform_iterator</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">--();</span>
<span class="keyword">private</span><span class="special">:</span>
  <span class="identifier">Iterator</span> <span class="identifier">m_iterator</span><span class="special">;</span> <span class="comment">// exposition only</span>
  <span class="identifier">UnaryFunction</span> <span class="identifier">m_f</span><span class="special">;</span>   <span class="comment">// exposition only</span>
<span class="special">};</span>
</pre>
<p>
        If <code class="computeroutput"><span class="identifier">Reference</span></code> is <code class="computeroutput"><span class="identifier">use_default</span></code> then the <code class="computeroutput"><span class="identifier">reference</span></code>
        member of <code class="computeroutput"><span class="identifier">transform_iterator</span></code>
        is<br> <code class="computeroutput"><span class="identifier">result_of</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">UnaryFunction</span><span class="special">(</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">reference</span><span class="special">)&gt;::</span><span class="identifier">type</span></code>.
        Otherwise, <code class="computeroutput"><span class="identifier">reference</span></code> is
        <code class="computeroutput"><span class="identifier">Reference</span></code>.
      </p>
<p>
        If <code class="computeroutput"><span class="identifier">Value</span></code> is <code class="computeroutput"><span class="identifier">use_default</span></code> then the <code class="computeroutput"><span class="identifier">value_type</span></code>
        member is <code class="computeroutput"><span class="identifier">remove_cv</span><span class="special">&lt;</span><span class="identifier">remove_reference</span><span class="special">&lt;</span><span class="identifier">reference</span><span class="special">&gt;</span>
        <span class="special">&gt;::</span><span class="identifier">type</span></code>.
        Otherwise, <code class="computeroutput"><span class="identifier">value_type</span></code> is
        <code class="computeroutput"><span class="identifier">Value</span></code>.
      </p>
<p>
        If <code class="computeroutput"><span class="identifier">Iterator</span></code> models Readable
        Lvalue Iterator and if <code class="computeroutput"><span class="identifier">Iterator</span></code>
        models Random Access Traversal Iterator, then <code class="computeroutput"><span class="identifier">iterator_category</span></code>
        is convertible to <code class="computeroutput"><span class="identifier">random_access_iterator_tag</span></code>.
        Otherwise, if <code class="computeroutput"><span class="identifier">Iterator</span></code> models
        Bidirectional Traversal Iterator, then <code class="computeroutput"><span class="identifier">iterator_category</span></code>
        is convertible to <code class="computeroutput"><span class="identifier">bidirectional_iterator_tag</span></code>.
        Otherwise <code class="computeroutput"><span class="identifier">iterator_category</span></code>
        is convertible to <code class="computeroutput"><span class="identifier">forward_iterator_tag</span></code>.
        If <code class="computeroutput"><span class="identifier">Iterator</span></code> does not model
        Readable Lvalue Iterator then <code class="computeroutput"><span class="identifier">iterator_category</span></code>
        is convertible to <code class="computeroutput"><span class="identifier">input_iterator_tag</span></code>.
      </p>
<h4>
<a name="iterator.specialized.transform.h3"></a>
        <span class="phrase"><a name="iterator.specialized.transform.requirements"></a></span><a class="link" href="transform.html#iterator.specialized.transform.requirements">Requirements</a>
      </h4>
<p>
        The type <code class="computeroutput"><span class="identifier">UnaryFunction</span></code> must
        be Assignable, Copy Constructible, and the expression <code class="computeroutput"><span class="identifier">f</span><span class="special">(*</span><span class="identifier">i</span><span class="special">)</span></code>
        must be valid where <code class="computeroutput"><span class="identifier">f</span></code> is
        a const object of type <code class="computeroutput"><span class="identifier">UnaryFunction</span></code>,
        <code class="computeroutput"><span class="identifier">i</span></code> is an object of type <code class="computeroutput"><span class="identifier">Iterator</span></code>, and where the type of <code class="computeroutput"><span class="identifier">f</span><span class="special">(*</span><span class="identifier">i</span><span class="special">)</span></code> must be <code class="computeroutput"><span class="identifier">result_of</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">UnaryFunction</span><span class="special">(</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">reference</span><span class="special">)&gt;::</span><span class="identifier">type</span></code>.
      </p>
<p>
        The argument <code class="computeroutput"><span class="identifier">Iterator</span></code> shall
        model Readable Iterator.
      </p>
<h4>
<a name="iterator.specialized.transform.h4"></a>
        <span class="phrase"><a name="iterator.specialized.transform.concepts"></a></span><a class="link" href="transform.html#iterator.specialized.transform.concepts">Concepts</a>
      </h4>
<p>
        The resulting <code class="computeroutput"><span class="identifier">transform_iterator</span></code>
        models the most refined of the following that is also modeled by <code class="computeroutput"><span class="identifier">Iterator</span></code>.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Writable Lvalue Iterator if <code class="computeroutput"><span class="identifier">transform_iterator</span><span class="special">::</span><span class="identifier">reference</span></code>
            is a non-const reference.
          </li>
<li class="listitem">
            Readable Lvalue Iterator if <code class="computeroutput"><span class="identifier">transform_iterator</span><span class="special">::</span><span class="identifier">reference</span></code>
            is a const reference.
          </li>
<li class="listitem">
            Readable Iterator otherwise.
          </li>
</ul></div>
<p>
        The <code class="computeroutput"><span class="identifier">transform_iterator</span></code> models
        the most refined standard traversal concept that is modeled by the <code class="computeroutput"><span class="identifier">Iterator</span></code> argument.
      </p>
<p>
        If <code class="computeroutput"><span class="identifier">transform_iterator</span></code> is
        a model of Readable Lvalue Iterator then it models the following original
        iterator concepts depending on what the <code class="computeroutput"><span class="identifier">Iterator</span></code>
        argument models.
      </p>
<div class="table">
<a name="iterator.specialized.transform.category"></a><p class="title"><b>Table 1.17. Category</b></p>
<div class="table-contents"><table class="table" summary="Category">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  If <code class="computeroutput"><span class="identifier">Iterator</span></code> models
                </p>
              </th>
<th>
                <p>
                  then <code class="computeroutput"><span class="identifier">transform_iterator</span></code>
                  models
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Single Pass Iterator
                </p>
              </td>
<td>
                <p>
                  Input Iterator
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Forward Traversal Iterator
                </p>
              </td>
<td>
                <p>
                  Forward Iterator
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bidirectional Traversal Iterator
                </p>
              </td>
<td>
                <p>
                  Bidirectional Iterator
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Random Access Traversal Iterator
                </p>
              </td>
<td>
                <p>
                  Random Access Iterator
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
        If <code class="computeroutput"><span class="identifier">transform_iterator</span></code> models
        Writable Lvalue Iterator then it is a mutable iterator (as defined in the
        old iterator requirements).
      </p>
<p>
        <code class="computeroutput"><span class="identifier">transform_iterator</span><span class="special">&lt;</span><span class="identifier">F1</span><span class="special">,</span> <span class="identifier">X</span><span class="special">,</span> <span class="identifier">R1</span><span class="special">,</span>
        <span class="identifier">V1</span><span class="special">&gt;</span></code>
        is interoperable with <code class="computeroutput"><span class="identifier">transform_iterator</span><span class="special">&lt;</span><span class="identifier">F2</span><span class="special">,</span> <span class="identifier">Y</span><span class="special">,</span>
        <span class="identifier">R2</span><span class="special">,</span> <span class="identifier">V2</span><span class="special">&gt;</span></code>
        if and only if <code class="computeroutput"><span class="identifier">X</span></code> is interoperable
        with <code class="computeroutput"><span class="identifier">Y</span></code>.
      </p>
<h4>
<a name="iterator.specialized.transform.h5"></a>
        <span class="phrase"><a name="iterator.specialized.transform.operations"></a></span><a class="link" href="transform.html#iterator.specialized.transform.operations">Operations</a>
      </h4>
<p>
        In addition to the operations required by the <a class="link" href="transform.html#iterator.specialized.transform.concepts">concepts</a>
        modeled by <code class="computeroutput"><span class="identifier">transform_iterator</span></code>,
        <code class="computeroutput"><span class="identifier">transform_iterator</span></code> provides
        the following operations:
      </p>
<pre class="programlisting"><span class="identifier">transform_iterator</span><span class="special">();</span>
</pre>
<p>
        <span class="bold"><strong>Returns: </strong></span> An instance of <code class="computeroutput"><span class="identifier">transform_iterator</span></code>
        with <code class="computeroutput"><span class="identifier">m_f</span></code> and <code class="computeroutput"><span class="identifier">m_iterator</span></code> default constructed.
      </p>
<pre class="programlisting"><span class="identifier">transform_iterator</span><span class="special">(</span><span class="identifier">Iterator</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">UnaryFunction</span> <span class="identifier">f</span><span class="special">);</span>
</pre>
<p>
        <span class="bold"><strong>Returns: </strong></span> An instance of <code class="computeroutput"><span class="identifier">transform_iterator</span></code>
        with <code class="computeroutput"><span class="identifier">m_f</span></code> initialized to
        <code class="computeroutput"><span class="identifier">f</span></code> and <code class="computeroutput"><span class="identifier">m_iterator</span></code>
        initialized to <code class="computeroutput"><span class="identifier">x</span></code>.
      </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">F2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">I2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">R2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">V2</span><span class="special">&gt;</span>
<span class="identifier">transform_iterator</span><span class="special">(</span>
      <span class="identifier">transform_iterator</span><span class="special">&lt;</span><span class="identifier">F2</span><span class="special">,</span> <span class="identifier">I2</span><span class="special">,</span> <span class="identifier">R2</span><span class="special">,</span> <span class="identifier">V2</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">t</span>
    <span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">enable_if_convertible</span><span class="special">&lt;</span><span class="identifier">I2</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">*</span> <span class="special">=</span> <span class="number">0</span>      <span class="comment">// exposition only</span>
    <span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">enable_if_convertible</span><span class="special">&lt;</span><span class="identifier">F2</span><span class="special">,</span> <span class="identifier">UnaryFunction</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">*</span> <span class="special">=</span> <span class="number">0</span> <span class="comment">// exposition only</span>
<span class="special">);</span>
</pre>
<p>
        <span class="bold"><strong>Returns: </strong></span> An instance of <code class="computeroutput"><span class="identifier">transform_iterator</span></code>
        with <code class="computeroutput"><span class="identifier">m_f</span></code> initialized to
        <code class="computeroutput"><span class="identifier">t</span><span class="special">.</span><span class="identifier">functor</span><span class="special">()</span></code>
        and <code class="computeroutput"><span class="identifier">m_iterator</span></code> initialized
        to <code class="computeroutput"><span class="identifier">t</span><span class="special">.</span><span class="identifier">base</span><span class="special">()</span></code>.<br>
        <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">OtherIterator</span></code>
        is implicitly convertible to <code class="computeroutput"><span class="identifier">Iterator</span></code>.
      </p>
<pre class="programlisting"><span class="identifier">UnaryFunction</span> <span class="identifier">functor</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<p>
        <span class="bold"><strong>Returns: </strong></span> <code class="computeroutput"><span class="identifier">m_f</span></code>
      </p>
<pre class="programlisting"><span class="identifier">Iterator</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">base</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<p>
        <span class="bold"><strong>Returns: </strong></span> <code class="computeroutput"><span class="identifier">m_iterator</span></code>
      </p>
<pre class="programlisting"><span class="identifier">reference</span> <span class="keyword">operator</span><span class="special">*()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<p>
        <span class="bold"><strong>Returns: </strong></span> <code class="computeroutput"><span class="identifier">m_f</span><span class="special">(*</span><span class="identifier">m_iterator</span><span class="special">)</span></code>
      </p>
<pre class="programlisting"><span class="identifier">transform_iterator</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">++();</span>
</pre>
<p>
        <span class="bold"><strong>Effects: </strong></span> <code class="computeroutput"><span class="special">++</span><span class="identifier">m_iterator</span></code><br> <span class="bold"><strong>Returns:
        </strong></span> <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
      </p>
<pre class="programlisting"><span class="identifier">transform_iterator</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">--();</span>
</pre>
<p>
        <span class="bold"><strong>Effects: </strong></span> <code class="computeroutput"><span class="special">--</span><span class="identifier">m_iterator</span></code><br> <span class="bold"><strong>Returns:
        </strong></span> <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
      </p>
</div>
<div class="copyright-footer">Copyright © 2003, 2005 David Abrahams Jeremy Siek Thomas
      Witt<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at &lt;ulink url="http://www.boost.org/LICENSE_1_0.txt"&gt;
        http://www.boost.org/LICENSE_1_0.txt &lt;/ulink&gt;)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="shared_container.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../specialized.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="zip.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
