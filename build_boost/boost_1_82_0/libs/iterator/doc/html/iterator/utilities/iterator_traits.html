<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Iterator Traits</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Iterator">
<link rel="up" href="../utilities.html" title="Utilities">
<link rel="prev" href="concept_checking.html" title="Concept Checking">
<link rel="next" href="traits.html" title="Type Traits">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="concept_checking.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../utilities.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="traits.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="iterator.utilities.iterator_traits"></a><a class="link" href="iterator_traits.html" title="Iterator Traits">Iterator Traits</a>
</h3></div></div></div>
<p>
        <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span></code> provides access to five
        associated types of any iterator: its <code class="computeroutput"><span class="identifier">value_type</span></code>,
        <code class="computeroutput"><span class="identifier">reference</span></code>, <code class="computeroutput"><span class="identifier">pointer</span></code>, <code class="computeroutput"><span class="identifier">iterator_category</span></code>,
        and <code class="computeroutput"><span class="identifier">difference_type</span></code>. Unfortunately,
        such a "multi-valued" traits template can be difficult to use in
        a metaprogramming context. <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">iterator</span><span class="special">/</span><span class="identifier">iterator_traits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
        provides access to these types using a standard metafunctions_.
      </p>
<h3>
<a name="iterator.utilities.iterator_traits.h0"></a>
        <span class="phrase"><a name="iterator.utilities.iterator_traits.synopsis"></a></span><a class="link" href="iterator_traits.html#iterator.utilities.iterator_traits.synopsis">Synopsis</a>
      </h3>
<p>
        Header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">iterator</span><span class="special">/</span><span class="identifier">iterator_traits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>:
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Iterator</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">iterator_value</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span>
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">value_type</span>
    <span class="identifier">type</span><span class="special">;</span>
<span class="special">};</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Iterator</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">iterator_reference</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span>
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">reference</span>
    <span class="identifier">type</span><span class="special">;</span>
<span class="special">};</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Iterator</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">iterator_pointer</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span>
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">pointer</span>
    <span class="identifier">type</span><span class="special">;</span>
<span class="special">};</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Iterator</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">iterator_difference</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span>
      <span class="identifier">detail</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">difference_type</span>
    <span class="identifier">type</span><span class="special">;</span>
<span class="special">};</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Iterator</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">iterator_category</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span>
      <span class="identifier">detail</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">Iterator</span><span class="special">&gt;::</span><span class="identifier">iterator_category</span>
    <span class="identifier">type</span><span class="special">;</span>
<span class="special">};</span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2003, 2005 David Abrahams Jeremy Siek Thomas
      Witt<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at &lt;ulink url="http://www.boost.org/LICENSE_1_0.txt"&gt;
        http://www.boost.org/LICENSE_1_0.txt &lt;/ulink&gt;)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="concept_checking.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../utilities.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="traits.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
