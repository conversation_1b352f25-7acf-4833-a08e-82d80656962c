[/ 
  Copyright 2006-2007 <PERSON>.
  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt).
]

[section:match_flag_type match_flag_type]

The type `match_flag_type` is an implementation specific bitmask type 
(see C++ std ********.2) that controls how a regular expression is 
matched against a character sequence.  The behavior of the format 
flags is described in more detail in the 
[link boost_regex.format format syntax guide].

   namespace boost{ namespace regex_constants{

   typedef implemenation-specific-bitmask-type match_flag_type;

   static const match_flag_type match_default = 0;
   static const match_flag_type match_not_bob;
   static const match_flag_type match_not_eob;
   static const match_flag_type match_not_bol;
   static const match_flag_type match_not_eol;
   static const match_flag_type match_not_bow;
   static const match_flag_type match_not_eow;
   static const match_flag_type match_any;
   static const match_flag_type match_not_null;
   static const match_flag_type match_continuous;
   static const match_flag_type match_partial;
   static const match_flag_type match_single_line;
   static const match_flag_type match_prev_avail;
   static const match_flag_type match_not_dot_newline;
   static const match_flag_type match_not_dot_null;
   static const match_flag_type match_posix;
   static const match_flag_type match_perl;
   static const match_flag_type match_nosubs;
   static const match_flag_type match_extra;

   static const match_flag_type format_default = 0;
   static const match_flag_type format_sed;
   static const match_flag_type format_perl;
   static const match_flag_type format_literal; 

   static const match_flag_type format_no_copy;
   static const match_flag_type format_first_only;
   static const match_flag_type format_all;

   } // namespace regex_constants
   } // namespace boost

[h4 Description]

The type `match_flag_type` is an implementation specific bitmask type 
(see C++ std ********.2). When matching a regular expression against a 
sequence of characters \[first, last) then setting its elements has the 
effects listed in the table below:

[table
[[Element][Effect if set]]
[[match_default][Specifies that matching of regular expressions proceeds without any modification of the normal rules used in ECMA-262, ECMAScript Language Specification, Chapter 15 part 10, RegExp (Regular Expression) Objects (FWD.1)]]
[[match_not_bob][Specifies that the expressions "\\A" and "\\`" should not match against the sub-sequence \[first,first).]]
[[match_not_eob][Specifies that the expressions "\\'", "\\z" and "\\Z" should not match against the sub-sequence \[last,last).]]
[[match_not_bol][Specifies that the expression "^" should not be matched against the sub-sequence \[first,first).]]
[[match_not_eol][Specifies that the expression "$" should not be matched against the sub-sequence \[last,last).]]
[[match_not_bow][Specifies that the expressions "\\<" and "\\b" should not be matched against the sub-sequence \[first,first).]]
[[match_not_eow][Specifies that the expressions "\\>" and "\\b" should not be matched against the sub-sequence \[last,last).]]
[[match_any][Specifies that if more than one match is possible then any match is an acceptable result: this will still find the leftmost match, but may not find the "best" match at that position.  Use this flag if you care about the speed of matching, but don't care what was matched (only whether there is one or not).]]
[[match_not_null][Specifies that the expression can not be matched against an empty sequence.]]
[[match_continuous][Specifies that the expression must match a sub-sequence that begins at first.]]
[[match_partial][Specifies that if no match can be found, then it is acceptable to return a match \[from, last) such that from!= last, if there could exist some longer sequence of characters \[from,to) of which \[from,last) is a prefix, and which would result in a full match.
This flag is used when matching incomplete or very long texts, see the partial matches documentation for more information.]]
[[match_extra][Instructs the matching engine to retain all available capture information; if a capturing group is repeated then information about every repeat is available via match_results::captures() or sub_match_captures().]]
[[match_single_line][Equivalent to the inverse of Perl's m/ modifier; prevents ^ from matching after an embedded newline character (so that it only matches at the start of the text being matched), and $ from matching before an embedded newline (so that it only matches at the end of the text being matched).]]
[[match_prev_avail][Specifies that --first is a valid iterator position, when this flag is set then the flags match_not_bol and match_not_bow are ignored by the regular expression algorithms (RE.7) and iterators (RE.8).]]
[[match_not_dot_newline][Specifies that the expression "." does not match a newline character.  This is the inverse of Perl's s/ modifier.]]
[[match_not_dot_null][Specifies that the expression "." does not match a character null '\\0'.]]
[[match_posix][Specifies that the expression should be matched according to the POSIX
   [link boost_regex.syntax.leftmost_longest_rule leftmost-longest rule], 
   regardless of what kind of expression was compiled.
   Be warned that these rules do not work well with many Perl-specific features
   such as non-greedy repeats.]]
[[match_perl][Specifies that the expression should be matched according to
   the [link boost_regex.syntax.perl_syntax.what_gets_matched Perl matching rules], 
   irrespective of what kind of expression was
   compiled.]]
[[match_nosubs][Makes the expression behave as if it had no marked
   subexpressions, no matter how many capturing groups are actually
   present.  The [match_results] class will only contain information
   about the overall match, and not any sub-expressions.]]

[[format_default][Specifies that when a regular expression match is to be 
      replaced by a new string, that the new string is constructed using the rules 
      used by the ECMAScript replace function in ECMA-262, ECMAScript Language 
      Specification, Chapter 15 part 5.4.11 String.prototype.replace. (FWD.1). 
      
      This is functionally identical to the [link boost_regex.format.perl_format Perl format string rules].
      
      In addition during search and replace operations then all non-overlapping 
      occurrences of the regular expression are located and replaced, and sections 
      of the input that did not match the expression, are copied unchanged to the 
      output string.]]
[[format_sed][Specifies that when a regular expression match is to be 
      replaced by a new string, that the new string is constructed using 
      the rules used by the Unix sed utility in IEEE Std 1003.1-2001, 
      Portable Operating SystemInterface (POSIX ), Shells and Utilities.      
      See also the [link boost_regex.format.sed_format Sed Format string reference].]]
[[format_perl][Specifies that when a regular expression match is to be replaced by 
      a new string, that the new string is constructed using 
      [link boost_regex.format.perl_format the same rules as Perl 5].]]
[[format_literal][Specifies that when a regular expression match is to be 
      replaced by a new string, that the new string is a literal copy of 
      the replacement text.]]
[[format_all][Specifies that all syntax extensions are enabled, including 
      conditional (?ddexpression1:expression2) replacements: see 
      the [link boost_regex.format.boost_format_syntax format string guide] for more details.]]
[[format_no_copy][When specified during a search and replace operation, then sections of the character container sequence being searched that do match the regular expression, are not copied to the output string.]]
[[format_first_only][When specified during a search and replace operation, then only the first occurrence of the regular expression is replaced.]]
]

[endsect]


