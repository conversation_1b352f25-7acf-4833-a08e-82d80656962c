<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>History</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Regex 7.0.1">
<link rel="up" href="../background.html" title="Background Information">
<link rel="prev" href="acknowledgements.html" title="Acknowledgements">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="acknowledgements.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../background.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_regex.background.history"></a><a class="link" href="history.html" title="History">History</a>
</h3></div></div></div>
<p>
        New issues should be submitted at <a href="https://github.com/boostorg/regex/issues" target="_top">https://github.com/boostorg/regex/issues</a>
      </p>
<p>
        Currently open issues can be viewed <a href="https://github.com/boostorg/regex/issues?q=is%3Aopen+is%3Aissue" target="_top">here</a>.
      </p>
<p>
        All issues including closed ones can be viewed <a href="https://github.com/boostorg/regex/issues?q=is%3Aissue+is%3Aclosed" target="_top">here</a>.
      </p>
<h5>
<a name="boost_regex.background.history.h0"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_7_0_1_boost_1_79_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_7_0_1_boost_1_79_0">Boost.Regex-7.0.1
        (boost-1.79.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Minor fix for setting building with -DBOOST_REGEX_MAX_CACHE_BLOCKS=0
            and <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">atomic</span><span class="special">&gt;</span></code> present.
          </li></ul></div>
<h5>
<a name="boost_regex.background.history.h1"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_7_0_0_boost_1_78_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_7_0_0_boost_1_78_0">Boost.Regex-7.0.0
        (Boost-1.78.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <span class="bold"><strong>Breaking Change:</strong></span> Change \B to be the
            opposite of \b as per Perl behaviour.
          </li>
<li class="listitem">
            Change w32_regex_traits.hpp so that windows.h is no longer included.
          </li>
<li class="listitem">
            Fxed fuzzing related issues <a href="https://github.com/boostorg/regex/issues/156" target="_top">#151</a>,
            <a href="https://github.com/boostorg/regex/issues/156" target="_top">#152</a>,
            <a href="https://github.com/boostorg/regex/issues/156" target="_top">#153</a>,
            <a href="https://github.com/boostorg/regex/issues/156" target="_top">#156</a>.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h2"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_6_0_0_boost_1_77_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_6_0_0_boost_1_77_0">Boost.Regex-6.0.0
        (Boost-1.77.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Big change to header only library.
          </li>
<li class="listitem">
            Deprecate C++03 support.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h3"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_5_1_4_boost_172_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_5_1_4_boost_172_0">Boost.Regex-5.1.4
        (Boost-172.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Minor build fixes, see <a href="https://github.com/boostorg/regex/issues/89" target="_top">#89</a>.
          </li></ul></div>
<h5>
<a name="boost_regex.background.history.h4"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_5_1_3_boost_1_64_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_5_1_3_boost_1_64_0">Boost.Regex-5.1.3
        (Boost-1.64.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Compiling with Oracle C++ toolset is no longer restricted to static linking.
          </li>
<li class="listitem">
            Big effort to de-fuzz the library using libFuzzer and fix identified
            issues, see: <a href="https://svn.boost.org/trac/boost/ticket/12818" target="_top">#12818</a>.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h5"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_5_1_2_boost_1_62_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_5_1_2_boost_1_62_0">Boost.Regex-5.1.2
        (Boost-1.62.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Fix buffer over-run error when parsing certain invalid regexes, see
            <a href="https://svn.boost.org/trac/boost/ticket/12222" target="_top">#12222</a>.
          </li>
<li class="listitem">
            Fix detection of ICU in library build, see <a href="https://svn.boost.org/trac/boost/ticket/12152" target="_top">#12152</a>.
          </li>
<li class="listitem">
            Fix bug in case sensitivity change, see <a href="https://svn.boost.org/trac/boost/ticket/11940" target="_top">#11940</a>.
          </li>
<li class="listitem">
            Allow types wider than int in <code class="computeroutput"><span class="special">\</span><span class="identifier">x</span><span class="special">{}</span></code>
            expressions (for char32_t etc), see <a href="https://svn.boost.org/trac/boost/ticket/11988" target="_top">#11988</a>.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h6"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_5_1_1_boost_1_61_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_5_1_1_boost_1_61_0">Boost.Regex-5.1.1
        (Boost-1.61.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Change to lockfree implementation of memory cache, see <a href="https://github.com/boostorg/regex/pull/23" target="_top">PR#23</a>.
          </li></ul></div>
<h5>
<a name="boost_regex.background.history.h7"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_5_1_0_boost_1_60_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_5_1_0_boost_1_60_0">Boost.Regex-5.1.0
        (Boost-1.60.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Add support for Perl's backtracking control verbs, see <a href="https://svn.boost.org/trac/boost/ticket/11205" target="_top">#11205</a>.
            Note however, that (*MARK) and operations on marks are not currently
            supported.
          </li>
<li class="listitem">
            Fix incorrect range end when matching [[:unicode:]], see <a href="https://svn.boost.org/trac/boost/ticket/11524" target="_top">#11524</a>.
          </li>
<li class="listitem">
            Change POSIX reg_comp API to not check potentially uninitialized memory,
            note that code which was previously free from memory leaks (but none
            the less buggy, as it didn't call reg_free) will now leak. See <a href="https://svn.boost.org/trac/boost/ticket/11472" target="_top">#11472</a>.
          </li>
<li class="listitem">
            Make sub_match a valid C++ range type, see <a href="https://svn.boost.org/trac/boost/ticket/11036" target="_top">#11036</a>.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h8"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_5_0_1_boost_1_58_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_5_0_1_boost_1_58_0">Boost.Regex-5.0.1
        (Boost-1.58.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Fixed some typos as in <a href="https://svn.boost.org/trac/boost/ticket/10682" target="_top">#10682</a>.
          </li>
<li class="listitem">
            Merged <a href="https://github.com/boostorg/regex/pull/6" target="_top">pull-request
            #6</a> for Coverity warnings.
          </li>
<li class="listitem">
            Merged <a href="https://github.com/boostorg/regex/pull/7" target="_top">pull-request
            #7</a> for Coverity warnings.
          </li>
<li class="listitem">
            Merged <a href="https://github.com/boostorg/regex/pull/8" target="_top">pull-request
            #8</a> for Coverity warnings.
          </li>
<li class="listitem">
            Merged <a href="https://github.com/boostorg/regex/pull/10" target="_top">pull-request
            #10</a> to enable more build variants when linking to ICU.
          </li>
<li class="listitem">
            Fix issue with ICU and partial matches, see <a href="https://svn.boost.org/trac/boost/ticket/10114" target="_top">#10114</a>.
          </li>
<li class="listitem">
            Removed delayload support for the ICU libraries: this doesn't work with
            the latest ICU releases (linker errors).
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h9"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_regex_5_0_0_boost_1_56_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_regex_5_0_0_boost_1_56_0">Boost.Regex-5.0.0
        (Boost-1.56.0)</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Moved to library-specific version number post the move to Git. And since
            we have one (minor) breaking change this gets bumped up from v4 to v5.
          </li>
<li class="listitem">
            <span class="bold"><strong>Breaking change:</strong></span> corrected behavior
            of <code class="computeroutput"><span class="identifier">basic_regex</span><span class="special">&lt;&gt;::</span><span class="identifier">mark_count</span><span class="special">()</span></code>
            to match existing documentation, <code class="computeroutput"><span class="identifier">basic_regex</span><span class="special">&lt;&gt;::</span><span class="identifier">subexpression</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span></code> changed to match, see <a href="https://svn.boost.org/trac/boost/ticket/9227" target="_top">#9227</a>
          </li>
<li class="listitem">
            Fixed issue <a href="https://svn.boost.org/trac/boost/ticket/8903" target="_top">#8903</a>.
          </li>
<li class="listitem">
            Fixed documentation typos from <a href="https://svn.boost.org/trac/boost/ticket/9283" target="_top">#9283</a>.
          </li>
<li class="listitem">
            Fixed bug in collation code that failed if the locale generated collation
            strings with embedded nul's, see <a href="https://svn.boost.org/trac/boost/ticket/9451" target="_top">#9451</a>.
          </li>
<li class="listitem">
            Apply patch for unusual thread usage (no statically initialized mutexes),
            see <a href="https://svn.boost.org/trac/boost/ticket/9461" target="_top">#9461</a>.
          </li>
<li class="listitem">
            Added better checks for invalid UTF-8 sequences, see <a href="https://svn.boost.org/trac/boost/ticket/9473" target="_top">#9473</a>.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h10"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_54"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_54">Boost-1.54</a>
      </h5>
<p>
        Fixed issue <a href="https://svn.boost.org/trac/boost/ticket/8569" target="_top">#8569</a>.
      </p>
<h5>
<a name="boost_regex.background.history.h11"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_53"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_53">Boost-1.53</a>
      </h5>
<p>
        Fixed Issues: <a href="https://svn.boost.org/trac/boost/ticket/7744" target="_top">#7744</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/7644" target="_top">#7644</a>.
      </p>
<h5>
<a name="boost_regex.background.history.h12"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_51"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_51">Boost-1.51</a>
      </h5>
<p>
        Fixed issues: <a href="https://svn.boost.org/trac/boost/ticket/589" target="_top">#589</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/7084" target="_top">#7084</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/7032" target="_top">#7032</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/6346" target="_top">#6346</a>.
      </p>
<h5>
<a name="boost_regex.background.history.h13"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_50"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_50">Boost-1.50</a>
      </h5>
<p>
        Fixed issue with <code class="computeroutput"><span class="special">(?!)</span></code> not being
        a valid expression, and updated docs on what constitutes a valid conditional
        expression.
      </p>
<h5>
<a name="boost_regex.background.history.h14"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_48"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_48">Boost-1.48</a>
      </h5>
<p>
        Fixed issues: <a href="https://svn.boost.org/trac/boost/ticket/688" target="_top">#698</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/5835" target="_top">#5835</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/5958" target="_top">#5958</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/5736" target="_top">#5736</a>.
      </p>
<h5>
<a name="boost_regex.background.history.h15"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_47"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_47">Boost
        1.47</a>
      </h5>
<p>
        Fixed issues: <a href="https://svn.boost.org/trac/boost/ticket/5223" target="_top">#5223</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/5353" target="_top">#5353</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/5363" target="_top">#5363</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/5462" target="_top">#5462</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/5472" target="_top">#5472</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/5504" target="_top">#5504</a>.
      </p>
<h5>
<a name="boost_regex.background.history.h16"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_44"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_44">Boost
        1.44</a>
      </h5>
<p>
        Fixed issues: <a href="https://svn.boost.org/trac/boost/ticket/4309" target="_top">#4309</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/4215" target="_top">#4215</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/4212" target="_top">#4212</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/4191" target="_top">#4191</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/4132" target="_top">#4132</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/4123" target="_top">#4123</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/4114" target="_top">#4114</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/4036" target="_top">#4036</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/4020" target="_top">#4020</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/3941" target="_top">#3941</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/3902" target="_top">#3902</a>,
        <a href="https://svn.boost.org/trac/boost/ticket/3890" target="_top">#3890</a>
      </p>
<h5>
<a name="boost_regex.background.history.h17"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_42"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_42">Boost
        1.42</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Added support for Functors rather than strings as format expressions.
          </li>
<li class="listitem">
            Improved error reporting when throwing exceptions to include better more
            relevant information.
          </li>
<li class="listitem">
            Improved performance and reduced stack usage of recursive expressions.
          </li>
<li class="listitem">
            Fixed tickets <a href="https://svn.boost.org/trac/boost/ticket/2802" target="_top">#2802</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/3425" target="_top">#3425</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/3507" target="_top">#3507</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/3546" target="_top">#3546</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/3631" target="_top">#3631</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/3632" target="_top">#3632</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/3715" target="_top">#3715</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/3718" target="_top">#3718</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/3763" target="_top">#3763</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/3764" target="_top">#3764</a>
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h18"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_40"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_40">Boost
        1.40</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Added support for many Perl 5.10 syntax elements including named sub-expressions,
            branch resets and recursive regular expressions.
          </li></ul></div>
<h5>
<a name="boost_regex.background.history.h19"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_38"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_38">Boost
        1.38</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <span class="bold"><strong>Breaking change</strong></span>: empty expressions,
            and empty alternatives are now allowed when using the Perl regular expression
            syntax. This change has been added for Perl compatibility, when the new
            <a class="link" href="../ref/syntax_option_type.html" title="syntax_option_type"><code class="computeroutput"><span class="identifier">syntax_option_type</span></code></a>
            <span class="emphasis"><em>no_empty_expressions</em></span> is set then the old behaviour
            is preserved and empty expressions are prohibited. This is issue <a href="https://svn.boost.org/trac/boost/ticket/1081" target="_top">#1081</a>.
          </li>
<li class="listitem">
            Added support for Perl style ${n} expressions in format strings (issue
            <a href="https://svn.boost.org/trac/boost/ticket/2556" target="_top">#2556</a>).
          </li>
<li class="listitem">
            Added support for accessing the location of sub-expressions within the
            regular expression string (issue <a href="https://svn.boost.org/trac/boost/ticket/2269" target="_top">#2269</a>).
          </li>
<li class="listitem">
            Fixed compiler compatibility issues <a href="https://svn.boost.org/trac/boost/ticket/2244" target="_top">#2244</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/2514" target="_top">#2514</a>,
            and <a href="https://svn.boost.org/trac/boost/ticket/2244" target="_top">#2458</a>.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h20"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_34"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_34">Boost
        1.34</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Fix for non-greedy repeats and partial matches not working correctly
            in some cases.
          </li>
<li class="listitem">
            Fix for non-greedy repeats on VC++ not working in some cases (bug report
            1515830).
          </li>
<li class="listitem">
            Changed match_results::position() to return a valid result when *this
            represents a partial match.
          </li>
<li class="listitem">
            Fixed the grep and egrep options so that the newline character gets treated
            the same as |.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h21"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_33_1"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_33_1">Boost
        1.33.1</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Fixed broken makefiles.
          </li>
<li class="listitem">
            Fixed configuration setup to allow building with VC7.1 - STLport-4.6.2
            when using /Zc:wchar_t.
          </li>
<li class="listitem">
            Moved declarations class-inline in static_mutex.hpp so that SGI Irix
            compiler can cope.
          </li>
<li class="listitem">
            Added needed standard library #includes to fileiter.hpp, regex_workaround.hpp
            and cpp_regex_traits.hpp.
          </li>
<li class="listitem">
            Fixed a bug where non-greedy repeats could in certain strange circumstances
            repeat more times than their maximum value.
          </li>
<li class="listitem">
            Fixed the value returned by basic_regex&lt;&gt;::empty() from a default
            constructed object.
          </li>
<li class="listitem">
            Changed the definition of regex_error to make it backwards compatible
            with Boost-1.32.0.
          </li>
<li class="listitem">
            Disabled external templates for Intel C++ 8.0 and earlier - otherwise
            unresolved references can occur.
          </li>
<li class="listitem">
            Rewritten extern template code for gcc so that only specific member functions
            are exported: otherwise strange unresolved references can occur when
            linking and mixing debug and non-debug code.
          </li>
<li class="listitem">
            Initialise all the data members of the unicode_iterators: this keeps
            gcc from issuing needless warnings.
          </li>
<li class="listitem">
            Ported the ICU integration code to VC6 and VC7.
          </li>
<li class="listitem">
            Ensured code is STLport debug mode clean.
          </li>
<li class="listitem">
            Fixed lookbehind assertions so that fixed length repeats are permitted,
            and so that regex iteration allows lookbehind to look back before the
            current search range (into the last match).
          </li>
<li class="listitem">
            Fixed strange bug with non-greedy repeats inside forward lookahead assertions.
          </li>
<li class="listitem">
            Enabled negated character classes inside character sets.
          </li>
<li class="listitem">
            Fixed regression so that [a-z-] is a valid expression again.
          </li>
<li class="listitem">
            Fixed bug that allowed some invalid expressions to be accepted.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h22"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_33_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_33_0">Boost
        1.33.0</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Completely rewritten expression parsing code, and traits class support;
            now conforms to the standardization proposal.
          </li>
<li class="listitem">
            Breaking Change: The syntax options that can be passed to basic_regex
            constructors have been rationalized. The default option (perl) now has
            a value of zero, and it is now clearly documented which options apply
            to which regular expression syntax styles (perl, POSIX-extended, POSIX-basic
            etc). Some of the more esoteric options have now been removed, so there
            is the possibility that existing code may fail to compile: however equivalent
            functionality should still be available.
          </li>
<li class="listitem">
            Breaking Change: POSIX-extended and POSIX-basic regular expressions now
            enforce the letter of the POSIX standard much more closely than before.
          </li>
<li class="listitem">
            Added support for (?imsx-imsx) constructs.
          </li>
<li class="listitem">
            Added support for lookbehind expressions (?&lt;=positive-lookbehind)
            and (?&lt;!negative-lookbehind).
          </li>
<li class="listitem">
            Added support for conditional expressions (?(assertion)true-expression|false-expression).
          </li>
<li class="listitem">
            Added MFC/ATL string wrappers.
          </li>
<li class="listitem">
            Added Unicode support; based on ICU.
          </li>
<li class="listitem">
            Changed newline support to recognise \f as a line separator (all character
            types), and \x85 as a line separator for wide characters / Unicode only.
          </li>
<li class="listitem">
            Added a new format flag format_literal that treats the replace string
            as a literal, rather than a Perl or Sed style format string.
          </li>
<li class="listitem">
            Errors are now reported by throwing exceptions of type regex_error. The
            types used previously - bad_expression and bad_pattern - are now just
            typedefs for regex_error. Type regex_error has a couple of new members:
            code() to report an error code rather than a string, and position() to
            report where in the expression the error occurred.
          </li>
</ul></div>
<h5>
<a name="boost_regex.background.history.h23"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_32_1"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_32_1">Boost
        1.32.1</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bug in partial matches of bounded repeats of '.'.
          </li></ul></div>
<h5>
<a name="boost_regex.background.history.h24"></a>
        <span class="phrase"><a name="boost_regex.background.history.boost_1_31_0"></a></span><a class="link" href="history.html#boost_regex.background.history.boost_1_31_0">Boost
        1.31.0</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Completely rewritten pattern matching code - it is now up to 10 times
            faster than before.
          </li>
<li class="listitem">
            Reorganized documentation.
          </li>
<li class="listitem">
            Deprecated all interfaces that are not part of the regular expression
            standardization proposal.
          </li>
<li class="listitem">
            Added regex_iterator and regex_token_iterator .
          </li>
<li class="listitem">
            Added support for Perl style independent sub-expressions.
          </li>
<li class="listitem">
            Added non-member operators to the sub_match class, so that you can compare
            sub_match's with strings, or add them to a string to produce a new string.
          </li>
<li class="listitem">
            Added experimental support for extended capture information.
          </li>
<li class="listitem">
            Changed the match flags so that they are a distinct type (not an integer),
            if you try to pass the match flags as an integer rather than match_flag_type
            to the regex algorithms then you will now get a compiler error.
          </li>
</ul></div>
</div>
<div class="copyright-footer">Copyright © 1998-2013 John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="acknowledgements.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../background.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
</body>
</html>
