<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>References and Further Information</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Regex 7.0.1">
<link rel="up" href="../background.html" title="Background Information">
<link rel="prev" href="examples.html" title="Test and Example Programs">
<link rel="next" href="faq.html" title="FAQ">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="examples.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../background.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="faq.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_regex.background.futher"></a><a class="link" href="futher.html" title="References and Further Information">References and Further
      Information</a>
</h3></div></div></div>
<p>
        The main Perl regular expression tutorial can be found <a href="http://perldoc.perl.org/perlretut.html" target="_top">here</a>,
        there's a much shorter summary of the main features <a href="https://www.cs.tut.fi/~jkorpela/perl/regexp.html" target="_top">here</a>.
      </p>
<p>
        The main book on regular expressions is <a href="http://www.oreilly.com/catalog/regex/" target="_top">Mastering
        Regular Expressions, published by O'Reilly</a>.
      </p>
<p>
        Boost.Regex forms the basis for the regular expression chapter of the <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2005/n1836.pdf" target="_top">Technical
        Report on C++ Library Extensions</a>.
      </p>
<p>
        The <a href="http://www.opengroup.org/onlinepubs/7908799/toc.htm" target="_top">Open
        Unix Specification</a> contains a wealth of useful material, including
        the POSIX regular expression syntax.
      </p>
<p>
        The <a href="http://www.cs.ucr.edu/~stelo/pattern.html" target="_top">Pattern Matching
        Pointers</a> site is a "must visit" resource for anyone interested
        in pattern matching.
      </p>
<p>
        <a href="http://glimpse.cs.arizona.edu/" target="_top">Glimpse and Agrep</a>, use
        a simplified regular expression syntax to achieve faster search times.
      </p>
<p>
        <a href="http://glimpse.cs.arizona.edu/udi.html" target="_top">Udi Manber</a> and
        <a href="http://www.dcc.uchile.cl/~rbaeza/" target="_top">Ricardo Baeza-Yates</a>
        both have a selection of useful pattern matching papers available from their
        respective web sites.
      </p>
</div>
<div class="copyright-footer">Copyright © 1998-2013 John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="examples.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../background.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="faq.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
