<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Performance</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Regex 7.0.1">
<link rel="up" href="../background.html" title="Background Information">
<link rel="prev" href="faq.html" title="FAQ">
<link rel="next" href="performance/section_id1378460593.html" title="Testing simple leftmost-longest matches (platform = linux, compiler = GNU C++ version 6.3.0)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="faq.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../background.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="performance/section_id1378460593.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_regex.background.performance"></a><a class="link" href="performance.html" title="Performance">Performance</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="performance/section_id1378460593.html">Testing
        simple leftmost-longest matches (platform = linux, compiler = GNU C++ version
        6.3.0)</a></span></dt>
<dt><span class="section"><a href="performance/section_id1675827111.html">Testing
        Perl searches (platform = linux, compiler = GNU C++ version 6.3.0)</a></span></dt>
<dt><span class="section"><a href="performance/section_id3141719723.html">Testing
        simple leftmost-longest matches (platform = Windows x64, compiler = Microsoft
        Visual C++ version 14.1)</a></span></dt>
<dt><span class="section"><a href="performance/section_id3258595385.html">Testing
        leftmost-longest searches (platform = Windows x64, compiler = Microsoft Visual
        C++ version 14.1)</a></span></dt>
<dt><span class="section"><a href="performance/section_id3261825021.html">Testing
        simple Perl matches (platform = linux, compiler = GNU C++ version 6.3.0)</a></span></dt>
<dt><span class="section"><a href="performance/section_id3752650613.html">Testing
        Perl searches (platform = Windows x64, compiler = Microsoft Visual C++ version
        14.1)</a></span></dt>
<dt><span class="section"><a href="performance/section_id4128344975.html">Testing
        simple Perl matches (platform = Windows x64, compiler = Microsoft Visual
        C++ version 14.1)</a></span></dt>
<dt><span class="section"><a href="performance/section_id4148872883.html">Testing
        leftmost-longest searches (platform = linux, compiler = GNU C++ version 6.3.0)</a></span></dt>
</dl></div>
<p>
        The performance of Boost.Regex in both recursive and non-recursive modes
        should be broadly comparable to other regular expression libraries: recursive
        mode is slightly faster (especially where memory allocation requires thread
        synchronisation), but not by much. The following pages compare Boost.Regex
        with various other regular expression libraries for the following compilers:
      </p>
</div>
<div class="copyright-footer">Copyright © 1998-2013 John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="faq.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../background.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="performance/section_id1378460593.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
