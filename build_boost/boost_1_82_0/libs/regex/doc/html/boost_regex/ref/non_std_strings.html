<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Interfacing With Non-Standard String Types</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Regex 7.0.1">
<link rel="up" href="../ref.html" title="Reference">
<link rel="prev" href="regex_traits.html" title="regex_traits">
<link rel="next" href="non_std_strings/icu.html" title="Working With Unicode and ICU String Types">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="regex_traits.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ref.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="non_std_strings/icu.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_regex.ref.non_std_strings"></a><a class="link" href="non_std_strings.html" title="Interfacing With Non-Standard String Types">Interfacing With Non-Standard
      String Types</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="non_std_strings/icu.html">Working With Unicode
        and ICU String Types</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="non_std_strings/icu/intro.html">Introduction
          to using Regex with ICU</a></span></dt>
<dt><span class="section"><a href="non_std_strings/icu/unicode_types.html">Unicode
          regular expression types</a></span></dt>
<dt><span class="section"><a href="non_std_strings/icu/unicode_algo.html">Unicode
          Regular Expression Algorithms</a></span></dt>
<dt><span class="section"><a href="non_std_strings/icu/unicode_iter.html">Unicode
          Aware Regex Iterators</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="non_std_strings/mfc_strings.html">Using
        Boost Regex With MFC Strings</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="non_std_strings/mfc_strings/mfc_intro.html">Introduction
          to Boost.Regex and MFC Strings</a></span></dt>
<dt><span class="section"><a href="non_std_strings/mfc_strings/mfc_regex_types.html">Regex
          Types Used With MFC Strings</a></span></dt>
<dt><span class="section"><a href="non_std_strings/mfc_strings/mfc_regex_create.html">Regular
          Expression Creation From an MFC String</a></span></dt>
<dt><span class="section"><a href="non_std_strings/mfc_strings/mfc_algo.html">Overloaded
          Algorithms For MFC String Types</a></span></dt>
<dt><span class="section"><a href="non_std_strings/mfc_strings/mfc_iter.html">Iterating
          Over the Matches Within An MFC String</a></span></dt>
</dl></dd>
</dl></div>
<p>
        The Boost.Regex algorithms and iterators are all iterator-based, with convenience
        overloads of the algorithms provided that convert standard library string
        types to iterator pairs internally. If you want to search a non-standard
        string type then the trick is to convert that string into an iterator pair:
        so far I haven't come across any string types that can't be handled this
        way, even if they're not officially iterator based. Certainly any string
        type that provides access to it's internal buffer, along with it's length,
        can be converted into a pair of pointers (which can be used as iterators).
      </p>
<p>
        Some non-standard string types are sufficiently common that wrappers have
        been provided for them already: currently this includes the ICU and MFC string
        class types.
      </p>
</div>
<div class="copyright-footer">Copyright © 1998-2013 John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="regex_traits.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ref.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="non_std_strings/icu.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
