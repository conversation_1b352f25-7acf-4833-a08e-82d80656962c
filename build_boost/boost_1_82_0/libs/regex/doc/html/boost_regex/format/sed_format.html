<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Sed Format String Syntax</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Regex 7.0.1">
<link rel="up" href="../format.html" title="Search and Replace Format String Syntax">
<link rel="prev" href="../format.html" title="Search and Replace Format String Syntax">
<link rel="next" href="perl_format.html" title="Perl Format String Syntax">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../format.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../format.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="perl_format.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_regex.format.sed_format"></a><a class="link" href="sed_format.html" title="Sed Format String Syntax">Sed Format String Syntax</a>
</h3></div></div></div>
<p>
        Sed-style format strings treat all characters as literals except:
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  character
                </p>
              </th>
<th>
                <p>
                  description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  &amp;
                </p>
              </td>
<td>
                <p>
                  The ampersand character is replaced in the output stream by the
                  whole of what matched the regular expression. Use \&amp; to output
                  a literal '&amp;' character.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \
                </p>
              </td>
<td>
                <p>
                  Specifies an escape sequence.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<p>
        An escape character followed by any character x, outputs that character unless
        x is one of the escape sequences shown below.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Escape
                </p>
              </th>
<th>
                <p>
                  Meaning
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  \a
                </p>
              </td>
<td>
                <p>
                  Outputs the bell character: '\a'.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \e
                </p>
              </td>
<td>
                <p>
                  Outputs the ANSI escape character (code point 27).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \f
                </p>
              </td>
<td>
                <p>
                  Outputs a form feed character: '\f'
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \n
                </p>
              </td>
<td>
                <p>
                  Outputs a newline character: '\n'.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \r
                </p>
              </td>
<td>
                <p>
                  Outputs a carriage return character: '\r'.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \t
                </p>
              </td>
<td>
                <p>
                  Outputs a tab character: '\t'.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \v
                </p>
              </td>
<td>
                <p>
                  Outputs a vertical tab character: '\v'.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \xDD
                </p>
              </td>
<td>
                <p>
                  Outputs the character whose hexadecimal code point is 0xDD
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \x{DDDD}
                </p>
              </td>
<td>
                <p>
                  Outputs the character whose hexadecimal code point is 0xDDDDD
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \cX
                </p>
              </td>
<td>
                <p>
                  Outputs the ANSI escape sequence "escape-X".
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  \D
                </p>
              </td>
<td>
                <p>
                  If D is a decimal digit in the range 1-9, then outputs the text
                  that matched sub-expression D.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 1998-2013 John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../format.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../format.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="perl_format.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
