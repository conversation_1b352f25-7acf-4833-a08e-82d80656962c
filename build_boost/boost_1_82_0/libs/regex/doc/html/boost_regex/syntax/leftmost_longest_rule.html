<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>The Leftmost Longest Rule</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Regex 7.0.1">
<link rel="up" href="../syntax.html" title="Regular Expression Syntax">
<link rel="prev" href="collating_names/named_unicode.html" title="Named Unicode Characters">
<link rel="next" href="../format.html" title="Search and Replace Format String Syntax">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="collating_names/named_unicode.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../syntax.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../format.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_regex.syntax.leftmost_longest_rule"></a><a class="link" href="leftmost_longest_rule.html" title="The Leftmost Longest Rule">The Leftmost
      Longest Rule</a>
</h3></div></div></div>
<p>
        Often there is more than one way of matching a regular expression at a particular
        location, for POSIX basic and extended regular expressions, the "best"
        match is determined as follows:
      </p>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
            Find the leftmost match, if there is only one match possible at this
            location then return it.
          </li>
<li class="listitem">
            Find the longest of the possible matches, along with any ties. If there
            is only one such possible match then return it.
          </li>
<li class="listitem">
            If there are no marked sub-expressions, then all the remaining alternatives
            are indistinguishable; return the first of these found.
          </li>
<li class="listitem">
            Find the match which has matched the first sub-expression in the leftmost
            position, along with any ties. If there is only on such match possible
            then return it.
          </li>
<li class="listitem">
            Find the match which has the longest match for the first sub-expression,
            along with any ties. If there is only one such match then return it.
          </li>
<li class="listitem">
            Repeat steps 4 and 5 for each additional marked sub-expression.
          </li>
<li class="listitem">
            If there is still more than one possible match remaining, then they are
            indistinguishable; return the first one found.
          </li>
</ol></div>
</div>
<div class="copyright-footer">Copyright © 1998-2013 John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="collating_names/named_unicode.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../syntax.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../format.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
