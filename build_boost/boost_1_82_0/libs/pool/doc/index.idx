# index.idx for Boost.Pool Quickbook docs auto-indexing for Boost.Pool
#   Copyright (c) 2011 <PERSON>
#
#   Use, modification and distribution is subject to the Boost Software
#   License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
#   http://www.boost.org/LICENSE_1_0.txt)

# Assume all header files are in boost/pool and sub-folders.
# Perhaps exclude sub-folder /details by setting to false?
!scan-path boost/pool .*\.hpp true

# Assume all example files are in /example (none in sub-folders).
!scan-path "libs/pool/example" ".*\.cpp"

align 
alignment 
alloc
allocation
automatic destruction 
block  \<block\w*\>
build
chunk   \<chunk\w*\>
concepts  \<concept\w*\>
conventions
deallocation
dynamic memory allocation
elements
fast pool allocation \<fast\w*\>
formatting conventions  \<convention\w*\>
guaranteeing alignment  \<guarantee\w*\>
include   \<include\w*\>
installation
headers  \<header\w*\>
interface \<interface\w*\>
jamfile  \<jamfile\.*\>
malloc
memory
memory block
naming
new
objects \<object\w*\>
object_pool
ordered  \<\w*(order|unorder)\w*\>
overview
padding \<pad\w*\>
portable \<portab\w*\>
segregated storage
Simple Segregated Storage
singleton  \<singleton\w*\>
singleton_pool
size   \<size\w*\>
template \<template\w*\>

#  \<\w*\>

!exclude junk 

!rewrite-name "(?i)(?:A|The)\s+(.*)" "\1"





