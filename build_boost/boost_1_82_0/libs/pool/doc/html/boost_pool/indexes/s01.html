<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function Index</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Pool">
<link rel="up" href="../indexes.html" title="Indexes">
<link rel="prev" href="../indexes.html" title="Indexes">
<link rel="next" href="s02.html" title="Class Index">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../indexes.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../indexes.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="s02.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="idm6279"></a>Function Index</h3></div></div></div>
<p><a class="link" href="s01.html#idx_id_0">A</a> <a class="link" href="s01.html#idx_id_1">B</a> <a class="link" href="s01.html#idx_id_2">C</a> <a class="link" href="s01.html#idx_id_3">D</a> <a class="link" href="s01.html#idx_id_5">F</a> <a class="link" href="s01.html#idx_id_6">G</a> <a class="link" href="s01.html#idx_id_8">I</a> <a class="link" href="s01.html#idx_id_10">L</a> <a class="link" href="s01.html#idx_id_11">M</a> <a class="link" href="s01.html#idx_id_12">N</a> <a class="link" href="s01.html#idx_id_13">O</a> <a class="link" href="s01.html#idx_id_14">P</a> <a class="link" href="s01.html#idx_id_15">R</a> <a class="link" href="s01.html#idx_id_16">S</a> <a class="link" href="s01.html#idx_id_17">T</a> <a class="link" href="s01.html#idx_id_20">Z</a></p>
<div class="variablelist"><dl class="variablelist">
<dt>
<a name="idx_id_0"></a><span class="term">A</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">address</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">add_block</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">add_ordered_block</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">allocate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_1"></a><span class="term">B</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">block</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li></ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_2"></a><span class="term">C</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">construct</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_3"></a><span class="term">D</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">deallocate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">destroy</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_5"></a><span class="term">F</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fast_pool_allocator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">find_prev</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">free</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">free_n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_6"></a><span class="term">G</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">get_pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li></ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_8"></a><span class="term">I</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">initialization</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">is_from</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_10"></a><span class="term">L</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lcm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">list</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_11"></a><span class="term">M</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">main</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">malloc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">malloc_n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">malloc_need_resize</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">max_size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_12"></a><span class="term">N</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nextof</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">next_size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">normally</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_13"></a><span class="term">O</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">O</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">object_pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ordered_free</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ordered_free_n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ordered_malloc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ordered_malloc_need_resize</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_14"></a><span class="term">P</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">p</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></strong></span></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pool_allocator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></strong></span></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ptr</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">purge_memory</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_15"></a><span class="term">R</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">release_memory</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_16"></a><span class="term">S</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">segregate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">set_max_size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">set_next_size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sizeof</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">store</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_17"></a><span class="term">T</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">try_malloc_n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li></ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_20"></a><span class="term">Z</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">zero</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li></ul></div>
</li></ul></div></dd>
</dl></div>
</div>
<div class="copyright-footer">Copyright © 2000-2006 Stephen Cleary<br>Copyright © 2011 Paul A. Bristow<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../indexes.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../indexes.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="s02.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
