<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Introduction</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Pool">
<link rel="up" href="../pool.html" title="Introduction and Overview">
<link rel="prev" href="conventions.html" title="Documentation Naming and Formatting Conventions">
<link rel="next" href="usage.html" title="How do I use Pool?">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="conventions.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../pool.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="usage.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_pool.pool.introduction"></a><a class="link" href="introduction.html" title="Introduction">Introduction</a>
</h3></div></div></div>
<h6>
<a name="boost_pool.pool.introduction.h0"></a>
        <span class="phrase"><a name="boost_pool.pool.introduction.what_is_pool_"></a></span><a class="link" href="introduction.html#boost_pool.pool.introduction.what_is_pool_">What
        is Pool?</a>
      </h6>
<p>
        Pool allocation is a memory allocation scheme that is very fast, but limited
        in its usage. For more information on pool allocation (also called <span class="emphasis"><em>simple
        segregated storage</em></span>, see <a class="link" href="pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling">concepts</a>
        concepts and <a class="link" href="pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage">Simple Segregated
        Storage</a>).
      </p>
<h6>
<a name="boost_pool.pool.introduction.h1"></a>
        <span class="phrase"><a name="boost_pool.pool.introduction.why_should_i_use_pool_"></a></span><a class="link" href="introduction.html#boost_pool.pool.introduction.why_should_i_use_pool_">Why
        should I use Pool?</a>
      </h6>
<p>
        Using Pools gives you more control over how memory is used in your program.
        For example, you could have a situation where you want to allocate a bunch
        of small objects at one point, and then reach a point in your program where
        none of them are needed any more. Using pool interfaces, you can choose to
        run their destructors or just drop them off into oblivion; the pool interface
        will guarantee that there are no system memory leaks.
      </p>
<h6>
<a name="boost_pool.pool.introduction.h2"></a>
        <span class="phrase"><a name="boost_pool.pool.introduction.when_should_i_use_pool_"></a></span><a class="link" href="introduction.html#boost_pool.pool.introduction.when_should_i_use_pool_">When should
        I use Pool?</a>
      </h6>
<p>
        Pools are generally used when there is a lot of allocation and deallocation
        of small objects. Another common usage is the situation above, where many
        objects may be dropped out of memory.
      </p>
<p>
        In general, use Pools when you need a more efficient way to do unusual memory
        control.
      </p>
<h6>
<a name="boost_pool.pool.introduction.h3"></a>
        <span class="phrase"><a name="boost_pool.pool.introduction.which_pool_allocator_should_i_use_"></a></span><a class="link" href="introduction.html#boost_pool.pool.introduction.which_pool_allocator_should_i_use_">Which
        pool allocator should I use?</a>
      </h6>
<p>
        <code class="computeroutput"><span class="identifier">pool_allocator</span></code> is a more
        general-purpose solution, geared towards efficiently servicing requests for
        any number of contiguous chunks.
      </p>
<p>
        <code class="computeroutput"><span class="identifier">fast_pool_allocator</span></code> is also
        a general-purpose solution but is geared towards efficiently servicing requests
        for one chunk at a time; it will work for contiguous chunks, but not as well
        as pool_allocator.
      </p>
<p>
        If you are seriously concerned about performance, use <code class="computeroutput"><span class="identifier">fast_pool_allocator</span></code>
        when dealing with containers such as <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">list</span></code>,
        and use <code class="computeroutput"><span class="identifier">pool_allocator</span></code> when
        dealing with containers such as <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span></code>.
      </p>
</div>
<div class="copyright-footer">Copyright © 2000-2006 Stephen Cleary<br>Copyright © 2011 Paul A. Bristow<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="conventions.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../pool.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="usage.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
