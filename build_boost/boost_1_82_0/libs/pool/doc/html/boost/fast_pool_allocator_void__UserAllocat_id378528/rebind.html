<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<title>Struct template rebind</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.76.1">
<link rel="home" href="../../index.html" title="Boost.Pool">
<link rel="up" href="../fast_pool_allocator_void__UserAllocat_id378528.html#id938511" title="Description">
<link rel="prev" href="../fast_pool_allocator_void__UserAllocat_id378528.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;">
<link rel="next" href="../../header/boost/pool/poolfwd_hpp.html" title="Header &lt;boost/pool/poolfwd.hpp&gt;">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../fast_pool_allocator_void__UserAllocat_id378528.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../fast_pool_allocator_void__UserAllocat_id378528.html#id938511"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../header/boost/pool/poolfwd_hpp.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.fast_pool_allocator_void,_UserAllocat_id378528.rebind"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct template rebind</span></h2>
<p>boost::fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;::rebind &#8212; Nested class rebind allows for transformation from fast_pool_allocator&lt;T&gt; to fast_pool_allocator&lt;U&gt;. </p>
</div>
<h2 xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" class="refsynopsisdiv-title">Synopsis</h2>
<div xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;">boost/pool/pool_alloc.hpp</a>&gt;

</span>

<span class="comment">// Nested class rebind allows for transformation from fast_pool_allocator&lt;T&gt;
// to fast_pool_allocator&lt;U&gt;.</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> U<span class="special">&gt;</span> 
<span class="keyword">struct</span> <a class="link" href="rebind.html" title="Struct template rebind">rebind</a> <span class="special">{</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <a class="link" href="../fast_pool_allocator.html" title="Class template fast_pool_allocator">fast_pool_allocator</a><span class="special">&lt;</span> <span class="identifier">U</span><span class="special">,</span> <span class="identifier">UserAllocator</span><span class="special">,</span> <span class="identifier">Mutex</span><span class="special">,</span> <span class="identifier">NextSize</span><span class="special">,</span> <span class="identifier">MaxSize</span> <span class="special">&gt;</span> <a name="boost.fast_pool_allocator_void,_UserAllocat_id378528.rebind.other"></a><span class="identifier">other</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id938665"></a><h2>Description</h2>
<p>Nested class rebind allows for transformation from fast_pool_allocator&lt;T&gt; to fast_pool_allocator&lt;U&gt; via the member typedef other. </p>
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright &#169; 2000-2006 Stephen Cleary<br>Copyright &#169; 2011 Paul A. Bristow<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../fast_pool_allocator_void__UserAllocat_id378528.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../fast_pool_allocator_void__UserAllocat_id378528.html#id938511"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../header/boost/pool/poolfwd_hpp.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
