# Generated by `boostdep --cmake metaparse`
# Copyright 2020 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.5...3.16)

project(boost_metaparse VERSION "${BOOST_SUPERPROJECT_VERSION}" LANGUAGES CXX)

add_library(boost_metaparse INTERFACE)
add_library(Boost::metaparse ALIAS boost_metaparse)

target_include_directories(boost_metaparse INTERFACE include)

target_link_libraries(boost_metaparse
  INTERFACE
    Boost::config
    Boost::mpl
    Boost::predef
    Boost::preprocessor
    Boost::static_assert
    Boost::type_traits
)

if(BUILD_TESTING AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")

  add_subdirectory(test)

endif()

