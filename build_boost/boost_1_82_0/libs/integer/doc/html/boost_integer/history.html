<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>History</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Boost.Integer">
<link rel="up" href="../index.html" title="Boost.Integer">
<link rel="prev" href="minmax.html" title="Compile time min/max calculation">
<link rel="next" href="cstdint.html" title="Removed from library: Standard Integer Types">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="minmax.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="cstdint.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_integer.history"></a><a class="link" href="history.html" title="History">History</a>
</h2></div></div></div>
<h5>
<a name="boost_integer.history.h0"></a>
      <span class="phrase"><a name="boost_integer.history.1_56_0"></a></span><a class="link" href="history.html#boost_integer.history.1_56_0">1.56.0</a>
    </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
          Moved <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">cstdint</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code> into <a href="../../../../../libs/config/index.html" target="_top">Boost.Config</a>.
        </li></ul></div>
<h5>
<a name="boost_integer.history.h1"></a>
      <span class="phrase"><a name="boost_integer.history.1_42_0"></a></span><a class="link" href="history.html#boost_integer.history.1_42_0">1.42.0</a>
    </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          Reverted Trunk to release branch state (i.e. a "known good state").
        </li>
<li class="listitem">
          Fixed issues: <a href="https://svn.boost.org/trac/boost/ticket/653" target="_top">653</a>,
          <a href="https://svn.boost.org/trac/boost/ticket/3084" target="_top">3084</a>,
          <a href="https://svn.boost.org/trac/boost/ticket/3177" target="_top">3177</a>,
          <a href="https://svn.boost.org/trac/boost/ticket/3180" target="_top">3180</a>,
          <a href="https://svn.boost.org/trac/boost/ticket/3548" target="_top">3568</a>,
          <a href="https://svn.boost.org/trac/boost/ticket/3657" target="_top">3657</a>,
          <a href="https://svn.boost.org/trac/boost/ticket/2134" target="_top">2134</a>.
        </li>
<li class="listitem">
          Added long long support to <code class="literal">boost::static_log2</code>, <code class="literal">boost::static_signed_min</code>,
          <code class="literal">boost::static_signed_max</code>, <code class="literal">boost::static_unsigned_min</code><code class="literal">boost::static_unsigned_max</code>,
          when available.
        </li>
<li class="listitem">
          The argument type and the result type of <code class="literal">boost::static_signed_min</code>
          etc are now typedef'd. Formerly, they were hardcoded as <code class="literal">unsigned
          long</code> and <code class="literal">int</code> respectively. Please, use the
          provided typedefs in new code (and update old code as soon as possible).
        </li>
</ul></div>
<h5>
<a name="boost_integer.history.h2"></a>
      <span class="phrase"><a name="boost_integer.history.1_32_0"></a></span><a class="link" href="history.html#boost_integer.history.1_32_0">1.32.0</a>
    </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
          The argument type and the result type of <code class="literal">boost::static_log2</code>
          are now typedef'd. Formerly, they were hardcoded as <code class="literal">unsigned long</code>
          and <code class="literal">int</code> respectively. Please, use the provided typedefs
          in new code (and update old code as soon as possible).
        </li></ul></div>
</div>
<div class="copyright-footer">Copyright © 2001-2009 Beman
      Dawes, Daryle Walker, Gennaro Prota, John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="https://www.boost.org/LICENSE_1_0.txt" target="_top">https://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="minmax.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="cstdint.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
