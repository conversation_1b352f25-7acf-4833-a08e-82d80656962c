<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Extended Euclidean Algorithm</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Boost.Integer">
<link rel="up" href="../index.html" title="Boost.Integer">
<link rel="prev" href="gcd_lcm.html" title="Greatest Common Divisor and Least Common Multiple">
<link rel="next" href="mod_inverse.html" title="Modular Multiplicative Inverse">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="gcd_lcm.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="mod_inverse.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_integer.extended_euclidean"></a><a class="link" href="extended_euclidean.html" title="Extended Euclidean Algorithm">Extended Euclidean Algorithm</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="extended_euclidean.html#boost_integer.extended_euclidean.introduction">Introduction</a></span></dt>
<dt><span class="section"><a href="extended_euclidean.html#boost_integer.extended_euclidean.synopsis">Synopsis</a></span></dt>
<dt><span class="section"><a href="extended_euclidean.html#boost_integer.extended_euclidean.usage">Usage</a></span></dt>
<dt><span class="section"><a href="extended_euclidean.html#boost_integer.extended_euclidean.references">References</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.extended_euclidean.introduction"></a><a class="link" href="extended_euclidean.html#boost_integer.extended_euclidean.introduction" title="Introduction">Introduction</a>
</h3></div></div></div>
<p>
        The extended Euclidean algorithm solves the integer relation <span class="emphasis"><em>mx
        + ny</em></span> = gcd(<span class="emphasis"><em>m</em></span>, <span class="emphasis"><em>n</em></span>) for
        <span class="emphasis"><em>x</em></span> and <span class="emphasis"><em>y</em></span>.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.extended_euclidean.synopsis"></a><a class="link" href="extended_euclidean.html#boost_integer.extended_euclidean.synopsis" title="Synopsis">Synopsis</a>
</h3></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">integer</span><span class="special">/</span><span class="identifier">extended_euclidean</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">integer</span> <span class="special">{</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Z</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">euclidean_result_t</span> <span class="special">{</span>
  <span class="identifier">Z</span> <span class="identifier">gcd</span><span class="special">;</span>
  <span class="identifier">Z</span> <span class="identifier">x</span><span class="special">;</span>
  <span class="identifier">Z</span> <span class="identifier">y</span><span class="special">;</span>
<span class="special">};</span>


<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Z</span><span class="special">&gt;</span>
<span class="identifier">euclidean_result_t</span><span class="special">&lt;</span><span class="identifier">Z</span><span class="special">&gt;</span> <span class="identifier">extended_euclidean</span><span class="special">(</span><span class="identifier">Z</span> <span class="identifier">m</span><span class="special">,</span> <span class="identifier">Z</span> <span class="identifier">n</span><span class="special">);</span>

<span class="special">}}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.extended_euclidean.usage"></a><a class="link" href="extended_euclidean.html#boost_integer.extended_euclidean.usage" title="Usage">Usage</a>
</h3></div></div></div>
<pre class="programlisting"><span class="keyword">int</span> <span class="identifier">m</span> <span class="special">=</span> <span class="number">12</span><span class="special">;</span>
<span class="keyword">int</span> <span class="identifier">n</span> <span class="special">=</span> <span class="number">15</span><span class="special">;</span>
<span class="keyword">auto</span> <span class="identifier">res</span> <span class="special">=</span> <span class="identifier">extended_euclidean</span><span class="special">(</span><span class="identifier">m</span><span class="special">,</span> <span class="identifier">n</span><span class="special">);</span>

<span class="keyword">int</span> <span class="identifier">gcd</span> <span class="special">=</span> <span class="identifier">res</span><span class="special">.</span><span class="identifier">gcd</span><span class="special">;</span>
<span class="keyword">int</span> <span class="identifier">x</span> <span class="special">=</span> <span class="identifier">res</span><span class="special">.</span><span class="identifier">x</span><span class="special">;</span>
<span class="keyword">int</span> <span class="identifier">y</span> <span class="special">=</span> <span class="identifier">res</span><span class="special">.</span><span class="identifier">y</span><span class="special">;</span>
<span class="comment">// mx + ny = gcd(m,n) should now hold</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.extended_euclidean.references"></a><a class="link" href="extended_euclidean.html#boost_integer.extended_euclidean.references" title="References">References</a>
</h3></div></div></div>
<p>
        Wagstaff, Samuel S., <span class="emphasis"><em>The Joy of Factoring</em></span>, Vol. 68.
        American Mathematical Soc., 2013.
      </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2001-2009 Beman
      Dawes, Daryle Walker, Gennaro Prota, John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="https://www.boost.org/LICENSE_1_0.txt" target="_top">https://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="gcd_lcm.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="mod_inverse.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
