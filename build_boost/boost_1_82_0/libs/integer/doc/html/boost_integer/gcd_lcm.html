<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Greatest Common Divisor and Least Common Multiple</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Boost.Integer">
<link rel="up" href="../index.html" title="Boost.Integer">
<link rel="prev" href="integer.html" title="Integer Type Selection">
<link rel="next" href="extended_euclidean.html" title="Extended Euclidean Algorithm">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="integer.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="extended_euclidean.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_integer.gcd_lcm"></a><a class="link" href="gcd_lcm.html" title="Greatest Common Divisor and Least Common Multiple">Greatest Common Divisor and Least
    Common Multiple</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.introduction">Introduction</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.synopsis">Synopsis</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.gcd_function_object">GCD Function
      Object</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.lcm_function_object">LCM Function
      Object</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.run_time">Run-time GCD &amp; LCM
      Determination</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.compile_time">Compile time GCD
      and LCM determination</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.gcd_header">Header &lt;boost/integer/common_factor.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.demo">Demonstration Program</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.rationale">Rationale</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.gcd_history">History</a></span></dt>
<dt><span class="section"><a href="gcd_lcm.html#boost_integer.gcd_lcm.gcd_credits">Credits</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.introduction"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.introduction" title="Introduction">Introduction</a>
</h3></div></div></div>
<p>
        The class and function templates in &lt;boost/math/common_factor.hpp&gt;
        provide run-time and compile-time evaluation of the greatest common divisor
        (GCD) or least common multiple (LCM) of two integers. These facilities are
        useful for many numeric-oriented generic programming problems.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.synopsis"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.synopsis" title="Synopsis">Synopsis</a>
</h3></div></div></div>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">integer</span>
<span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span> <span class="special">&gt;</span>
   <span class="keyword">class</span> <span class="identifier">gcd_evaluator</span><span class="special">;</span>
<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span> <span class="special">&gt;</span>
   <span class="keyword">class</span> <span class="identifier">lcm_evaluator</span><span class="special">;</span>

<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span> <span class="special">&gt;</span>
   <span class="keyword">constexpr</span> <span class="identifier">IntegerType</span>  <span class="identifier">gcd</span><span class="special">(</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span> <span class="special">);</span>
<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span> <span class="special">&gt;</span>
   <span class="keyword">constexpr</span> <span class="identifier">IntegerType</span>  <span class="identifier">lcm</span><span class="special">(</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span> <span class="special">);</span>
<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span><span class="special">,</span> <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span> <span class="special">&gt;</span>
   <span class="keyword">constexpr</span> <span class="identifier">IntegerType</span>  <span class="identifier">gcd</span><span class="special">(</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">Args</span> <span class="keyword">const</span><span class="special">&amp;...</span> <span class="special">);</span>
<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span><span class="special">,</span> <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span> <span class="special">&gt;</span>
   <span class="keyword">constexpr</span> <span class="identifier">IntegerType</span>  <span class="identifier">lcm</span><span class="special">(</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">Args</span> <span class="keyword">const</span><span class="special">&amp;...</span> <span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">I</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">I</span><span class="special">&gt;::</span><span class="identifier">value_type</span><span class="special">,</span> <span class="identifier">I</span><span class="special">&gt;</span>
   <span class="identifier">gcd_range</span><span class="special">(</span><span class="identifier">I</span> <span class="identifier">first</span><span class="special">,</span> <span class="identifier">I</span> <span class="identifier">last</span><span class="special">);</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">I</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">I</span><span class="special">&gt;::</span><span class="identifier">value_type</span><span class="special">,</span> <span class="identifier">I</span><span class="special">&gt;</span>
   <span class="identifier">lcm_range</span><span class="special">(</span><span class="identifier">I</span> <span class="identifier">first</span><span class="special">,</span> <span class="identifier">I</span> <span class="identifier">last</span><span class="special">);</span>

<span class="keyword">typedef</span> <span class="emphasis"><em>see-below</em></span> <span class="identifier">static_gcd_type</span><span class="special">;</span>

<span class="keyword">template</span> <span class="special">&lt;</span> <span class="identifier">static_gcd_type</span> <span class="identifier">Value1</span><span class="special">,</span> <span class="identifier">static_gcd_type</span> <span class="identifier">Value2</span> <span class="special">&gt;</span>
   <span class="keyword">struct</span> <span class="identifier">static_gcd</span><span class="special">;</span>
<span class="keyword">template</span> <span class="special">&lt;</span> <span class="identifier">static_gcd_type</span> <span class="identifier">Value1</span><span class="special">,</span> <span class="identifier">static_gcd_type</span> <span class="identifier">Value2</span> <span class="special">&gt;</span>
   <span class="keyword">struct</span> <span class="identifier">static_lcm</span><span class="special">;</span>

<span class="special">}</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.gcd_function_object"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.gcd_function_object" title="GCD Function Object">GCD Function
      Object</a>
</h3></div></div></div>
<p>
        <span class="bold"><strong>Header: </strong></span> <a href="../../../../../boost/integer/common_factor_rt.hpp" target="_top">&lt;boost/integer/common_factor_rt.hpp&gt;</a>
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span> <span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">gcd_evaluator</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
   <span class="comment">// Types</span>
   <span class="keyword">typedef</span> <span class="identifier">IntegerType</span>  <span class="identifier">result_type</span><span class="special">;</span>
   <span class="keyword">typedef</span> <span class="identifier">IntegerType</span>  <span class="identifier">first_argument_type</span><span class="special">;</span>
   <span class="keyword">typedef</span> <span class="identifier">IntegerType</span>  <span class="identifier">second_argument_type</span><span class="special">;</span>

   <span class="comment">// Function object interface</span>
   <span class="keyword">constexpr</span> <span class="identifier">result_type</span>  <span class="keyword">operator</span> <span class="special">()(</span>
      <span class="identifier">first_argument_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span>
      <span class="identifier">second_argument_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span> <span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
        The boost::integer::gcd_evaluator class template defines a function object
        class to return the greatest common divisor of two integers. The template
        is parameterized by a single type, called IntegerType here. This type should
        be a numeric type that represents integers. The result of the function object
        is always nonnegative, even if either of the operator arguments is negative.
      </p>
<p>
        This function object class template is used in the corresponding version
        of the GCD function template. If a numeric type wants to customize evaluations
        of its greatest common divisors, then the type should specialize on the gcd_evaluator
        class template.
      </p>
<p>
        Note that these function objects are <code class="computeroutput"><span class="keyword">constexpr</span></code>
        in C++14 and later only. They are also declared <code class="computeroutput"><span class="keyword">noexcept</span></code>
        when appropriate.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.lcm_function_object"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.lcm_function_object" title="LCM Function Object">LCM Function
      Object</a>
</h3></div></div></div>
<p>
        <span class="bold"><strong>Header: </strong></span> <a href="../../../../../boost/integer/common_factor_rt.hpp" target="_top">&lt;boost/integer/common_factor_rt.hpp&gt;</a>
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span> <span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">lcm_evaluator</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
   <span class="comment">// Types</span>
   <span class="keyword">typedef</span> <span class="identifier">IntegerType</span>  <span class="identifier">result_type</span><span class="special">;</span>
   <span class="keyword">typedef</span> <span class="identifier">IntegerType</span>  <span class="identifier">first_argument_type</span><span class="special">;</span>
   <span class="keyword">typedef</span> <span class="identifier">IntegerType</span>  <span class="identifier">second_argument_type</span><span class="special">;</span>

   <span class="comment">// Function object interface</span>
   <span class="keyword">constexpr</span> <span class="identifier">result_type</span>  <span class="keyword">operator</span> <span class="special">()(</span>
     <span class="identifier">first_argument_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span>
     <span class="identifier">second_argument_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span> <span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
        The boost::integer::lcm_evaluator class template defines a function object
        class to return the least common multiple of two integers. The template is
        parameterized by a single type, called IntegerType here. This type should
        be a numeric type that represents integers. The result of the function object
        is always nonnegative, even if either of the operator arguments is negative.
        If the least common multiple is beyond the range of the integer type, the
        results are undefined.
      </p>
<p>
        This function object class template is used in the corresponding version
        of the LCM function template. If a numeric type wants to customize evaluations
        of its least common multiples, then the type should specialize on the lcm_evaluator
        class template.
      </p>
<p>
        Note that these function objects are constexpr in C++14 and later only. They
        are also declared <code class="computeroutput"><span class="keyword">noexcept</span></code> when
        appropriate.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.run_time"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.run_time" title="Run-time GCD &amp; LCM Determination">Run-time GCD &amp; LCM
      Determination</a>
</h3></div></div></div>
<p>
        <span class="bold"><strong>Header: </strong></span> <a href="../../../../../boost/integer/common_factor_rt.hpp" target="_top">&lt;boost/integer/common_factor_rt.hpp&gt;</a>
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span> <span class="special">&gt;</span>
<span class="keyword">constexpr</span> <span class="identifier">IntegerType</span>  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">gcd</span><span class="special">(</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span> <span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span> <span class="special">&gt;</span>
<span class="keyword">constexpr</span> <span class="identifier">IntegerType</span>  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">lcm</span><span class="special">(</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span> <span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span><span class="special">,</span> <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span> <span class="special">&gt;</span>
   <span class="keyword">constexpr</span> <span class="identifier">IntegerType</span>  <span class="identifier">gcd</span><span class="special">(</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">Args</span> <span class="keyword">const</span><span class="special">&amp;...</span> <span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">IntegerType</span><span class="special">,</span> <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span> <span class="special">&gt;</span>
   <span class="keyword">constexpr</span> <span class="identifier">IntegerType</span>  <span class="identifier">lcm</span><span class="special">(</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a</span><span class="special">,</span> <span class="identifier">IntegerType</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">Args</span> <span class="keyword">const</span><span class="special">&amp;...</span> <span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">I</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">I</span><span class="special">&gt;::</span><span class="identifier">value_type</span><span class="special">,</span> <span class="identifier">I</span><span class="special">&gt;</span>
   <span class="identifier">gcd_range</span><span class="special">(</span><span class="identifier">I</span> <span class="identifier">first</span><span class="special">,</span> <span class="identifier">I</span> <span class="identifier">last</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">I</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span><span class="identifier">I</span><span class="special">&gt;::</span><span class="identifier">value_type</span><span class="special">,</span> <span class="identifier">I</span><span class="special">&gt;</span>
   <span class="identifier">lcm_range</span><span class="special">(</span><span class="identifier">I</span> <span class="identifier">first</span><span class="special">,</span> <span class="identifier">I</span> <span class="identifier">last</span><span class="special">);</span>
</pre>
<p>
        The boost::integer::gcd function template returns the greatest common (nonnegative)
        divisor of the two integers passed to it. <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">gcd_range</span></code>
        is the iteration of the above gcd algorithm over a range, returning the greatest
        common divisor of all the elements. The algorithm terminates when the gcd
        reaches unity or the end of the range. Thus it also returns the iterator
        after the last element inspected because this may not be equal to the end
        of the range. The variadic version of <code class="computeroutput"><span class="identifier">gcd</span></code>
        behaves similarly but does not indicate which input value caused the gcd
        to reach unity.
      </p>
<p>
        The boost::integer::lcm function template returns the least common (nonnegative)
        multiple of the two integers passed to it. As with gcd, there are range and
        variadic versions of the function for more than 2 arguments.
      </p>
<p>
        Note that these functions are constexpr in C++14 and later only. They are
        also declared <code class="computeroutput"><span class="keyword">noexcept</span></code> when
        appropriate.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.compile_time"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.compile_time" title="Compile time GCD and LCM determination">Compile time GCD
      and LCM determination</a>
</h3></div></div></div>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
          These functions are deprecated in favor of constexpr <code class="computeroutput"><span class="identifier">gcd</span></code>
          and <code class="computeroutput"><span class="identifier">lcm</span></code> on C++14 capable
          compilers.
        </p></td></tr>
</table></div>
<p>
        <span class="bold"><strong>Header: </strong></span> <a href="../../../../../boost/integer/common_factor_ct.hpp" target="_top">&lt;boost/integer/common_factor_ct.hpp&gt;</a>
      </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="emphasis"><em>unspecified</em></span> <span class="identifier">static_gcd_type</span><span class="special">;</span>

<span class="keyword">template</span> <span class="special">&lt;</span> <span class="identifier">static_gcd_type</span> <span class="identifier">Value1</span><span class="special">,</span> <span class="identifier">static_gcd_type</span> <span class="identifier">Value2</span> <span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">static_gcd</span> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">integral_c</span><span class="special">&lt;</span><span class="identifier">static_gcd_type</span><span class="special">,</span> <span class="identifier">implementation_defined</span><span class="special">&gt;</span>
<span class="special">{</span>
<span class="special">};</span>

<span class="keyword">template</span> <span class="special">&lt;</span> <span class="identifier">static_gcd_type</span> <span class="identifier">Value1</span><span class="special">,</span> <span class="identifier">static_gcd_type</span> <span class="identifier">Value2</span> <span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">static_lcm</span> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">integral_c</span><span class="special">&lt;</span><span class="identifier">static_gcd_type</span><span class="special">,</span> <span class="identifier">implementation_defined</span><span class="special">&gt;</span>
<span class="special">{</span>
<span class="special">};</span>
</pre>
<p>
        The type <code class="computeroutput"><span class="identifier">static_gcd_type</span></code>
        is the widest unsigned-integer-type that is supported for use in integral-constant-expressions
        by the compiler. Usually this the same type as <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">uintmax_t</span></code>,
        but may fall back to being <code class="computeroutput"><span class="keyword">unsigned</span>
        <span class="keyword">long</span></code> for some older compilers.
      </p>
<p>
        The boost::integer::static_gcd and boost::integer::static_lcm class templates
        take two value-based template parameters of the <span class="emphasis"><em>static_gcd_type</em></span>
        type and inherit from the type <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">mpl</span><span class="special">::</span><span class="identifier">integral_c</span></code>. Inherited from the base class,
        they have a member <span class="emphasis"><em>value</em></span> that is the greatest common
        factor or least common multiple, respectively, of the template arguments.
        A compile-time error will occur if the least common multiple is beyond the
        range of <code class="computeroutput"><span class="identifier">static_gcd_type</span></code>.
      </p>
<h4>
<a name="boost_integer.gcd_lcm.compile_time.h0"></a>
        <span class="phrase"><a name="boost_integer.gcd_lcm.compile_time.example"></a></span><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.compile_time.example">Example</a>
      </h4>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">integer</span><span class="special">/</span><span class="identifier">common_factor</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">algorithm</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iterator</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
   <span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span><span class="special">;</span>
   <span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"The GCD and LCM of 6 and 15 are "</span>
   <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">gcd</span><span class="special">(</span><span class="number">6</span><span class="special">,</span> <span class="number">15</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="string">" and "</span>
   <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">lcm</span><span class="special">(</span><span class="number">6</span><span class="special">,</span> <span class="number">15</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="string">", respectively."</span>
   <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"The GCD and LCM of 8 and 9 are "</span>
   <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">static_gcd</span><span class="special">&lt;</span><span class="number">8</span><span class="special">,</span> <span class="number">9</span><span class="special">&gt;::</span><span class="identifier">value</span>
   <span class="special">&lt;&lt;</span> <span class="string">" and "</span>
   <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">static_lcm</span><span class="special">&lt;</span><span class="number">8</span><span class="special">,</span> <span class="number">9</span><span class="special">&gt;::</span><span class="identifier">value</span>
   <span class="special">&lt;&lt;</span> <span class="string">", respectively."</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

   <span class="keyword">int</span>  <span class="identifier">a</span><span class="special">[]</span> <span class="special">=</span> <span class="special">{</span> <span class="number">4</span><span class="special">,</span> <span class="number">5</span><span class="special">,</span> <span class="number">6</span> <span class="special">},</span> <span class="identifier">b</span><span class="special">[]</span> <span class="special">=</span> <span class="special">{</span> <span class="number">7</span><span class="special">,</span> <span class="number">8</span><span class="special">,</span> <span class="number">9</span> <span class="special">},</span> <span class="identifier">c</span><span class="special">[</span><span class="number">3</span><span class="special">];</span>
   <span class="identifier">std</span><span class="special">::</span><span class="identifier">transform</span><span class="special">(</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">a</span> <span class="special">+</span> <span class="number">3</span><span class="special">,</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">c</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">integer</span><span class="special">::</span><span class="identifier">gcd_evaluator</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;()</span> <span class="special">);</span>
   <span class="identifier">std</span><span class="special">::</span><span class="identifier">copy</span><span class="special">(</span> <span class="identifier">c</span><span class="special">,</span> <span class="identifier">c</span> <span class="special">+</span> <span class="number">3</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream_iterator</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">cout</span><span class="special">,</span> <span class="string">" "</span><span class="special">)</span> <span class="special">);</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.gcd_header"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.gcd_header" title="Header &lt;boost/integer/common_factor.hpp&gt;">Header &lt;boost/integer/common_factor.hpp&gt;</a>
</h3></div></div></div>
<p>
        This header simply includes the headers <a href="../../../../../boost/integer/common_factor_ct.hpp" target="_top">&lt;boost/integer/common_factor_ct.hpp&gt;</a>
        and <a href="../../../../../boost/integer/common_factor_rt.hpp" target="_top">&lt;boost/integer/common_factor_rt.hpp&gt;</a>.
      </p>
<p>
        Note this is a legacy header: it used to contain the actual implementation,
        but the compile-time and run-time facilities were moved to separate headers
        (since they were independent of each other).
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.demo"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.demo" title="Demonstration Program">Demonstration Program</a>
</h3></div></div></div>
<p>
        The program <a href="../../../../../libs/integer/test/common_factor_test.cpp" target="_top">common_factor_test.cpp</a>
        is a demonstration of the results from instantiating various examples of
        the run-time GCD and LCM function templates and the compile-time GCD and
        LCM class templates. (The run-time GCD and LCM class templates are tested
        indirectly through the run-time function templates.)
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.rationale"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.rationale" title="Rationale">Rationale</a>
</h3></div></div></div>
<p>
        The greatest common divisor and least common multiple functions are greatly
        used in some numeric contexts, including some of the other Boost libraries.
        Centralizing these functions to one header improves code factoring and eases
        maintenance.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.gcd_history"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.gcd_history" title="History">History</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            24th April 2017 Moved to Jeremy Murphy's improved algorithms, added constexpr
            and noexcept support, added compiler intrinsic support, added variadic
            and range based versions of the algorithms.
          </li>
<li class="listitem">
            13 May 2013 Moved into main Boost.Math Quickbook documentation.
          </li>
<li class="listitem">
            17 Dec 2005: Converted documentation to Quickbook Format.
          </li>
<li class="listitem">
            2 Jul 2002: Compile-time and run-time items separated to new headers.
          </li>
<li class="listitem">
            7 Nov 2001: Initial version
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.gcd_lcm.gcd_credits"></a><a class="link" href="gcd_lcm.html#boost_integer.gcd_lcm.gcd_credits" title="Credits">Credits</a>
</h3></div></div></div>
<p>
        The author of the Boost compilation of GCD and LCM computations is Daryle
        Walker. The code was prompted by existing code hiding in the implementations
        of Paul Moore's rational library and Steve Cleary's pool library. The code
        had updates by Helmut Zeisel.
      </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2001-2009 Beman
      Dawes, Daryle Walker, Gennaro Prota, John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="https://www.boost.org/LICENSE_1_0.txt" target="_top">https://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="integer.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="extended_euclidean.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
