<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Compile time min/max calculation</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Boost.Integer">
<link rel="up" href="../index.html" title="Boost.Integer">
<link rel="prev" href="log2.html" title="Compile Time log2 Calculation">
<link rel="next" href="history.html" title="History">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="log2.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="history.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_integer.minmax"></a><a class="link" href="minmax.html" title="Compile time min/max calculation">Compile time min/max calculation</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="minmax.html#boost_integer.minmax.synopsis">Synopsis</a></span></dt>
<dt><span class="section"><a href="minmax.html#boost_integer.minmax.usage">Usage</a></span></dt>
<dt><span class="section"><a href="minmax.html#boost_integer.minmax.example">Example</a></span></dt>
<dt><span class="section"><a href="minmax.html#boost_integer.minmax.demonstration_program">Demonstration
      Program</a></span></dt>
<dt><span class="section"><a href="minmax.html#boost_integer.minmax.rationale">Rationale</a></span></dt>
<dt><span class="section"><a href="minmax.html#boost_integer.minmax.credits">Credits</a></span></dt>
</dl></div>
<p>
      The class templates in <a href="../../../../../boost/integer/static_min_max.hpp" target="_top">&lt;boost/integer/static_min_max.hpp&gt;</a>
      provide a compile-time evaluation of the minimum or maximum of two integers.
      These facilities are useful for generic programming problems.
    </p>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.minmax.synopsis"></a><a class="link" href="minmax.html#boost_integer.minmax.synopsis" title="Synopsis">Synopsis</a>
</h3></div></div></div>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>

<span class="keyword">typedef</span> <span class="emphasis"><em>implementation-defined</em></span> <span class="identifier">static_min_max_signed_type</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="emphasis"><em>implementation-defined</em></span> <span class="identifier">static_min_max_unsigned_type</span><span class="special">;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="identifier">static_min_max_signed_type</span> <span class="identifier">Value1</span><span class="special">,</span> <span class="identifier">static_min_max_signed_type</span> <span class="identifier">Value2</span> <span class="special">&gt;</span>
    <span class="keyword">struct</span> <span class="identifier">static_signed_min</span><span class="special">;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="identifier">static_min_max_signed_type</span> <span class="identifier">Value1</span><span class="special">,</span> <span class="identifier">static_min_max_signed_type</span> <span class="identifier">Value2</span><span class="special">&gt;</span>
    <span class="keyword">struct</span> <span class="identifier">static_signed_max</span><span class="special">;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="identifier">static_min_max_unsigned_type</span> <span class="identifier">Value1</span><span class="special">,</span> <span class="identifier">static_min_max_unsigned_type</span> <span class="identifier">Value2</span><span class="special">&gt;</span>
    <span class="keyword">struct</span> <span class="identifier">static_unsigned_min</span><span class="special">;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="identifier">static_min_max_unsigned_type</span> <span class="identifier">Value1</span><span class="special">,</span> <span class="identifier">static_min_max_unsigned_type</span> <span class="identifier">Value2</span><span class="special">&gt;</span>
    <span class="keyword">struct</span> <span class="identifier">static_unsigned_max</span><span class="special">;</span>

<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.minmax.usage"></a><a class="link" href="minmax.html#boost_integer.minmax.usage" title="Usage">Usage</a>
</h3></div></div></div>
<p>
        The four class templates provide the combinations for finding the minimum
        or maximum of two <code class="literal">signed</code> or <code class="literal">unsigned</code>
        (<code class="literal">long</code>) parameters, <span class="emphasis"><em>Value1</em></span> and <span class="emphasis"><em>Value2</em></span>,
        at compile-time. Each template has a single static data member, <code class="literal">value</code>,
        which is set to the respective minimum or maximum of the template's parameters.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.minmax.example"></a><a class="link" href="minmax.html#boost_integer.minmax.example" title="Example">Example</a>
</h3></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">integer</span><span class="special">/</span><span class="identifier">static_min_max</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">template</span> <span class="special">&lt;</span> <span class="keyword">unsigned</span> <span class="keyword">long</span> <span class="identifier">AddendSize1</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">long</span> <span class="identifier">AddendSize2</span> <span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">adder</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">static</span>  <span class="keyword">unsigned</span> <span class="keyword">long</span>  <span class="keyword">const</span>  <span class="identifier">addend1_size</span> <span class="special">=</span> <span class="identifier">AddendSize1</span><span class="special">;</span>
    <span class="keyword">static</span>  <span class="keyword">unsigned</span> <span class="keyword">long</span>  <span class="keyword">const</span>  <span class="identifier">addend2_size</span> <span class="special">=</span> <span class="identifier">AddendSize2</span><span class="special">;</span>
    <span class="keyword">static</span>  <span class="keyword">unsigned</span> <span class="keyword">long</span>  <span class="keyword">const</span>  <span class="identifier">sum_size</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">static_unsigned_max</span><span class="special">&lt;</span><span class="identifier">AddendSize1</span><span class="special">,</span> <span class="identifier">AddendSize2</span><span class="special">&gt;::</span><span class="identifier">value</span> <span class="special">+</span> <span class="number">1</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="keyword">int</span>  <span class="identifier">addend1_type</span><span class="special">[</span> <span class="identifier">addend1_size</span> <span class="special">];</span>
    <span class="keyword">typedef</span> <span class="keyword">int</span>  <span class="identifier">addend2_type</span><span class="special">[</span> <span class="identifier">addend2_size</span> <span class="special">];</span>
    <span class="keyword">typedef</span> <span class="keyword">int</span>  <span class="identifier">sum_type</span><span class="special">[</span> <span class="identifier">sum_size</span> <span class="special">];</span>

    <span class="keyword">void</span>  <span class="keyword">operator</span> <span class="special">()(</span> <span class="identifier">addend1_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a1</span><span class="special">,</span> <span class="identifier">addend2_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="identifier">a2</span><span class="special">,</span> <span class="identifier">sum_type</span> <span class="special">&amp;</span><span class="identifier">s</span> <span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
<span class="special">};</span>

<span class="comment">//...</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="keyword">int</span> <span class="keyword">const</span>   <span class="identifier">a1</span><span class="special">[]</span> <span class="special">=</span> <span class="special">{</span> <span class="number">0</span><span class="special">,</span> <span class="number">4</span><span class="special">,</span> <span class="number">3</span> <span class="special">};</span>  <span class="comment">// 340</span>
    <span class="keyword">int</span> <span class="keyword">const</span>   <span class="identifier">a2</span><span class="special">[]</span> <span class="special">=</span> <span class="special">{</span> <span class="number">9</span><span class="special">,</span> <span class="number">8</span> <span class="special">};</span>     <span class="comment">//  89</span>
    <span class="keyword">int</span>         <span class="identifier">s</span><span class="special">[</span> <span class="number">4</span> <span class="special">];</span>
    <span class="identifier">adder</span><span class="special">&lt;</span><span class="number">3</span><span class="special">,</span><span class="number">2</span><span class="special">&gt;</span>  <span class="identifier">obj</span><span class="special">;</span>

    <span class="identifier">obj</span><span class="special">(</span> <span class="identifier">a1</span><span class="special">,</span> <span class="identifier">a2</span><span class="special">,</span> <span class="identifier">s</span> <span class="special">);</span>  <span class="comment">// 's' should be 429 or { 9, 2, 4, 0 }</span>
    <span class="comment">//...</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.minmax.demonstration_program"></a><a class="link" href="minmax.html#boost_integer.minmax.demonstration_program" title="Demonstration Program">Demonstration
      Program</a>
</h3></div></div></div>
<p>
        The program <a href="../../../test/static_min_max_test.cpp" target="_top">static_min_max_test.cpp</a>
        is a simplistic demonstration of various comparisons using the compile-time
        extrema class templates.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.minmax.rationale"></a><a class="link" href="minmax.html#boost_integer.minmax.rationale" title="Rationale">Rationale</a>
</h3></div></div></div>
<p>
        Sometimes the minimum or maximum of several values needs to be found for
        later compile-time processing, <span class="emphasis"><em>e.g.</em></span> for a bound for
        another class template.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_integer.minmax.credits"></a><a class="link" href="minmax.html#boost_integer.minmax.credits" title="Credits">Credits</a>
</h3></div></div></div>
<p>
        The author of the Boost compile-time extrema class templates is <a href="https://www.boost.org/people/daryle_walker.html" target="_top">Daryle
        Walker</a>.
      </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2001-2009 Beman
      Dawes, Daryle Walker, Gennaro Prota, John Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="https://www.boost.org/LICENSE_1_0.txt" target="_top">https://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="log2.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="history.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
