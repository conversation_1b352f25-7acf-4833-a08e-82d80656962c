<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct template is_fundamental</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../../boost_compute/reference.html#header.boost.compute.type_traits.is_fundamental_hpp" title="Header &lt;boost/compute/type_traits/is_fundamental.hpp&gt;">
<link rel="prev" href="is_device_iterator.html" title="Struct template is_device_iterator">
<link rel="next" href="is_vector_type.html" title="Struct template is_vector_type">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_device_iterator.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.type_traits.is_fundamental_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="is_vector_type.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.compute.is_fundamental"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct template is_fundamental</span></h2>
<p>boost::compute::is_fundamental</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_compute/reference.html#header.boost.compute.type_traits.is_fundamental_hpp" title="Header &lt;boost/compute/type_traits/is_fundamental.hpp&gt;">boost/compute/type_traits/is_fundamental.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> 
<span class="keyword">struct</span> <a class="link" href="is_fundamental.html" title="Struct template is_fundamental">is_fundamental</a> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">false_type</span> <span class="special">{</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm37603"></a><h2>Description</h2>
<p>Meta-function returning <code class="computeroutput">true</code> if <code class="computeroutput">T</code> is a fundamental (i.e. built-in) type.</p>
<p>For example, </p>
<pre class="programlisting"><span class="identifier">is_fundamental</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span> <span class="special">==</span> <span class="keyword">true</span>
<span class="identifier">is_fundamental</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">,</span> <span class="keyword">float</span><span class="special">&gt;&gt;</span><span class="special">::</span><span class="identifier">value</span> <span class="special">==</span> <span class="keyword">false</span>
</pre>
<p> </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_device_iterator.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.type_traits.is_fundamental_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="is_vector_type.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
