<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class opengl_renderbuffer</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../../boost_compute/reference.html#header.boost.compute.interop.opengl.opengl_renderbuffer_hpp" title="Header &lt;boost/compute/interop/opengl/opengl_renderbuffer.hpp&gt;">
<link rel="prev" href="opengl_buffer.html" title="Class opengl_buffer">
<link rel="next" href="opengl_texture.html" title="Class opengl_texture">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="opengl_buffer.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.interop.opengl.opengl_renderbuffer_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="opengl_texture.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.compute.opengl_renderbuffer"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class opengl_renderbuffer</span></h2>
<p>boost::compute::opengl_renderbuffer</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_compute/reference.html#header.boost.compute.interop.opengl.opengl_renderbuffer_hpp" title="Header &lt;boost/compute/interop/opengl/opengl_renderbuffer.hpp&gt;">boost/compute/interop/opengl/opengl_renderbuffer.hpp</a>&gt;

</span>
<span class="keyword">class</span> <a class="link" href="opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer</a> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">compute</span><span class="special">::</span><span class="identifier">image_object</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// <a class="link" href="opengl_renderbuffer.html#boost.compute.opengl_renderbufferconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="opengl_renderbuffer.html#idm9588-bb"><span class="identifier">opengl_renderbuffer</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="opengl_renderbuffer.html#idm9590-bb"><span class="identifier">opengl_renderbuffer</span></a><span class="special">(</span><span class="identifier">cl_mem</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">=</span> <span class="keyword">true</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="opengl_renderbuffer.html#idm9598-bb"><span class="identifier">opengl_renderbuffer</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">context</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">GLuint</span><span class="special">,</span> <span class="identifier">cl_mem_flags</span> <span class="special">=</span> <span class="identifier">read_write</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="opengl_renderbuffer.html#idm9613-bb"><span class="identifier">opengl_renderbuffer</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer</a> <span class="special">&amp;</span> <a class="link" href="opengl_renderbuffer.html#idm9619-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="opengl_renderbuffer.html#idm9627-bb"><span class="special">~</span><span class="identifier">opengl_renderbuffer</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="opengl_renderbuffer.html#idm9565-bb">public member functions</a></span>
  <a class="link" href="extents.html" title="Class template extents">extents</a><span class="special">&lt;</span> <span class="number">2</span> <span class="special">&gt;</span> <a class="link" href="opengl_renderbuffer.html#idm9566-bb"><span class="identifier">size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <a class="link" href="extents.html" title="Class template extents">extents</a><span class="special">&lt;</span> <span class="number">2</span> <span class="special">&gt;</span> <a class="link" href="opengl_renderbuffer.html#idm9570-bb"><span class="identifier">origin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">GLuint</span> <a class="link" href="opengl_renderbuffer.html#idm9576-bb"><span class="identifier">get_opengl_object</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">cl_gl_object_type</span> <a class="link" href="opengl_renderbuffer.html#idm9582-bb"><span class="identifier">get_opengl_type</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm29786"></a><h2>Description</h2>
<p>A OpenCL buffer for accessing an OpenGL renderbuffer object. </p>
<div class="refsect2">
<a name="idm29789"></a><h3>
<a name="boost.compute.opengl_renderbufferconstruct-copy-destruct"></a><code class="computeroutput">opengl_renderbuffer</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm9588-bb"></a><span class="identifier">opengl_renderbuffer</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Creates a null OpenGL renderbuffer object. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm9590-bb"></a><span class="identifier">opengl_renderbuffer</span><span class="special">(</span><span class="identifier">cl_mem</span> mem<span class="special">,</span> <span class="keyword">bool</span> retain <span class="special">=</span> <span class="keyword">true</span><span class="special">)</span><span class="special">;</span></pre>Creates a new OpenGL renderbuffer object for <code class="computeroutput">mem</code>. </li>
<li class="listitem">
<pre class="literallayout"><a name="idm9598-bb"></a><span class="identifier">opengl_renderbuffer</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">context</span> <span class="special">&amp;</span> context<span class="special">,</span> <span class="identifier">GLuint</span> renderbuffer<span class="special">,</span> 
                    <span class="identifier">cl_mem_flags</span> flags <span class="special">=</span> <span class="identifier">read_write</span><span class="special">)</span><span class="special">;</span></pre>
<p>Creates a new OpenGL renderbuffer object in <code class="computeroutput">context</code> for <code class="computeroutput">renderbuffer</code> with <code class="computeroutput">flags</code>.</p>
<p>See the documentation for <a href="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clCreateFromGLRenderbuffer.html" target="_top">clCreateFromGLRenderbuffer()</a> for more information. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9613-bb"></a><span class="identifier">opengl_renderbuffer</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer</a> <span class="special">&amp;</span> other<span class="special">)</span><span class="special">;</span></pre>Creates a new OpenGL renderbuffer object as a copy of <code class="computeroutput">other</code>. </li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer</a> <span class="special">&amp;</span> <a name="idm9619-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer</a> <span class="special">&amp;</span> other<span class="special">)</span><span class="special">;</span></pre>Copies the OpenGL renderbuffer object from <code class="computeroutput">other</code>. </li>
<li class="listitem">
<pre class="literallayout"><a name="idm9627-bb"></a><span class="special">~</span><span class="identifier">opengl_renderbuffer</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Destroys the OpenGL buffer object. </li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm29876"></a><h3>
<a name="idm9565-bb"></a><code class="computeroutput">opengl_renderbuffer</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a class="link" href="extents.html" title="Class template extents">extents</a><span class="special">&lt;</span> <span class="number">2</span> <span class="special">&gt;</span> <a name="idm9566-bb"></a><span class="identifier">size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Returns the size (width, height) of the renderbuffer. </li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="extents.html" title="Class template extents">extents</a><span class="special">&lt;</span> <span class="number">2</span> <span class="special">&gt;</span> <a name="idm9570-bb"></a><span class="identifier">origin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Returns the origin of the renderbuffer (<code class="computeroutput">0</code>, <code class="computeroutput">0</code>). </li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">GLuint</span> <a name="idm9576-bb"></a><span class="identifier">get_opengl_object</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Returns the OpenGL memory object ID.</p>
<p>See the documentation for <a href="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetGLObjectInfo.html" target="_top">clGetGLObjectInfo()</a> for more information. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">cl_gl_object_type</span> <a name="idm9582-bb"></a><span class="identifier">get_opengl_type</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Returns the OpenGL memory object type.</p>
<p>See the documentation for <a href="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetGLObjectInfo.html" target="_top">clGetGLObjectInfo()</a> for more information. </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="opengl_buffer.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.interop.opengl.opengl_renderbuffer_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="opengl_texture.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
