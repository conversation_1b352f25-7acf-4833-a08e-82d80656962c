<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template future</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../../boost_compute/reference.html#header.boost.compute.async.future_hpp" title="Header &lt;boost/compute/async/future.hpp&gt;">
<link rel="prev" href="pinned_allocator.html" title="Class template pinned_allocator">
<link rel="next" href="wait_for_all.html" title="Function template wait_for_all">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="pinned_allocator.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.async.future_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="wait_for_all.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.compute.future"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template future</span></h2>
<p>boost::compute::future — Holds the result of an asynchronous computation. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_compute/reference.html#header.boost.compute.async.future_hpp" title="Header &lt;boost/compute/async/future.hpp&gt;">boost/compute/async/future.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="future.html" title="Class template future">future</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// <a class="link" href="future.html#boost.compute.futureconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="future.html#idm6183-bb"><span class="identifier">future</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="future.html#idm6184-bb"><span class="identifier">future</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">event</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="future.html#idm6189-bb"><span class="identifier">future</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="future.html" title="Class template future">future</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="future.html" title="Class template future">future</a> <span class="special">&amp;</span> <a class="link" href="future.html#idm6193-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="future.html" title="Class template future">future</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="future.html#idm6199-bb"><span class="special">~</span><span class="identifier">future</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="future.html#idm6154-bb">public member functions</a></span>
  <span class="identifier">T</span> <a class="link" href="future.html#idm6155-bb"><span class="identifier">get</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="future.html#idm6159-bb"><span class="identifier">valid</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="future.html#idm6163-bb"><span class="identifier">wait</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">event</span> <a class="link" href="future.html#idm6166-bb"><span class="identifier">get_event</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Function<span class="special">&gt;</span> <a class="link" href="future.html" title="Class template future">future</a> <span class="special">&amp;</span> <a class="link" href="future.html#idm6169-bb"><span class="identifier">then</span></a><span class="special">(</span><span class="identifier">Function</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm14121"></a><h2>Description</h2>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p>event, <a class="link" href="wait_list.html" title="Class wait_list">wait_list</a> </p>
<p>
</p>
<p>
</p>
<div class="refsect2">
<a name="idm14128"></a><h3>
<a name="boost.compute.futureconstruct-copy-destruct"></a><code class="computeroutput">future</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><a name="idm6183-bb"></a><span class="identifier">future</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a name="idm6184-bb"></a><span class="identifier">future</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> result<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">event</span> <span class="special">&amp;</span> event<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a name="idm6189-bb"></a><span class="identifier">future</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="future.html" title="Class template future">future</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&amp;</span> other<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="future.html" title="Class template future">future</a> <span class="special">&amp;</span> <a name="idm6193-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="future.html" title="Class template future">future</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&amp;</span> other<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a name="idm6199-bb"></a><span class="special">~</span><span class="identifier">future</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm14196"></a><h3>
<a name="idm6154-bb"></a><code class="computeroutput">future</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="identifier">T</span> <a name="idm6155-bb"></a><span class="identifier">get</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Returns the result of the computation. This will block until the result is ready. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm6159-bb"></a><span class="identifier">valid</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Returns <code class="computeroutput">true</code> if the future is valid. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6163-bb"></a><span class="identifier">wait</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Blocks until the computation is complete. </li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">event</span> <a name="idm6166-bb"></a><span class="identifier">get_event</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Returns the underlying event object. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Function<span class="special">&gt;</span> <a class="link" href="future.html" title="Class template future">future</a> <span class="special">&amp;</span> <a name="idm6169-bb"></a><span class="identifier">then</span><span class="special">(</span><span class="identifier">Function</span> callback<span class="special">)</span><span class="special">;</span></pre>
<p>Invokes a generic callback function once the future is ready.</p>
<p>The function specified by callback must be invokable with zero arguments.</p>
<p>See the documentation for <a href="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clSetEventCallback.html" target="_top">clSetEventCallback()</a> for more information. </p>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top"><p>This method is only available if the OpenCL version is 1.1 or later. </p></td></tr>
</table></div>
<p>
</p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="pinned_allocator.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.async.future_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="wait_for_all.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
