<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function template fill</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../../boost_compute/reference.html#header.boost.compute.algorithm.fill_hpp" title="Header &lt;boost/compute/algorithm/fill.hpp&gt;">
<link rel="prev" href="exclusive_scan.html" title="Function exclusive_scan">
<link rel="next" href="fill_n.html" title="Function template fill_n">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="exclusive_scan.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.algorithm.fill_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="fill_n.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.compute.fill"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function template fill</span></h2>
<p>boost::compute::fill</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_compute/reference.html#header.boost.compute.algorithm.fill_hpp" title="Header &lt;boost/compute/algorithm/fill.hpp&gt;">boost/compute/algorithm/fill.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BufferIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
  <span class="keyword">void</span> <span class="identifier">fill</span><span class="special">(</span><span class="identifier">BufferIterator</span> first<span class="special">,</span> <span class="identifier">BufferIterator</span> last<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> value<span class="special">,</span> 
            <span class="identifier">command_queue</span> <span class="special">&amp;</span> queue <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm5403"></a><h2>Description</h2>
<p>Fills the range [<code class="computeroutput">first</code>, <code class="computeroutput">last</code>) with <code class="computeroutput">value</code>.</p>
<p>
For example, to fill a vector on the device with sevens: </p>
<pre class="programlisting"><span class="comment">// vector on the device</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">compute</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="identifier">vec</span><span class="special">(</span><span class="number">10</span><span class="special">,</span> <span class="identifier">context</span><span class="special">)</span><span class="special">;</span>

<span class="comment">// fill vector with sevens</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">compute</span><span class="special">::</span><span class="identifier">fill</span><span class="special">(</span><span class="identifier">vec</span><span class="special">.</span><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span><span class="special">,</span> <span class="identifier">vec</span><span class="special">.</span><span class="identifier">end</span><span class="special">(</span><span class="special">)</span><span class="special">,</span> <span class="number">7</span><span class="special">,</span> <span class="identifier">queue</span><span class="special">)</span><span class="special">;</span>
</pre>
<p>Space complexity: \Omega(1)</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p>boost::compute::fill_n() </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>first element in the range to fill </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>last element in the range to fill </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">queue</code></span></p></td>
<td><p>command queue to perform the operation</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">value</code></span></p></td>
<td><p>value to copy to each element </p></td>
</tr>
</tbody>
</table></div></td>
</tr></tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="exclusive_scan.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.algorithm.fill_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="fill_n.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
