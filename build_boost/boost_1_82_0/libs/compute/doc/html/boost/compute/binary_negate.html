<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template binary_negate</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../../boost_compute/reference.html#header.boost.compute.functional.logical_hpp" title="Header &lt;boost/compute/functional/logical.hpp&gt;">
<link rel="prev" href="identity.html" title="Class template identity">
<link rel="next" href="logical_not.html" title="Struct template logical_not">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="identity.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.functional.logical_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="logical_not.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.compute.binary_negate"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template binary_negate</span></h2>
<p>boost::compute::binary_negate</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_compute/reference.html#header.boost.compute.functional.logical_hpp" title="Header &lt;boost/compute/functional/logical.hpp&gt;">boost/compute/functional/logical.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="binary_negate.html" title="Class template binary_negate">binary_negate</a> <span class="special">:</span>
  <span class="keyword">public</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">compute</span><span class="special">::</span><span class="identifier">binary_function</span><span class="special">&lt;</span> <span class="keyword">void</span><span class="special">,</span> <span class="keyword">void</span><span class="special">,</span> <span class="keyword">int</span> <span class="special">&gt;</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// <a class="link" href="binary_negate.html#boost.compute.binary_negateconstruct-copy-destruct">construct/copy/destruct</a></span>
  <span class="keyword">explicit</span> <a class="link" href="binary_negate.html#idm8558-bb"><span class="identifier">binary_negate</span></a><span class="special">(</span><span class="identifier">Predicate</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm26299"></a><h2>Description</h2>
<p>The binnary_negate function adaptor negates a binary function.</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p>not2() </p>
<p>
</p>
<p>
</p>
<div class="refsect2">
<a name="idm26306"></a><h3>
<a name="boost.compute.binary_negateconstruct-copy-destruct"></a><code class="computeroutput">binary_negate</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><pre class="literallayout"><span class="keyword">explicit</span> <a name="idm8558-bb"></a><span class="identifier">binary_negate</span><span class="special">(</span><span class="identifier">Predicate</span> pred<span class="special">)</span><span class="special">;</span></pre></li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="identity.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.functional.logical_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="logical_not.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
