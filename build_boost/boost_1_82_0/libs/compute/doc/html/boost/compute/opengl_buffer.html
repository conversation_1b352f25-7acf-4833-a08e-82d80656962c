<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class opengl_buffer</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../../boost_compute/reference.html#header.boost.compute.interop.opengl.opengl_buffer_hpp" title="Header &lt;boost/compute/interop/opengl/opengl_buffer.hpp&gt;">
<link rel="prev" href="opengl_create_shar_idm9487.html" title="Function opengl_create_shared_context">
<link rel="next" href="opengl_renderbuffer.html" title="Class opengl_renderbuffer">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="opengl_create_shar_idm9487.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.interop.opengl.opengl_buffer_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="opengl_renderbuffer.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.compute.opengl_buffer"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class opengl_buffer</span></h2>
<p>boost::compute::opengl_buffer</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_compute/reference.html#header.boost.compute.interop.opengl.opengl_buffer_hpp" title="Header &lt;boost/compute/interop/opengl/opengl_buffer.hpp&gt;">boost/compute/interop/opengl/opengl_buffer.hpp</a>&gt;

</span>
<span class="keyword">class</span> <a class="link" href="opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">buffer</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// <a class="link" href="opengl_buffer.html#boost.compute.opengl_bufferconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="opengl_buffer.html#idm9517-bb"><span class="identifier">opengl_buffer</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="opengl_buffer.html#idm9519-bb"><span class="identifier">opengl_buffer</span></a><span class="special">(</span><span class="identifier">cl_mem</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">=</span> <span class="keyword">true</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="opengl_buffer.html#idm9527-bb"><span class="identifier">opengl_buffer</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">context</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">GLuint</span><span class="special">,</span> <span class="identifier">cl_mem_flags</span> <span class="special">=</span> <span class="identifier">read_write</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="opengl_buffer.html#idm9542-bb"><span class="identifier">opengl_buffer</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a> <span class="special">&amp;</span> <a class="link" href="opengl_buffer.html#idm9548-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="opengl_buffer.html#idm9556-bb"><span class="special">~</span><span class="identifier">opengl_buffer</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="opengl_buffer.html#idm9504-bb">public member functions</a></span>
  <span class="identifier">GLuint</span> <a class="link" href="opengl_buffer.html#idm9505-bb"><span class="identifier">get_opengl_object</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">cl_gl_object_type</span> <a class="link" href="opengl_buffer.html#idm9511-bb"><span class="identifier">get_opengl_type</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm29532"></a><h2>Description</h2>
<p>A OpenCL buffer for accessing an OpenGL memory object. </p>
<div class="refsect2">
<a name="idm29535"></a><h3>
<a name="boost.compute.opengl_bufferconstruct-copy-destruct"></a><code class="computeroutput">opengl_buffer</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm9517-bb"></a><span class="identifier">opengl_buffer</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Creates a null OpenGL buffer object. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm9519-bb"></a><span class="identifier">opengl_buffer</span><span class="special">(</span><span class="identifier">cl_mem</span> mem<span class="special">,</span> <span class="keyword">bool</span> retain <span class="special">=</span> <span class="keyword">true</span><span class="special">)</span><span class="special">;</span></pre>Creates a new OpenGL buffer object for <code class="computeroutput">mem</code>. </li>
<li class="listitem">
<pre class="literallayout"><a name="idm9527-bb"></a><span class="identifier">opengl_buffer</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">context</span> <span class="special">&amp;</span> context<span class="special">,</span> <span class="identifier">GLuint</span> bufobj<span class="special">,</span> 
              <span class="identifier">cl_mem_flags</span> flags <span class="special">=</span> <span class="identifier">read_write</span><span class="special">)</span><span class="special">;</span></pre>
<p>Creates a new OpenGL buffer object in <code class="computeroutput">context</code> for <code class="computeroutput">bufobj</code> with <code class="computeroutput">flags</code>.</p>
<p>See the documentation for <a href="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clCreateFromGLBuffer.html" target="_top">clCreateFromGLBuffer()</a> for more information. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9542-bb"></a><span class="identifier">opengl_buffer</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a> <span class="special">&amp;</span> other<span class="special">)</span><span class="special">;</span></pre>Creates a new OpenGL buffer object as a copy of <code class="computeroutput">other</code>. </li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a> <span class="special">&amp;</span> <a name="idm9548-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a> <span class="special">&amp;</span> other<span class="special">)</span><span class="special">;</span></pre>Copies the OpenGL buffer object from <code class="computeroutput">other</code>. </li>
<li class="listitem">
<pre class="literallayout"><a name="idm9556-bb"></a><span class="special">~</span><span class="identifier">opengl_buffer</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Destroys the OpenGL buffer object. </li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm29622"></a><h3>
<a name="idm9504-bb"></a><code class="computeroutput">opengl_buffer</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="identifier">GLuint</span> <a name="idm9505-bb"></a><span class="identifier">get_opengl_object</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Returns the OpenGL memory object ID.</p>
<p>See the documentation for <a href="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetGLObjectInfo.html" target="_top">clGetGLObjectInfo()</a> for more information. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">cl_gl_object_type</span> <a name="idm9511-bb"></a><span class="identifier">get_opengl_type</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Returns the OpenGL memory object type.</p>
<p>See the documentation for <a href="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetGLObjectInfo.html" target="_top">clGetGLObjectInfo()</a> for more information. </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="opengl_create_shar_idm9487.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_compute/reference.html#header.boost.compute.interop.opengl.opengl_buffer_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="opengl_renderbuffer.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
