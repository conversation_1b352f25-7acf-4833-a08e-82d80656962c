<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Frequently Asked Questions</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="prev" href="performance.html" title="Performance">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="performance.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_compute.faq"></a><a class="link" href="faq.html" title="Frequently Asked Questions">Frequently Asked Questions</a>
</h2></div></div></div>
<h4>
<a name="boost_compute.faq.h0"></a>
      <span class="phrase"><a name="boost_compute.faq.how_do_i_report_a_bug__issue__or_feature_request_"></a></span><a class="link" href="faq.html#boost_compute.faq.how_do_i_report_a_bug__issue__or_feature_request_">How
      do I report a bug, issue, or feature request?</a>
    </h4>
<p>
      Please submit an issue on the GitHub issue tracker at <a href="https://github.com/boostorg/compute/issues" target="_top">https://github.com/boostorg/compute/issues</a>.
    </p>
<h4>
<a name="boost_compute.faq.h1"></a>
      <span class="phrase"><a name="boost_compute.faq.where_can_i_find_more_documentation_"></a></span><a class="link" href="faq.html#boost_compute.faq.where_can_i_find_more_documentation_">Where can
      I find more documentation?</a>
    </h4>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          The main documentation is here: <a href="http://boostorg.github.io/compute/" target="_top">http://boostorg.github.io/compute/</a>
        </li>
<li class="listitem">
          The README is here: <a href="https://github.com/boostorg/compute/blob/master/README.md" target="_top">https://github.com/boostorg/compute/blob/master/README.md</a>
        </li>
<li class="listitem">
          The wiki is here: <a href="https://github.com/boostorg/compute/wiki" target="_top">https://github.com/boostorg/compute/wiki</a>
        </li>
<li class="listitem">
          The contributor guide is here: <a href="https://github.com/boostorg/compute/blob/master/CONTRIBUTING.md" target="_top">https://github.com/boostorg/compute/blob/master/CONTRIBUTING.md</a>
        </li>
<li class="listitem">
          The reference is here: <a href="http://boostorg.github.io/compute/compute/reference.html" target="_top">http://boostorg.github.io/compute/compute/reference.html</a>
        </li>
</ul></div>
<h4>
<a name="boost_compute.faq.h2"></a>
      <span class="phrase"><a name="boost_compute.faq.where_is_the_best_place_to_ask_questions_about_the_library_"></a></span><a class="link" href="faq.html#boost_compute.faq.where_is_the_best_place_to_ask_questions_about_the_library_">Where
      is the best place to ask questions about the library?</a>
    </h4>
<p>
      The mailing list at <a href="https://groups.google.com/forum/#!forum/boost-compute" target="_top">https://groups.google.com/forum/#!forum/boost-compute</a>.
    </p>
<h4>
<a name="boost_compute.faq.h3"></a>
      <span class="phrase"><a name="boost_compute.faq.what_compute_devices__e_g__gpus__are_supported_"></a></span><a class="link" href="faq.html#boost_compute.faq.what_compute_devices__e_g__gpus__are_supported_">What
      compute devices (e.g. GPUs) are supported?</a>
    </h4>
<p>
      Any device which implements the OpenCL standard is supported. This includes
      GPUs from NVIDIA, AMD, and Intel as well as CPUs from AMD and Intel and other
      accelerator cards such as the Xeon Phi.
    </p>
<h4>
<a name="boost_compute.faq.h4"></a>
      <span class="phrase"><a name="boost_compute.faq.can_you_compare_boost_compute_to_other_gpgpu_libraries_such_as_thrust__bolt_and_vexcl_"></a></span><a class="link" href="faq.html#boost_compute.faq.can_you_compare_boost_compute_to_other_gpgpu_libraries_such_as_thrust__bolt_and_vexcl_">Can
      you compare Boost.Compute to other GPGPU libraries such as Thrust, Bolt and
      VexCL?</a>
    </h4>
<p>
      Thrust implements a C++ STL-like API for GPUs and CPUs. It is built with multiple
      backends. NVIDIA GPUs use the CUDA backend and multi-core CPUs can use the
      Intel TBB or OpenMP backends. However, thrust will not work with AMD graphics
      cards or other lesser-known accelerators. I feel Boost.Compute is superior
      in that it uses the vendor-neutral OpenCL library to achieve portability across
      all types of compute devices.
    </p>
<p>
      Bolt is an AMD specific C++ wrapper around the OpenCL API which extends the
      C99-based OpenCL language to support C++ features (most notably templates).
      It is similar to NVIDIA's Thrust library and shares the same failure, lack
      of portability.
    </p>
<p>
      VexCL is an expression-template based linear-algebra library for OpenCL. The
      aims and scope are a bit different from the Boost Compute library. VexCL is
      closer in nature to the Eigen library while Boost.Compute is closer to the
      C++ standard library. I don't feel that Boost.Compute really fills the same
      role as VexCL. In fact, the recent versions of VexCL allow to use Boost.Compute
      as one of the backends, which makes the interaction between the two libraries
      a breeze.
    </p>
<p>
      Also see this StackOverflow question: <a href="http://stackoverflow.com/questions/20154179/differences-between-vexcl-thrust-and-boost-compute" target="_top">http://stackoverflow.com/questions/20154179/differences-between-vexcl-thrust-and-boost-compute</a>
    </p>
<h4>
<a name="boost_compute.faq.h5"></a>
      <span class="phrase"><a name="boost_compute.faq.why_not_write_just_write_a_new_opencl_back_end_for_thrust_"></a></span><a class="link" href="faq.html#boost_compute.faq.why_not_write_just_write_a_new_opencl_back_end_for_thrust_">Why
      not write just write a new OpenCL back-end for Thrust?</a>
    </h4>
<p>
      It would not be possible to provide the same API that Thrust expects for OpenCL.
      The fundamental reason is that functions/functors passed to Thrust algorithms
      are actual compiled C++ functions whereas for Boost.Compute these form expression
      objects which are then translated into C99 code which is then compiled for
      OpenCL.
    </p>
<h4>
<a name="boost_compute.faq.h6"></a>
      <span class="phrase"><a name="boost_compute.faq.why_not_target_cuda_and_or_support_multiple_back_ends_"></a></span><a class="link" href="faq.html#boost_compute.faq.why_not_target_cuda_and_or_support_multiple_back_ends_">Why
      not target CUDA and/or support multiple back-ends?</a>
    </h4>
<p>
      CUDA and OpenCL are two very different technologies. OpenCL works by compiling
      C99 code at run-time to generate kernel objects which can then be executed
      on the GPU. CUDA, on the other hand, works by compiling its kernels using a
      special compiler (nvcc) which then produces binaries which can executed on
      the GPU.
    </p>
<p>
      OpenCL already has multiple implementations which allow it to be used on a
      variety of platforms (e.g. NVIDIA GPUs, Intel CPUs, etc.). I feel that adding
      another abstraction level within Boost.Compute would only complicate and bloat
      the library.
    </p>
<h4>
<a name="boost_compute.faq.h7"></a>
      <span class="phrase"><a name="boost_compute.faq.is_it_possible_to_use_ordinary_c___functions_functors_or_c__11__lambdas_with_boost_compute_"></a></span><a class="link" href="faq.html#boost_compute.faq.is_it_possible_to_use_ordinary_c___functions_functors_or_c__11__lambdas_with_boost_compute_">Is
      it possible to use ordinary C++ functions/functors or C++11 lambdas with Boost.Compute?</a>
    </h4>
<p>
      Unfortunately no. OpenCL relies on having C99 source code available at run-time
      in order to execute code on the GPU. Thus compiled C++ functions or C++11 lambdas
      cannot simply be passed to the OpenCL environment to be executed on the GPU.
    </p>
<p>
      This is the reason why I wrote the Boost.Compute lambda library. Basically
      it takes C++ lambda expressions (e.g. _1 * sqrt(_1) + 4) and transforms them
      into C99 source code fragments (e.g. “input[i] * sqrt(input[i]) + 4)”)
      which are then passed to the Boost.Compute STL-style algorithms for execution.
      While not perfect, it allows the user to write code closer to C++ that still
      can be executed through OpenCL.
    </p>
<p>
      Also check out the BOOST_COMPUTE_FUNCTION() macro which allows OpenCL functions
      to be defined inline with C++ code. An example can be found in the monte_carlo
      example code.
    </p>
<h4>
<a name="boost_compute.faq.h8"></a>
      <span class="phrase"><a name="boost_compute.faq.what_is_the_command_queue_argument_that_appears_in_all_of_the_algorithms_"></a></span><a class="link" href="faq.html#boost_compute.faq.what_is_the_command_queue_argument_that_appears_in_all_of_the_algorithms_">What
      is the command_queue argument that appears in all of the algorithms?</a>
    </h4>
<p>
      Command queues specify the context and device for the algorithm's execution.
      For all of the standard algorithms the command_queue parameter is optional.
      If not provided, a default command_queue will be created for the default GPU
      device and the algorithm will be executed there.
    </p>
<h4>
<a name="boost_compute.faq.h9"></a>
      <span class="phrase"><a name="boost_compute.faq.how_can_i_print_out_the_contents_of_a_buffer_vector_on_the_gpu_"></a></span><a class="link" href="faq.html#boost_compute.faq.how_can_i_print_out_the_contents_of_a_buffer_vector_on_the_gpu_">How
      can I print out the contents of a buffer/vector on the GPU?</a>
    </h4>
<p>
      This can be accompilshed easily using the generic boost::compute::copy() algorithm
      along with std::ostream_iterator&lt;T&gt;. For example:
    </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"vector: [ "</span><span class="special">;</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">compute</span><span class="special">::</span><span class="identifier">copy</span><span class="special">(</span>
    <span class="identifier">vector</span><span class="special">.</span><span class="identifier">begin</span><span class="special">(),</span> <span class="identifier">vector</span><span class="special">.</span><span class="identifier">end</span><span class="special">(),</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream_iterator</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span><span class="special">,</span> <span class="string">", "</span><span class="special">),</span>
    <span class="identifier">queue</span>
<span class="special">);</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"]"</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
</pre>
<p>
    </p>
<h4>
<a name="boost_compute.faq.h10"></a>
      <span class="phrase"><a name="boost_compute.faq.does_boost_compute_support_zero_copy_memory_"></a></span><a class="link" href="faq.html#boost_compute.faq.does_boost_compute_support_zero_copy_memory_">Does
      Boost.Compute support zero-copy memory?</a>
    </h4>
<p>
      Zero-copy memory allows OpenCL kernels to directly operate on regions of host
      memory (if supported by the platform).
    </p>
<p>
      Boost.Compute supports zero-copy memory in multiple ways. The low-level interface
      is provided by allocating <code class="computeroutput">buffer</code>
      objects with the <code class="computeroutput"><span class="identifier">CL_MEM_USE_HOST_PTR</span></code>
      flag. The high-level interface is provided by the <code class="computeroutput"><a class="link" href="../boost/compute/mapped_view.html" title="Class template mapped_view">mapped_view&lt;T&gt;</a></code>
      class which provides a std::vector-like interface to a region of host-memory
      and can be used directly with all of the Boost.Compute algorithms.
    </p>
<h4>
<a name="boost_compute.faq.h11"></a>
      <span class="phrase"><a name="boost_compute.faq.is_boost_compute_thread_safe_"></a></span><a class="link" href="faq.html#boost_compute.faq.is_boost_compute_thread_safe_">Is
      Boost.Compute thread-safe?</a>
    </h4>
<p>
      The low-level Boost.Compute APIs offer the same thread-safety guarantees as
      the underyling OpenCL library implementation. However, the high-level APIs
      make use of a few global static objects for features such as automatic program
      caching which makes them not thread-safe by default.
    </p>
<p>
      To compile Boost.Compute in thread-safe mode define <code class="computeroutput"><span class="identifier">BOOST_COMPUTE_THREAD_SAFE</span></code>
      before including any of the Boost.Compute headers. By default this will require
      linking your application/library with the Boost.Thread library.
    </p>
<h4>
<a name="boost_compute.faq.h12"></a>
      <span class="phrase"><a name="boost_compute.faq.what_applications_libraries_use_boost_compute_"></a></span><a class="link" href="faq.html#boost_compute.faq.what_applications_libraries_use_boost_compute_">What
      applications/libraries use Boost.Compute?</a>
    </h4>
<p>
      Boost.Compute is used by a number of open-source libraries and applications
      including:
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          ArrayFire (<a href="http://arrayfire.com" target="_top">http://arrayfire.com</a>)
        </li>
<li class="listitem">
          Ceemple (<a href="http://www.ceemple.com" target="_top">http://www.ceemple.com</a>)
        </li>
<li class="listitem">
          Odeint (<a href="http://headmyshoulder.github.io/odeint-v2" target="_top">http://headmyshoulder.github.io/odeint-v2</a>)
        </li>
<li class="listitem">
          VexCL (<a href="https://github.com/ddemidov/vexcl" target="_top">https://github.com/ddemidov/vexcl</a>)
        </li>
</ul></div>
<p>
      If you use Boost.Compute in your project and would like it to be listed here
      please send an email to Kyle Lutz (<EMAIL>).
    </p>
<h4>
<a name="boost_compute.faq.h13"></a>
      <span class="phrase"><a name="boost_compute.faq.how_can_i_contribute_"></a></span><a class="link" href="faq.html#boost_compute.faq.how_can_i_contribute_">How
      can I contribute?</a>
    </h4>
<p>
      We are actively seeking additional C++ developers with experience in GPGPU
      and parallel-computing.
    </p>
<p>
      Please send an email to Kyle Lutz (<EMAIL>) for more information.
    </p>
<p>
      Also see the <a href="https://github.com/boostorg/compute/blob/master/CONTRIBUTING.md" target="_top">contributor
      guide</a> and check out the list of issues at: <a href="https://github.com/boostorg/compute/issues" target="_top">https://github.com/boostorg/compute/issues</a>.
    </p>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="performance.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
</body>
</html>
