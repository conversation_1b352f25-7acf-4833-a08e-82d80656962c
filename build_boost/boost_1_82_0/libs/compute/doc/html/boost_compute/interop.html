<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Interoperability</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="prev" href="advanced_topics.html" title="Advanced Topics">
<link rel="next" href="porting_guide.html" title="Porting Guide">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="advanced_topics.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="porting_guide.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_compute.interop"></a><a class="link" href="interop.html" title="Interoperability">Interoperability</a>
</h2></div></div></div>
<div class="toc"><dl class="toc"><dt><span class="section"><a href="interop.html#boost_compute.interop.opengl">OpenGL</a></span></dt></dl></div>
<p>
      Boost.Compute provides a number of facilities to ease interoperability with
      other C and C++ libraries including <a href="http://en.wikipedia.org/wiki/OpenGL" target="_top">OpenGL</a>,
      <a href="http://opencv.org/" target="_top">OpenCV</a>, <a href="http://eigen.tuxfamily.org" target="_top">Eigen</a>,
      <a href="http://qt-project.org/" target="_top">Qt</a>, and <a href="http://vtk.org/" target="_top">VTK</a>.
    </p>
<p>
      All of the interop headers are located in the <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">interop</span><span class="special">/...&gt;</span></code> directory.
    </p>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_compute.interop.opengl"></a><a class="link" href="interop.html#boost_compute.interop.opengl" title="OpenGL">OpenGL</a>
</h3></div></div></div>
<p>
        OpenGL is a standard API for 3D graphics and closely related to OpenCL.
      </p>
<p>
        Boost.Compute provides an easy way to create a shared OpenCL/OpenGL context
        via the <code class="computeroutput"><a class="link" href="../boost/compute/opengl_create_shar_idm9487.html" title="Function opengl_create_shared_context">opengl_create_shared_context()</a></code>
        function. This creates a Boost.Compute <code class="computeroutput">context</code>
        object which can be used to create OpenCL memory buffers corresponding to
        OpenGL memory objects (such as VBOs). This allows for OpenCL kernels to read
        and write data which is then used directly for rendering with OpenGL.
      </p>
<p>
        Boost.Compute provides a few classes to manage OpenGL objects and make them
        accessible to OpenCL. These include <code class="computeroutput"><a class="link" href="../boost/compute/opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a></code>
        (for OpenGL memory objects) and <code class="computeroutput"><a class="link" href="../boost/compute/opengl_texture.html" title="Class opengl_texture">opengl_texture</a></code>
        (for OpenGL texture objects).
      </p>
<p>
        Control of shared OpenCL/OpenGL memory objects is transfered between OpenCL
        and OpenGL using the <code class="computeroutput"><a class="link" href="../boost/compute/opengl_enqueue_acq_idm9416.html" title="Function opengl_enqueue_acquire_gl_objects">opengl_enqueue_acquire_gl_objects()</a></code>
        and <code class="computeroutput"><a class="link" href="../boost/compute/opengl_enqueue_rel_idm9433.html" title="Function opengl_enqueue_release_gl_objects">opengl_enqueue_release_gl_objects()</a></code>
        functions.
      </p>
<p>
        See the OpenCL documentation for the <a href="https://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/cl_khr_gl_sharing.html" target="_top">cl_khr_gl_sharing</a>
        extension for more information.
      </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="advanced_topics.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="porting_guide.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
