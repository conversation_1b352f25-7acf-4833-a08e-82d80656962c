<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Design</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="prev" href="getting_started.html" title="Getting Started">
<link rel="next" href="tutorial.html" title="Tutorial">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="getting_started.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="tutorial.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_compute.design"></a><a class="link" href="design.html" title="Design">Design</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="design.html#boost_compute.design.library_architecture">Library Architecture</a></span></dt>
<dt><span class="section"><a href="design.html#boost_compute.design.why_opencl">Why OpenCL</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_compute.design.library_architecture"></a><a class="link" href="design.html#boost_compute.design.library_architecture" title="Library Architecture">Library Architecture</a>
</h3></div></div></div>
<p>
        The Boost Compute library consists of several different components. The core
        layer provides a "thin" C++ wrapper over the OpenCL API. This includes
        classes to manage OpenCL objects such as <code class="computeroutput">device</code>'s,
        <code class="computeroutput">kernel</code>'s and <code class="computeroutput">command_queue</code>'s.
      </p>
<p>
        On top of the core layer is a partial implementation of the C++ standard
        library providing common containers (e.g. <code class="computeroutput"><a class="link" href="../boost/compute/vector.html" title="Class template vector">vector&lt;T&gt;</a></code>,
        <code class="computeroutput"><a class="link" href="../boost/compute/array.html" title="Class template array">array&lt;T, N&gt;</a></code>) along
        with common algorithms (e.g. <code class="computeroutput"><a class="link" href="../boost/compute/transform.html" title="Function transform">transform()</a></code>
        and <code class="computeroutput"><a class="link" href="../boost/compute/sort.html" title="Function sort">sort()</a></code>).
      </p>
<p>
        The library also provides a number of "fancy" iterators (e.g.
        <code class="computeroutput"><a class="link" href="../boost/compute/transform_iterator.html" title="Class template transform_iterator">transform_iterator</a></code>
        and <code class="computeroutput"><a class="link" href="../boost/compute/permutation_iterator.html" title="Class template permutation_iterator">permutation_iterator</a></code>)
        which enhance the functionality of the standard algorithms.
      </p>
<p>
        Boost.Compute also supplies a number of facilities for interoperation with
        other C and C++ libraries. See the section on <a class="link" href="interop.html" title="Interoperability">interoperability</a>
        for more information.
      </p>
<p>
        See the <a class="link" href="reference.html#boost_compute.reference.api_overview" title="API Overview">API Overview</a>
        section for a full list of functions, classes, and macros provided by Boost.Compute.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_compute.design.why_opencl"></a><a class="link" href="design.html#boost_compute.design.why_opencl" title="Why OpenCL">Why OpenCL</a>
</h3></div></div></div>
<p>
        Boost.Compute uses <a href="http://en.wikipedia.org/wiki/OpenCL" target="_top">OpenCL</a>
        as its interface for executing code on parallel devices such as GPUs and
        multi-core CPUs.
      </p>
<p>
        OpenCL was chosen for a number of reasons:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Vendor-neutral, standard C/C++, and doesn't require a special compiler,
            non-standard pragmas, or compiler extensions.
          </li>
<li class="listitem">
            It is not just another parallel-library abstraction layer, it provides
            direct access to the underlying hardware.
          </li>
<li class="listitem">
            Its runtime compilation model allows for kernels to be optimized and
            tuned dynamically for the device present when the application is run
            rather that the device that was present when the code was compiled (which
            is often a separate machine).
          </li>
<li class="listitem">
            Using OpenCL allows Boost.Compute to directly interoperate with other
            OpenCL libraries (such as VexCL and OpenCV), as well as existing code
            written with OpenCL.
          </li>
<li class="listitem">
            The "thin" C++ wrapper provided by Boost.Compute allows the
            user to break-out and write their own custom kernels when the provided
            APIs are not suitable.
          </li>
</ul></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="getting_started.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="tutorial.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
