<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Porting Guide</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="prev" href="interop.html" title="Interoperability">
<link rel="next" href="platforms_and_compilers.html" title="Platforms and Compilers">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="interop.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="platforms_and_compilers.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_compute.porting_guide"></a><a class="link" href="porting_guide.html" title="Porting Guide">Porting Guide</a>
</h2></div></div></div>
<div class="toc"><dl class="toc"><dt><span class="section"><a href="porting_guide.html#boost_compute.porting_guide.opencl_api">OpenCL API</a></span></dt></dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_compute.porting_guide.opencl_api"></a><a class="link" href="porting_guide.html#boost_compute.porting_guide.opencl_api" title="OpenCL API">OpenCL API</a>
</h3></div></div></div>
<div class="table">
<a name="boost_compute.porting_guide.opencl_api.opencl_api_translation_table"></a><p class="title"><b>Table 1.1. OpenCL API Translation Table</b></p>
<div class="table-contents"><table class="table" summary="OpenCL API Translation Table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  OpenCL Function
                </p>
              </th>
<th>
                <p>
                  Boost.Compute Function
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Buffers</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateBuffer</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">buffer::buffer()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clReleaseMemObject</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">buffer::~buffer()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetMemObjectInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">buffer::get_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Command Queues</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateCommandQueue</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::command_queue()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clReleaseCommandQueue</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::~command_queue()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetCommandQueueInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::get_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueReadBuffer</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_read_buffer()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueWriteBuffer</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_write_buffer()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueCopyBuffer</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_copy_buffer()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueNDRangeKernel</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_nd_range_kernel()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueTask</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_task()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueBarrier</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_barrier()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueMarker</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_marker()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clFlush</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::flush()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clFinish</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::finish()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Contexts</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateContext</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">context::context()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clReleaseContext</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">context::~context()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetContextInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">context::get_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Devices</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetDeviceInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">device::get_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Events</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clReleaseEvent</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">event::~event()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetEventInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">event::get_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetEventProfilingInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">event::get_profiling_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clWaitForEvents</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><a class="link" href="../boost/compute/wait_list.html#idm11844-bb">wait_list::wait()</a></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateUserEvent</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">user_event::user_event()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clSetUserEventStatus</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">user_event::set_status()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Kernels</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateKernel</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">kernel::kernel()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clReleaseKernel</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">kernel::~kernel()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetKernelInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">kernel::get_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetKernelArgInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">kernel::get_arg_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetKernelWorkGroupInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">kernel::get_work_group_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clSetKernelArg</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">kernel::set_arg()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clSetKernelExecInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">kernel::set_exec_info()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Pipes</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreatePipe</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">pipe::pipe()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetPipeInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">pipe::get_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Platforms</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetDeviceIDs</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">platform::devices()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetPlatformInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">platform::get_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetExtensionFunctionAddress</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">platform::get_extension_function_address()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clUnloadCompiler</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">platform::unload_compiler()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Programs</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateProgramWithSource</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">program::create_with_source()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateProgramWithBinary</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">program::create_with_binary()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateProgramWithBuiltInKernels</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">program::create_with_builtin_kernels()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clReleaseProgram</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">program::~program()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetProgramInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">program::get_info&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clBuildProgram</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">program::build()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCompileProgram</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">program::compile()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clLinkProgram</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">program::link()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>Shared Virtual Memory</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clSVMAlloc</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">svm_alloc&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clSVMFree</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">svm_free&lt;T&gt;()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueSVMFree</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_svm_free()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueSVMMemcpy</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_svm_memcpy()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueSVMMemFill</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_svm_fill()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueSVMMap</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_svm_map()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueSVMUnmap</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">command_queue::enqueue_svm_unmap()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>System</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetPlatformIDs</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput">system::platforms()</code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong>OpenGL Sharing</strong></span>
                </p>
              </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateFromGLBuffer</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><a class="link" href="../boost/compute/opengl_buffer.html" title="Class opengl_buffer">opengl_buffer::opengl_buffer()</a></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateFromGLRenderbuffer</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><a class="link" href="../boost/compute/opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer::opengl_renderbuffer()</a></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clCreateFromGLTexture</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><a class="link" href="../boost/compute/opengl_texture.html" title="Class opengl_texture">opengl_texture::opengl_texture()</a></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clGetGLTextureInfo</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><a class="link" href="../boost/compute/opengl_texture.html" title="Class opengl_texture">opengl_texture::get_texture_info&lt;T&gt;()</a></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueAcquireGLObjects</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><a class="link" href="../boost/compute/opengl_enqueue_acq_idm9416.html" title="Function opengl_enqueue_acquire_gl_objects">opengl_enqueue_acquire_gl_objects()</a></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">clEnqueueReleaseGLObjects</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><a class="link" href="../boost/compute/opengl_enqueue_rel_idm9433.html" title="Function opengl_enqueue_release_gl_objects">opengl_enqueue_release_gl_objects()</a></code>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="interop.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="platforms_and_compilers.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
