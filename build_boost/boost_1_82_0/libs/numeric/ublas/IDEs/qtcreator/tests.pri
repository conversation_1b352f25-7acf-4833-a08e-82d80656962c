SUBDIRS += \
    begin_end \
    comp_mat_erase \
    concepts \
    num_columns \
    num_rows \
    placement_new \
    size \
    sparse_view_test \
    test1 \
    test2 \
    test3 \
    test3_coo \
    test3_mvov \
    test4 \
    test5 \
    test6 \
    test7 \
    test_assignment \
    test_banded_storage_layout \
    test_complex_norms \
    test_coordinate_matrix_inplace_merge \
    test_coordinate_matrix_sort \
    test_coordinate_matrix_always_do_full_sort \
    test_coordinate_vector_inplace_merge \
    test_fixed_containers \
    test_inplace_solve_basic \
    test_inplace_solve_sparse \
    test_inplace_solve_mvov \
    test_lu \
    test_matrix_vector \
    test_ticket7296 \
    test_triangular \
    triangular_access \
    triangular_layout \
    test_tensor

begin_end.file = test/begin_end.pro
comp_mat_erase.file = test/comp_mat_erase.pro
concepts.file = test/concepts.pro
num_columns.file = test/num_columns.pro
num_rows.file = test/num_rows.pro
placement_new.file = test/placement_new.pro
size.file = test/size.pro
sparse_view_test.file = test/sparse_view_test.pro
test1.file = test/test1.pro
test2.file = test/test2.pro
test3.file = test/test3.pro
test3_coo.file = test/test3_coo.pro
test3_mvov.file = test/test3_mvov.pro
test4.file = test/test4.pro
test5.file = test/test5.pro
test6.file = test/test6.pro
test7.file = test/test7.pro
test_assignment.file = test/test_assignment.pro
test_banded_storage_layout.file = test/test_banded_storage_layout.pro
test_complex_norms.file = test/test_complex_norms.pro
test_coordinate_matrix_inplace_merge.file = test/test_coordinate_matrix_inplace_merge.pro
test_coordinate_matrix_sort.file = test/test_coordinate_matrix_sort.pro
test_coordinate_matrix_always_do_full_sort.file = test/test_coordinate_matrix_always_do_full_sort.pro
test_coordinate_vector_inplace_merge.file = test/test_coordinate_vector_inplace_merge.pro
test_fixed_containers.file = test/test_fixed_containers.pro
test_inplace_solve_basic.file = test/test_inplace_solve_basic.pro
test_inplace_solve_sparse.file = test/test_inplace_solve_sparse.pro
test_inplace_solve_mvov.file = test/test_inplace_solve_mvov.pro
test_lu.file = test/test_lu.pro
test_matrix_vector.file = test/test_matrix_vector.pro
test_ticket7296.file = test/test_ticket7296.pro
test_triangular.file = test/test_triangular.pro
triangular_access.file = test/triangular_access.pro
triangular_layout.file = test/triangular_layout.pro
test_tensor.file = test/test_tensor.pro
