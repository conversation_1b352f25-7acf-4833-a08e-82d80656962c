# Copyright 2012 <PERSON><PERSON>
# Copyright 2013 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

# bring in rules for testing


import testing ;

use-project boost : $(BOOST_ROOT) ;
VEXCL_INCLUDE = /home/<USER>/boost/testing/vexcl ;
OPENCL_INCLUDE = /usr/local/cuda/include ;
#OPENCL_INCLUDE = /usr/include ;

project
    : requirements
      <library>/boost/test//boost_unit_test_framework
      <define>BOOST_ALL_NO_LIB=1
      <include>$(VEXCL_INCLUDE)
      <include>$(OPENCL_INCLUDE)
      <cxxflags>-std=c++0x
      <library>/boost//system/
    ;

lib OpenCL : : <name>OpenCL <link>shared ;

test-suite "odeint"
    :
    [ run lorenz.cpp OpenCL ]
    [ run norm_inf.cpp OpenCL ]
    : <testing.launcher>valgrind
    : 
    : <link>shared:<define>BOOST_TEST_DYN_LINK=1
    ;