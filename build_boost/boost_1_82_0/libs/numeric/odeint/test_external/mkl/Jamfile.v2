# Copyright 2012 <PERSON><PERSON>
# Copyright 2012 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

# bring in rules for testing

import testing ;
use-project boost : $(BOOST_ROOT) ;

project
    : requirements
      <library>/boost/test//boost_unit_test_framework
    ;
    
    
lib libmkl : : <name>mkl_intel_lp64 <link>shared ;
lib libmkl_core : : <name>mkl_core <link>shared ;
lib libmkl_intel_thread : : <name>mkl_intel_thread ;
lib libiomp5 : : <name>iomp5 ;
lib libpthread : : <name>pthread ;

test-suite "mkl"
    : 
      [ run check_mkl.cpp  libpthread libiomp5 libmkl_core libmkl_intel_thread libmkl
      :
      :
      : <link>shared:<define>BOOST_TEST_DYN_LINK=1
      ] 
    ;
