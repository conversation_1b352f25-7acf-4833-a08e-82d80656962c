#==============================================================================
#         Copyright 2014          LRI    UMR 8623 CNRS/Univ Paris Sud XI
#         Copyright 2014          NumScale SAS
#
#          Distributed under the Boost Software License, Version 1.0.
#                 See accompanying file LICENSE.txt or copy at
#                     http://www.boost.org/LICENSE_1_0.txt
#==============================================================================

import testing ;
import os ;

# This must be built using an NT2 installation.
# NT2_ROOT_PATH should point to the build directory.
# Currently, cxxflags needs to be set to the required architecture
# if using avx/avx2, set the environemnt variable NT2_SIMD_FLAGS to the
# required value for your compiler (i.e. -mavx2 on g++)
# If using sse2/3/4 in 64 bits, this is set automatically.

local NT2_ROOT_PATH = [ os.environ NT2_ROOT_PATH ] ;
local NT2_SIMD_FLAGS = [ os.environ NT2_SIMD_FLAGS ] ;

use-project boost : $(BOOST_ROOT) ;

project
  : requirements
    <library>$(BOOST_ROOT)/boost/test/included/unit_test_framework.hpp
    <define>BOOST_ALL_NO_LIB=1
    <include>$(NT2_ROOT_PATH)/include/
    <link>static
    <toolset>gcc:<cxxflags>-DBOOST_SIMD_NO_STRICT_ALIASING
    <toolset>gcc:<cxxflags>-fno-strict-aliasing
    <cxxflags>$(NT2_SIMD_FLAGS)
  ;

test-suite "odeint"
  :
    [ run copy.cpp ]
    [ run norm_inf.cpp ]
    [ run resize.cpp ]
    [ run is_resizeable.cpp ]
    [ run algebra_dispatcher.cpp ]
    : <testing.launcher>valgrind
  ;
