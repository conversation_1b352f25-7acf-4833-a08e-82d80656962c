<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function template make_times_iterator_end</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Numeric.Odeint">
<link rel="up" href="../../../header/boost/numeric/odeint/iterator/times_iterator_hpp.html" title="Header &lt;boost/numeric/odeint/iterator/times_iterator.hpp&gt;">
<link rel="prev" href="make_times_iterator_begin.html" title="Function template make_times_iterator_begin">
<link rel="next" href="make_times_range.html" title="Function template make_times_range">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../logo.jpg"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="make_times_iterator_begin.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/iterator/times_iterator_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="make_times_range.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.numeric.odeint.make_times_iterator_end"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function template make_times_iterator_end</span></h2>
<p>boost::numeric::odeint::make_times_iterator_end — Factory function for <a class="link" href="times_iterator.html" title="Class template times_iterator">times_iterator</a>. Constructs an end iterator. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/numeric/odeint/iterator/times_iterator_hpp.html" title="Header &lt;boost/numeric/odeint/iterator/times_iterator.hpp&gt;">boost/numeric/odeint/iterator/times_iterator.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> TimeIterator<span class="special">,</span> <span class="keyword">typename</span> Stepper<span class="special">,</span> <span class="keyword">typename</span> System<span class="special">,</span> 
         <span class="keyword">typename</span> State<span class="special">&gt;</span> 
  <a class="link" href="times_iterator.html" title="Class template times_iterator">times_iterator</a><span class="special">&lt;</span> <span class="identifier">Stepper</span><span class="special">,</span> <span class="identifier">System</span><span class="special">,</span> <span class="identifier">State</span><span class="special">,</span> <span class="identifier">TimeIterator</span> <span class="special">&gt;</span> 
  <span class="identifier">make_times_iterator_end</span><span class="special">(</span><span class="identifier">Stepper</span> stepper<span class="special">,</span> <span class="identifier">System</span> system<span class="special">,</span> <span class="identifier">State</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm29499"></a><h2>Description</h2>
<p>


This function needs the TimeIterator type specifically defined as a template parameter. </p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">stepper</code></span></p></td>
<td><p>The stepper to use during the iteration. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">system</code></span></p></td>
<td><p>The system function (ODE) to solve. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">x</code></span></p></td>
<td><p>The initial state. <code class="computeroutput"><a class="link" href="const_step_iterator.html" title="Class template const_step_iterator">const_step_iterator</a></code> stores a reference of s and changes its value during the iteration. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The times iterator.</p></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2015 Karsten Ahnert and Mario Mulansky<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="make_times_iterator_begin.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/iterator/times_iterator_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="make_times_range.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
