<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template symplectic_euler</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Numeric.Odeint">
<link rel="up" href="../../../header/boost/numeric/odeint/stepper/symplectic_euler_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/symplectic_euler.hpp&gt;">
<link rel="prev" href="../../../header/boost/numeric/odeint/stepper/symplectic_euler_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/symplectic_euler.hpp&gt;">
<link rel="next" href="../../../header/boost/numeric/odeint/stepper/symplectic_rkn_sb3a_m4_mclachlan_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/symplectic_rkn_sb3a_m4_mclachlan.hpp&gt;">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../logo.jpg"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../../header/boost/numeric/odeint/stepper/symplectic_euler_hpp.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/symplectic_euler_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/stepper/symplectic_rkn_sb3a_m4_mclachlan_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.numeric.odeint.symplectic_euler"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template symplectic_euler</span></h2>
<p>boost::numeric::odeint::symplectic_euler — Implementation of the symplectic Euler method. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/numeric/odeint/stepper/symplectic_euler_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/symplectic_euler.hpp&gt;">boost/numeric/odeint/stepper/symplectic_euler.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Coor<span class="special">,</span> <span class="keyword">typename</span> Momentum <span class="special">=</span> <span class="identifier">Coor</span><span class="special">,</span> <span class="keyword">typename</span> Value <span class="special">=</span> <span class="keyword">double</span><span class="special">,</span> 
         <span class="keyword">typename</span> CoorDeriv <span class="special">=</span> <span class="identifier">Coor</span><span class="special">,</span> <span class="keyword">typename</span> MomentumDeriv <span class="special">=</span> <span class="identifier">Coor</span><span class="special">,</span> 
         <span class="keyword">typename</span> Time <span class="special">=</span> <span class="identifier">Value</span><span class="special">,</span> 
         <span class="keyword">typename</span> Algebra <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">algebra_dispatcher</span><span class="special">&lt;</span> <span class="identifier">Coor</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">algebra_type</span><span class="special">,</span> 
         <span class="keyword">typename</span> Operations <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">operations_dispatcher</span><span class="special">&lt;</span> <span class="identifier">Coor</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">operations_type</span><span class="special">,</span> 
         <span class="keyword">typename</span> Resizer <span class="special">=</span> <span class="identifier">initially_resizer</span><span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="symplectic_euler.html" title="Class template symplectic_euler">symplectic_euler</a> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">symplectic_nystroem_stepper_base</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">stepper_base_type</span><span class="special">::</span><span class="identifier">algebra_type</span> <a name="boost.numeric.odeint.symplectic_euler.algebra_type"></a><span class="identifier">algebra_type</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="identifier">stepper_base_type</span><span class="special">::</span><span class="identifier">value_type</span>   <a name="boost.numeric.odeint.symplectic_euler.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>  

  <span class="comment">// <a class="link" href="symplectic_euler.html#boost.numeric.odeint.symplectic_eulerconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="symplectic_euler.html#idm30409-bb"><span class="identifier">symplectic_euler</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">algebra_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">algebra_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm51734"></a><h2>Description</h2>
<p>The method is of first order and has one stage. It is described HERE.</p>
<p>
</p>
<div class="refsect2">
<a name="idm51738"></a><h3>Template Parameters</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Coor</pre>
<p>The type representing the coordinates q. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Momentum <span class="special">=</span> <span class="identifier">Coor</span></pre>
<p>The type representing the coordinates p. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Value <span class="special">=</span> <span class="keyword">double</span></pre>
<p>The basic value type. Should be something like float, double or a high-precision type. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> CoorDeriv <span class="special">=</span> <span class="identifier">Coor</span></pre>
<p>The type representing the time derivative of the coordinate dq/dt. </p>
</li>
<li class="listitem"><pre class="literallayout"><span class="keyword">typename</span> MomentumDeriv <span class="special">=</span> <span class="identifier">Coor</span></pre></li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Time <span class="special">=</span> <span class="identifier">Value</span></pre>
<p>The type representing the time t. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Algebra <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">algebra_dispatcher</span><span class="special">&lt;</span> <span class="identifier">Coor</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">algebra_type</span></pre>
<p>The algebra. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Operations <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">operations_dispatcher</span><span class="special">&lt;</span> <span class="identifier">Coor</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">operations_type</span></pre>
<p>The operations. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Resizer <span class="special">=</span> <span class="identifier">initially_resizer</span></pre>
<p>The resizer policy. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm51821"></a><h3>
<a name="boost.numeric.odeint.symplectic_eulerconstruct-copy-destruct"></a><code class="computeroutput">symplectic_euler</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">
<pre class="literallayout"><a name="idm30409-bb"></a><span class="identifier">symplectic_euler</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">algebra_type</span> <span class="special">&amp;</span> algebra <span class="special">=</span> <span class="identifier">algebra_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Constructs the <code class="computeroutput"><a class="link" href="symplectic_euler.html" title="Class template symplectic_euler">symplectic_euler</a></code>. This constructor can be used as a default constructor if the algebra has a default constructor. <p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">algebra</code></span></p></td>
<td><p>A copy of algebra is made and stored inside explicit_stepper_base. </p></td>
</tr></tbody>
</table></div></td>
</tr></tbody>
</table></div>
</li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2015 Karsten Ahnert and Mario Mulansky<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../../header/boost/numeric/odeint/stepper/symplectic_euler_hpp.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/symplectic_euler_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/stepper/symplectic_rkn_sb3a_m4_mclachlan_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
