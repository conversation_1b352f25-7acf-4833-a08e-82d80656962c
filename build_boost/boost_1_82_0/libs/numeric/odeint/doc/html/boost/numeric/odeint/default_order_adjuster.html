<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template default_order_adjuster</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Numeric.Odeint">
<link rel="up" href="../../../header/boost/numeric/odeint/stepper/controlled_adams_bashforth_moulton_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/controlled_adams_bashforth_moulton.hpp&gt;">
<link rel="prev" href="controlled_adams__idm26622.html" title="Class template controlled_adams_bashforth_moulton">
<link rel="next" href="../../../header/boost/numeric/odeint/stepper/controlled_runge_kutta_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/controlled_runge_kutta.hpp&gt;">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../logo.jpg"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="controlled_adams__idm26622.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/controlled_adams_bashforth_moulton_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/stepper/controlled_runge_kutta_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.numeric.odeint.default_order_adjuster"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template default_order_adjuster</span></h2>
<p>boost::numeric::odeint::default_order_adjuster</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/numeric/odeint/stepper/controlled_adams_bashforth_moulton_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/controlled_adams_bashforth_moulton.hpp&gt;">boost/numeric/odeint/stepper/controlled_adams_bashforth_moulton.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">size_t</span> MaxOrder<span class="special">,</span> <span class="keyword">typename</span> State<span class="special">,</span> <span class="keyword">typename</span> Value <span class="special">=</span> <span class="keyword">double</span><span class="special">,</span> 
         <span class="keyword">typename</span> Algebra <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">algebra_dispatcher</span><span class="special">&lt;</span> <span class="identifier">State</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">algebra_type</span><span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="default_order_adjuster.html" title="Class template default_order_adjuster">default_order_adjuster</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">State</span>                       <a name="boost.numeric.odeint.default_order_adjuster.state_type"></a><span class="identifier">state_type</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">Value</span>                       <a name="boost.numeric.odeint.default_order_adjuster.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">state_wrapper</span><span class="special">&lt;</span> <span class="identifier">state_type</span> <span class="special">&gt;</span> <a name="boost.numeric.odeint.default_order_adjuster.wrapped_state_type"></a><span class="identifier">wrapped_state_type</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="identifier">Algebra</span>                     <a name="boost.numeric.odeint.default_order_adjuster.algebra_type"></a><span class="identifier">algebra_type</span><span class="special">;</span>      

  <span class="comment">// <a class="link" href="default_order_adjuster.html#boost.numeric.odeint.default_order_adjusterconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="default_order_adjuster.html#idm26785-bb"><span class="identifier">default_order_adjuster</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">algebra_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">algebra_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="default_order_adjuster.html#idm26776-bb">public member functions</a></span>
  <span class="identifier">size_t</span> <a class="link" href="default_order_adjuster.html#idm26777-bb"><span class="identifier">adjust_order</span></a><span class="special">(</span><span class="identifier">size_t</span><span class="special">,</span> <span class="identifier">size_t</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">array</span><span class="special">&lt;</span> <span class="identifier">wrapped_state_type</span><span class="special">,</span> <span class="number">4</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm37453"></a><h2>Description</h2>
<div class="refsect2">
<a name="idm37455"></a><h3>
<a name="boost.numeric.odeint.default_order_adjusterconstruct-copy-destruct"></a><code class="computeroutput">default_order_adjuster</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><pre class="literallayout"><a name="idm26785-bb"></a><span class="identifier">default_order_adjuster</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">algebra_type</span> <span class="special">&amp;</span> algebra <span class="special">=</span> <span class="identifier">algebra_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre></li></ol></div>
</div>
<div class="refsect2">
<a name="idm37475"></a><h3>
<a name="idm26776-bb"></a><code class="computeroutput">default_order_adjuster</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><pre class="literallayout"><span class="identifier">size_t</span> <a name="idm26777-bb"></a><span class="identifier">adjust_order</span><span class="special">(</span><span class="identifier">size_t</span> order<span class="special">,</span> <span class="identifier">size_t</span> init<span class="special">,</span> 
                    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">array</span><span class="special">&lt;</span> <span class="identifier">wrapped_state_type</span><span class="special">,</span> <span class="number">4</span> <span class="special">&gt;</span> <span class="special">&amp;</span> xerr<span class="special">)</span><span class="special">;</span></pre></li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2015 Karsten Ahnert and Mario Mulansky<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="controlled_adams__idm26622.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/controlled_adams_bashforth_moulton_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/stepper/controlled_runge_kutta_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
