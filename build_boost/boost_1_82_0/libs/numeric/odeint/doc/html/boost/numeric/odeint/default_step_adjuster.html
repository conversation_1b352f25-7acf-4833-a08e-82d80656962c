<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template default_step_adjuster</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Numeric.Odeint">
<link rel="up" href="../../../header/boost/numeric/odeint/stepper/controlled_runge_kutta_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/controlled_runge_kutta.hpp&gt;">
<link rel="prev" href="default_error_checker.html" title="Class template default_error_checker">
<link rel="next" href="../../../header/boost/numeric/odeint/stepper/controlled_step_result_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/controlled_step_result.hpp&gt;">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../logo.jpg"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="default_error_checker.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/controlled_runge_kutta_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/stepper/controlled_step_result_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.numeric.odeint.default_step_adjuster"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template default_step_adjuster</span></h2>
<p>boost::numeric::odeint::default_step_adjuster</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/numeric/odeint/stepper/controlled_runge_kutta_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/controlled_runge_kutta.hpp&gt;">boost/numeric/odeint/stepper/controlled_runge_kutta.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Value<span class="special">,</span> <span class="keyword">typename</span> Time<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="default_step_adjuster.html" title="Class template default_step_adjuster">default_step_adjuster</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">Time</span>  <a name="boost.numeric.odeint.default_step_adjuster.time_type"></a><span class="identifier">time_type</span><span class="special">;</span> 
  <span class="keyword">typedef</span> <span class="identifier">Value</span> <a name="boost.numeric.odeint.default_step_adjuster.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="default_step_adjuster.html#boost.numeric.odeint.default_step_adjusterconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="default_step_adjuster.html#idm27515-bb"><span class="identifier">default_step_adjuster</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">time_type</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span> <span class="identifier">time_type</span> <span class="special">&gt;</span><span class="special">(</span><span class="number">0</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="default_step_adjuster.html#idm27492-bb">public member functions</a></span>
  <span class="identifier">time_type</span> <a class="link" href="default_step_adjuster.html#idm27493-bb"><span class="identifier">decrease_step</span></a><span class="special">(</span><span class="identifier">time_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">int</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">time_type</span> <a class="link" href="default_step_adjuster.html#idm27501-bb"><span class="identifier">increase_step</span></a><span class="special">(</span><span class="identifier">time_type</span><span class="special">,</span> <span class="identifier">value_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">int</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="default_step_adjuster.html#idm27509-bb"><span class="identifier">check_step_size_limit</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">time_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">time_type</span> <a class="link" href="default_step_adjuster.html#idm27513-bb"><span class="identifier">get_max_dt</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm40217"></a><h2>Description</h2>
<div class="refsect2">
<a name="idm40219"></a><h3>
<a name="boost.numeric.odeint.default_step_adjusterconstruct-copy-destruct"></a><code class="computeroutput">default_step_adjuster</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><pre class="literallayout"><a name="idm27515-bb"></a><span class="identifier">default_step_adjuster</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">time_type</span> max_dt <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span> <span class="identifier">time_type</span> <span class="special">&gt;</span><span class="special">(</span><span class="number">0</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre></li></ol></div>
</div>
<div class="refsect2">
<a name="idm40242"></a><h3>
<a name="idm27492-bb"></a><code class="computeroutput">default_step_adjuster</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><span class="identifier">time_type</span> <a name="idm27493-bb"></a><span class="identifier">decrease_step</span><span class="special">(</span><span class="identifier">time_type</span> dt<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_type</span> error<span class="special">,</span> 
                        <span class="keyword">const</span> <span class="keyword">int</span> error_order<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">time_type</span> <a name="idm27501-bb"></a><span class="identifier">increase_step</span><span class="special">(</span><span class="identifier">time_type</span> dt<span class="special">,</span> <span class="identifier">value_type</span> error<span class="special">,</span> 
                        <span class="keyword">const</span> <span class="keyword">int</span> stepper_order<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">bool</span> <a name="idm27509-bb"></a><span class="identifier">check_step_size_limit</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">time_type</span> dt<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">time_type</span> <a name="idm27513-bb"></a><span class="identifier">get_max_dt</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2015 Karsten Ahnert and Mario Mulansky<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="default_error_checker.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/controlled_runge_kutta_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/stepper/controlled_step_result_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
