<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template runge_kutta_dopri5</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Numeric.Odeint">
<link rel="up" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_dopri5_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/runge_kutta_dopri5.hpp&gt;">
<link rel="prev" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_dopri5_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/runge_kutta_dopri5.hpp&gt;">
<link rel="next" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_fehlberg78_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/runge_kutta_fehlberg78.hpp&gt;">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../logo.jpg"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_dopri5_hpp.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_dopri5_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_fehlberg78_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.numeric.odeint.runge_kutta_dopri5"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template runge_kutta_dopri5</span></h2>
<p>boost::numeric::odeint::runge_kutta_dopri5 — The Runge-Kutta Dormand-Prince 5 method. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_dopri5_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/runge_kutta_dopri5.hpp&gt;">boost/numeric/odeint/stepper/runge_kutta_dopri5.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> State<span class="special">,</span> <span class="keyword">typename</span> Value <span class="special">=</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">typename</span> Deriv <span class="special">=</span> <span class="identifier">State</span><span class="special">,</span> 
         <span class="keyword">typename</span> Time <span class="special">=</span> <span class="identifier">Value</span><span class="special">,</span> 
         <span class="keyword">typename</span> Algebra <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">algebra_dispatcher</span><span class="special">&lt;</span> <span class="identifier">State</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">algebra_type</span><span class="special">,</span> 
         <span class="keyword">typename</span> Operations <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">operations_dispatcher</span><span class="special">&lt;</span> <span class="identifier">State</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">operations_type</span><span class="special">,</span> 
         <span class="keyword">typename</span> Resizer <span class="special">=</span> <span class="identifier">initially_resizer</span><span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="runge_kutta_dopri5.html" title="Class template runge_kutta_dopri5">runge_kutta_dopri5</a> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">explicit_error_stepper_fsal_base</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">explicit_error_stepper_fsal_base</span><span class="special">&lt;</span> <a class="link" href="runge_kutta_dopri5.html" title="Class template runge_kutta_dopri5">runge_kutta_dopri5</a><span class="special">&lt;</span> <span class="special">...</span> <span class="special">&gt;</span><span class="special">,</span><span class="special">...</span> <span class="special">&gt;</span> <a name="boost.numeric.odeint.runge_kutta_dopri5.stepper_base_type"></a><span class="identifier">stepper_base_type</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="identifier">stepper_base_type</span><span class="special">::</span><span class="identifier">state_type</span>                                     <a name="boost.numeric.odeint.runge_kutta_dopri5.state_type"></a><span class="identifier">state_type</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">stepper_base_type</span><span class="special">::</span><span class="identifier">value_type</span>                                     <a name="boost.numeric.odeint.runge_kutta_dopri5.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">stepper_base_type</span><span class="special">::</span><span class="identifier">deriv_type</span>                                     <a name="boost.numeric.odeint.runge_kutta_dopri5.deriv_type"></a><span class="identifier">deriv_type</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">stepper_base_type</span><span class="special">::</span><span class="identifier">time_type</span>                                      <a name="boost.numeric.odeint.runge_kutta_dopri5.time_type"></a><span class="identifier">time_type</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">stepper_base_type</span><span class="special">::</span><span class="identifier">algebra_type</span>                                   <a name="boost.numeric.odeint.runge_kutta_dopri5.algebra_type"></a><span class="identifier">algebra_type</span><span class="special">;</span>     
  <span class="keyword">typedef</span> <span class="identifier">stepper_base_type</span><span class="special">::</span><span class="identifier">operations_type</span>                                <a name="boost.numeric.odeint.runge_kutta_dopri5.operations_type"></a><span class="identifier">operations_type</span><span class="special">;</span>  
  <span class="keyword">typedef</span> <span class="identifier">stepper_base_type</span><span class="special">::</span><span class="identifier">resizer_type</span>                                   <a name="boost.numeric.odeint.runge_kutta_dopri5.resizer_type"></a><span class="identifier">resizer_type</span><span class="special">;</span>     

  <span class="comment">// <a class="link" href="runge_kutta_dopri5.html#boost.numeric.odeint.runge_kutta_dopri5construct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="runge_kutta_dopri5.html#idm30107-bb"><span class="identifier">runge_kutta_dopri5</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">algebra_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">algebra_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="runge_kutta_dopri5.html#idm29964-bb">public member functions</a></span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> System<span class="special">,</span> <span class="keyword">typename</span> StateIn<span class="special">,</span> <span class="keyword">typename</span> DerivIn<span class="special">,</span> 
           <span class="keyword">typename</span> StateOut<span class="special">,</span> <span class="keyword">typename</span> DerivOut<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="runge_kutta_dopri5.html#idm29965-bb"><span class="identifier">do_step_impl</span></a><span class="special">(</span><span class="identifier">System</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">DerivIn</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">time_type</span><span class="special">,</span> 
                      <span class="identifier">StateOut</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">DerivOut</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">time_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> System<span class="special">,</span> <span class="keyword">typename</span> StateIn<span class="special">,</span> <span class="keyword">typename</span> DerivIn<span class="special">,</span> 
           <span class="keyword">typename</span> StateOut<span class="special">,</span> <span class="keyword">typename</span> DerivOut<span class="special">,</span> <span class="keyword">typename</span> Err<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="runge_kutta_dopri5.html#idm30012-bb"><span class="identifier">do_step_impl</span></a><span class="special">(</span><span class="identifier">System</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">DerivIn</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">time_type</span><span class="special">,</span> 
                      <span class="identifier">StateOut</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">DerivOut</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">time_type</span><span class="special">,</span> <span class="identifier">Err</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StateOut<span class="special">,</span> <span class="keyword">typename</span> StateIn1<span class="special">,</span> <span class="keyword">typename</span> DerivIn1<span class="special">,</span> 
           <span class="keyword">typename</span> StateIn2<span class="special">,</span> <span class="keyword">typename</span> DerivIn2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="runge_kutta_dopri5.html#idm30064-bb"><span class="identifier">calc_state</span></a><span class="special">(</span><span class="identifier">time_type</span><span class="special">,</span> <span class="identifier">StateOut</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">StateIn1</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">DerivIn1</span> <span class="special">&amp;</span><span class="special">,</span> 
                    <span class="identifier">time_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">StateIn2</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">DerivIn2</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">time_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StateIn<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="runge_kutta_dopri5.html#idm30096-bb"><span class="identifier">adjust_size</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="runge_kutta_dopri5.html#idm30117-bb">private member functions</a></span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StateIn<span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="runge_kutta_dopri5.html#idm30118-bb"><span class="identifier">resize_k_x_tmp_impl</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StateIn<span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="runge_kutta_dopri5.html#idm30124-bb"><span class="identifier">resize_dxdt_tmp_impl</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm50068"></a><h2>Description</h2>
<p>The Runge-Kutta Dormand-Prince 5 method is a very popular method for solving ODEs, see <a href="../../../" target="_top"></a>. The method is explicit and fulfills the Error Stepper concept. Step size control is provided but continuous output is available which make this method favourable for many applications.</p>
<p>This class derives from explicit_error_stepper_fsal_base and inherits its interface via CRTP (current recurring template pattern). The method possesses the FSAL (first-same-as-last) property. See explicit_error_stepper_fsal_base for more details.</p>
<p>
</p>
<div class="refsect2">
<a name="idm50074"></a><h3>Template Parameters</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> State</pre>
<p>The state type. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Value <span class="special">=</span> <span class="keyword">double</span></pre>
<p>The value type. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Deriv <span class="special">=</span> <span class="identifier">State</span></pre>
<p>The type representing the time derivative of the state. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Time <span class="special">=</span> <span class="identifier">Value</span></pre>
<p>The time representing the independent variable - the time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Algebra <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">algebra_dispatcher</span><span class="special">&lt;</span> <span class="identifier">State</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">algebra_type</span></pre>
<p>The algebra type. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Operations <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">operations_dispatcher</span><span class="special">&lt;</span> <span class="identifier">State</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">operations_type</span></pre>
<p>The operations type. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Resizer <span class="special">=</span> <span class="identifier">initially_resizer</span></pre>
<p>The resizer policy type. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm50143"></a><h3>
<a name="boost.numeric.odeint.runge_kutta_dopri5construct-copy-destruct"></a><code class="computeroutput">runge_kutta_dopri5</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">
<pre class="literallayout"><a name="idm30107-bb"></a><span class="identifier">runge_kutta_dopri5</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">algebra_type</span> <span class="special">&amp;</span> algebra <span class="special">=</span> <span class="identifier">algebra_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Constructs the <code class="computeroutput"><a class="link" href="runge_kutta_dopri5.html" title="Class template runge_kutta_dopri5">runge_kutta_dopri5</a></code> class. This constructor can be used as a default constructor if the algebra has a default constructor. <p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">algebra</code></span></p></td>
<td><p>A copy of algebra is made and stored inside explicit_stepper_base. </p></td>
</tr></tbody>
</table></div></td>
</tr></tbody>
</table></div>
</li></ol></div>
</div>
<div class="refsect2">
<a name="idm50176"></a><h3>
<a name="idm29964-bb"></a><code class="computeroutput">runge_kutta_dopri5</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> System<span class="special">,</span> <span class="keyword">typename</span> StateIn<span class="special">,</span> <span class="keyword">typename</span> DerivIn<span class="special">,</span> 
         <span class="keyword">typename</span> StateOut<span class="special">,</span> <span class="keyword">typename</span> DerivOut<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm29965-bb"></a><span class="identifier">do_step_impl</span><span class="special">(</span><span class="identifier">System</span> system<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span> in<span class="special">,</span> 
                    <span class="keyword">const</span> <span class="identifier">DerivIn</span> <span class="special">&amp;</span> dxdt_in<span class="special">,</span> <span class="identifier">time_type</span> t<span class="special">,</span> <span class="identifier">StateOut</span> <span class="special">&amp;</span> out<span class="special">,</span> 
                    <span class="identifier">DerivOut</span> <span class="special">&amp;</span> dxdt_out<span class="special">,</span> <span class="identifier">time_type</span> dt<span class="special">)</span><span class="special">;</span></pre>This method performs one step. The derivative <code class="computeroutput">dxdt_in</code> of <code class="computeroutput">in</code> at the time <code class="computeroutput">t</code> is passed to the method. The result is updated out-of-place, hence the input is in <code class="computeroutput">in</code> and the output in <code class="computeroutput">out</code>. Furthermore, the derivative is update out-of-place, hence the input is assumed to be in <code class="computeroutput">dxdt_in</code> and the output in <code class="computeroutput">dxdt_out</code>. Access to this step functionality is provided by explicit_error_stepper_fsal_base and <code class="computeroutput">do_step_impl</code> should not be called directly. <p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">dt</code></span></p></td>
<td><p>The step size. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">dxdt_in</code></span></p></td>
<td><p>The derivative of x at t. dxdt_in is not modified by this method </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">dxdt_out</code></span></p></td>
<td><p>The result of the new derivative at time t+dt. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">in</code></span></p></td>
<td><p>The state of the ODE which should be solved. in is not modified in this method </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">out</code></span></p></td>
<td><p>The result of the step is written in out. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">system</code></span></p></td>
<td><p>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">t</code></span></p></td>
<td><p>The value of the time, at which the step should be performed. </p></td>
</tr>
</tbody>
</table></div></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> System<span class="special">,</span> <span class="keyword">typename</span> StateIn<span class="special">,</span> <span class="keyword">typename</span> DerivIn<span class="special">,</span> 
         <span class="keyword">typename</span> StateOut<span class="special">,</span> <span class="keyword">typename</span> DerivOut<span class="special">,</span> <span class="keyword">typename</span> Err<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm30012-bb"></a><span class="identifier">do_step_impl</span><span class="special">(</span><span class="identifier">System</span> system<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span> in<span class="special">,</span> 
                    <span class="keyword">const</span> <span class="identifier">DerivIn</span> <span class="special">&amp;</span> dxdt_in<span class="special">,</span> <span class="identifier">time_type</span> t<span class="special">,</span> <span class="identifier">StateOut</span> <span class="special">&amp;</span> out<span class="special">,</span> 
                    <span class="identifier">DerivOut</span> <span class="special">&amp;</span> dxdt_out<span class="special">,</span> <span class="identifier">time_type</span> dt<span class="special">,</span> <span class="identifier">Err</span> <span class="special">&amp;</span> xerr<span class="special">)</span><span class="special">;</span></pre>This method performs one step. The derivative <code class="computeroutput">dxdt_in</code> of <code class="computeroutput">in</code> at the time <code class="computeroutput">t</code> is passed to the method. The result is updated out-of-place, hence the input is in <code class="computeroutput">in</code> and the output in <code class="computeroutput">out</code>. Furthermore, the derivative is update out-of-place, hence the input is assumed to be in <code class="computeroutput">dxdt_in</code> and the output in <code class="computeroutput">dxdt_out</code>. Access to this step functionality is provided by explicit_error_stepper_fsal_base and <code class="computeroutput">do_step_impl</code> should not be called directly. An estimation of the error is calculated. <p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">dt</code></span></p></td>
<td><p>The step size. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">dxdt_in</code></span></p></td>
<td><p>The derivative of x at t. dxdt_in is not modified by this method </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">dxdt_out</code></span></p></td>
<td><p>The result of the new derivative at time t+dt. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">in</code></span></p></td>
<td><p>The state of the ODE which should be solved. in is not modified in this method </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">out</code></span></p></td>
<td><p>The result of the step is written in out. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">system</code></span></p></td>
<td><p>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">t</code></span></p></td>
<td><p>The value of the time, at which the step should be performed. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">xerr</code></span></p></td>
<td><p>An estimation of the error. </p></td>
</tr>
</tbody>
</table></div></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StateOut<span class="special">,</span> <span class="keyword">typename</span> StateIn1<span class="special">,</span> <span class="keyword">typename</span> DerivIn1<span class="special">,</span> 
         <span class="keyword">typename</span> StateIn2<span class="special">,</span> <span class="keyword">typename</span> DerivIn2<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm30064-bb"></a><span class="identifier">calc_state</span><span class="special">(</span><span class="identifier">time_type</span> t<span class="special">,</span> <span class="identifier">StateOut</span> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">StateIn1</span> <span class="special">&amp;</span> x_old<span class="special">,</span> 
                  <span class="keyword">const</span> <span class="identifier">DerivIn1</span> <span class="special">&amp;</span> deriv_old<span class="special">,</span> <span class="identifier">time_type</span> t_old<span class="special">,</span> 
                  <span class="keyword">const</span> <span class="identifier">StateIn2</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">DerivIn2</span> <span class="special">&amp;</span> deriv_new<span class="special">,</span> 
                  <span class="identifier">time_type</span> t_new<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>This method is used for continuous output and it calculates the state <code class="computeroutput">x</code> at a time <code class="computeroutput">t</code> from the knowledge of two states <code class="computeroutput">old_state</code> and <code class="computeroutput">current_state</code> at time points <code class="computeroutput">t_old</code> and <code class="computeroutput">t_new</code>. It also uses internal variables to calculate the result. Hence this method must be called after two successful <code class="computeroutput">do_step</code> calls. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StateIn<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm30096-bb"></a><span class="identifier">adjust_size</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>Adjust the size of all temporaries in the stepper manually. <p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">x</code></span></p></td>
<td><p>A state from which the size of the temporaries to be resized is deduced. </p></td>
</tr></tbody>
</table></div></td>
</tr></tbody>
</table></div>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm50449"></a><h3>
<a name="idm30117-bb"></a><code class="computeroutput">runge_kutta_dopri5</code> private member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StateIn<span class="special">&gt;</span> <span class="keyword">bool</span> <a name="idm30118-bb"></a><span class="identifier">resize_k_x_tmp_impl</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StateIn<span class="special">&gt;</span> <span class="keyword">bool</span> <a name="idm30124-bb"></a><span class="identifier">resize_dxdt_tmp_impl</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">StateIn</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre></li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2015 Karsten Ahnert and Mario Mulansky<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_dopri5_hpp.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_dopri5_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/stepper/runge_kutta_fehlberg78_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
