<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function template integrate_const</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Numeric.Odeint">
<link rel="up" href="../../../header/boost/numeric/odeint/integrate/integrate_const_hpp.html" title="Header &lt;boost/numeric/odeint/integrate/integrate_const.hpp&gt;">
<link rel="prev" href="../../../header/boost/numeric/odeint/integrate/integrate_const_hpp.html" title="Header &lt;boost/numeric/odeint/integrate/integrate_const.hpp&gt;">
<link rel="next" href="../../../header/boost/numeric/odeint/integrate/integrate_n_steps_hpp.html" title="Header &lt;boost/numeric/odeint/integrate/integrate_n_steps.hpp&gt;">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../logo.jpg"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../../header/boost/numeric/odeint/integrate/integrate_const_hpp.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/integrate/integrate_const_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/integrate/integrate_n_steps_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.numeric.odeint.integrate_const_idm23173"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function template integrate_const</span></h2>
<p>boost::numeric::odeint::integrate_const — Integrates the ODE with constant step size. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/numeric/odeint/integrate/integrate_const_hpp.html" title="Header &lt;boost/numeric/odeint/integrate/integrate_const.hpp&gt;">boost/numeric/odeint/integrate/integrate_const.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Stepper<span class="special">,</span> <span class="keyword">typename</span> System<span class="special">,</span> <span class="keyword">typename</span> State<span class="special">,</span> <span class="keyword">typename</span> Time<span class="special">,</span> 
         <span class="keyword">typename</span> Observer<span class="special">,</span> <span class="keyword">typename</span> StepOverflowChecker<span class="special">&gt;</span> 
  <span class="identifier">size_t</span> <span class="identifier">integrate_const</span><span class="special">(</span><span class="identifier">Stepper</span> stepper<span class="special">,</span> <span class="identifier">System</span> system<span class="special">,</span> <span class="identifier">State</span> <span class="special">&amp;</span> start_state<span class="special">,</span> 
                         <span class="identifier">Time</span> start_time<span class="special">,</span> <span class="identifier">Time</span> end_time<span class="special">,</span> <span class="identifier">Time</span> dt<span class="special">,</span> 
                         <span class="identifier">Observer</span> observer<span class="special">,</span> <span class="identifier">StepOverflowChecker</span> checker<span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm24475"></a><h2>Description</h2>
<p>Integrates the ODE defined by system using the given stepper. This method ensures that the observer is called at constant intervals dt. If the Stepper is a normal stepper without step size control, dt is also used for the numerical scheme. If a ControlledStepper is provided, the algorithm might reduce the step size to meet the error bounds, but it is ensured that the observer is always called at equidistant time points t0 + n*dt. If a DenseOutputStepper is used, the step size also may vary and the dense output is used to call the observer at equidistant time points. If a <code class="computeroutput"><a class="link" href="max_step_checker.html" title="Class max_step_checker">max_step_checker</a></code> is provided as StepOverflowChecker, a no_progress_error is thrown if too many steps (default: 500) are performed without progress, i.e. in between observer calls. If no checker is provided, no such overflow check is performed.</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">checker</code></span></p></td>
<td><p>[optional] Functor to check for step count overflows, if no checker is provided, no exception is thrown. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">dt</code></span></p></td>
<td><p>The time step between observer calls, <span class="emphasis"><em>not</em></span> necessarily the time step of the integration. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">end_time</code></span></p></td>
<td><p>The final integration time tend. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">observer</code></span></p></td>
<td><p>[optional] Function/Functor called at equidistant time intervals. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">start_state</code></span></p></td>
<td><p>The initial condition x0. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">start_time</code></span></p></td>
<td><p>The initial time t0. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">stepper</code></span></p></td>
<td><p>The stepper to be used for numerical integration. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">system</code></span></p></td>
<td><p>Function/Functor defining the rhs of the ODE. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The number of steps performed. </p></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2015 Karsten Ahnert and Mario Mulansky<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../../header/boost/numeric/odeint/integrate/integrate_const_hpp.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/integrate/integrate_const_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../../header/boost/numeric/odeint/integrate/integrate_n_steps_hpp.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
