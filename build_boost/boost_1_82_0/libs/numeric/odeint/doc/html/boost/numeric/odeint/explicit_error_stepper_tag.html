<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct explicit_error_stepper_tag</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Numeric.Odeint">
<link rel="up" href="../../../header/boost/numeric/odeint/stepper/stepper_categories_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/stepper_categories.hpp&gt;">
<link rel="prev" href="explicit_error_st_idm30355.html" title="Struct explicit_error_stepper_fsal_tag">
<link rel="next" href="stepper_tag.html" title="Struct stepper_tag">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../logo.jpg"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="explicit_error_st_idm30355.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/stepper_categories_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="stepper_tag.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.numeric.odeint.explicit_error_stepper_tag"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct explicit_error_stepper_tag</span></h2>
<p>boost::numeric::odeint::explicit_error_stepper_tag</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/numeric/odeint/stepper/stepper_categories_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/stepper_categories.hpp&gt;">boost/numeric/odeint/stepper/stepper_categories.hpp</a>&gt;

</span>
<span class="keyword">struct</span> <a class="link" href="explicit_error_stepper_tag.html" title="Struct explicit_error_stepper_tag">explicit_error_stepper_tag</a> <span class="special">:</span>
  <span class="keyword">public</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">numeric</span><span class="special">::</span><span class="identifier">odeint</span><span class="special">::</span><span class="identifier">error_stepper_tag</span>
<span class="special">{</span>
<span class="special">}</span><span class="special">;</span></pre></div>
</div>
<div class="copyright-footer">Copyright © 2009-2015 Karsten Ahnert and Mario Mulansky<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="explicit_error_st_idm30355.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/stepper_categories_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="stepper_tag.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
