# Copyright 2012 <PERSON><PERSON>
# Copyright 2012 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

# bring in rules for testing


import testing ;

use-project boost : $(BOOST_ROOT) ;

project
    : requirements
      <library>/boost/test//boost_unit_test_framework
      <define>BOOST_ALL_NO_LIB=1
      <include>../../include
      <link>static
      <toolset>clang:<cxxflags>-Wno-unused-variable

# <cxxflags>-D_SCL_SECURE_NO_WARNINGS
    ;

test-suite "odeint"
     :
     [ run runge_kutta.cpp ]
     [ run symplectic.cpp ]
     [ run rosenbrock.cpp ]
     [ run adams_bashforth.cpp ]
     [ run adams_bashforth_moulton.cpp ]
     [ run adaptive_adams_bashforth_moulton.cpp ]
     [ run abm_time_dependent.cpp ]
     [ run order_quadrature_formula.cpp ]
     [ run velocity_verlet.cpp ]
     : <testing.launcher>valgrind
     ;
