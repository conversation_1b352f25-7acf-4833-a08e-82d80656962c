<meta name="keywords" content="c++,quaternion,vector,matrix,open source">
<meta name="description" content="Generic C++ library for working with Quaternions, Vectors and Matrices">
<link rel="stylesheet" href="./zajo-light.css" disabled=true>
<script>
function switch_style()
{
	var i, tag;
	for( i=0, tag=document.getElementsByTagName("link"); i<tag.length; i++ )
		if( tag[i].rel.indexOf("stylesheet")!=-1 && tag[i].href.includes("zajo-") )
			tag[i].disabled = !tag[i].disabled;
}
</script>
