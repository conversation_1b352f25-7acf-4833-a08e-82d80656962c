# Copyright 2008-2022 <PERSON> and Reverge Studios, Inc.

# Distributed under the Boost Software License, Version 1.0. (See accompanying
# file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

project
    : requirements
		<include>../include
    ;

variant debug_qvm_hpp : debug : <define>BOOST_QVM_TEST_SINGLE_HEADER="\\\"qvm.hpp\\\"" ;
variant release_qvm_hpp : release : <define>BOOST_QVM_TEST_SINGLE_HEADER="\\\"qvm.hpp\\\"" ;
variant debug_qvm_lite_hpp : debug : <define>BOOST_QVM_TEST_SINGLE_HEADER="\\\"qvm_lite.hpp\\\"" <define>BOOST_QVM_TEST_SINGLE_HEADER_SWIZZLE=\"<boost/qvm/swizzle.hpp>\" ;
variant release_qvm_lite_hpp : release : <define>BOOST_QVM_TEST_SINGLE_HEADER="\\\"qvm_lite.hpp\\\"" <define>BOOST_QVM_TEST_SINGLE_HEADER_SWIZZLE=\"<boost/qvm/swizzle.hpp>\" ;

import testing ;
import path ;

rule
headers-compile-test ( headers * : requirements * : target-tag ? )
    {
    local test-names = ;
    for local header in $(headers)
        {
        local target = hdr$(target-tag)-$(header:D=) ;
        compile header-test.cpp : $(requirements) <define>REVERGE_HEADER=\"\<boost/qvm/$(header:B).hpp\>\" <dependency>$(header) : $(target) ;
        test-names += $(target) ;
        }
    alias hdrtest$(target-tag) : $(test-names) ;
    }

headers-compile-test [ glob ../include/boost/qvm/*.hpp ]
    : # requirements
    : # target-tag
        qvm
    ;

run math_test.cpp ;

run mat_traits_array_test.cpp ;
run mat_traits_std_array_test.cpp ;
run vec_traits_array_test.cpp ;
run vec_traits_gnuc_test.cpp ;
run vec_traits_std_array_test.cpp ;
run quat_traits_array_test.cpp ;
run quat_traits_std_array_test.cpp ;

run access_m_test.cpp ;
compile-fail access_m_fail1.cpp ;
compile-fail access_m_fail2.cpp ;

run access_q_test.cpp ;
compile-fail access_q_fail.cpp ;

run access_v_test.cpp ;
compile-fail access_v_fail1.cpp ;
compile-fail access_v_fail2.cpp ;

run assign_test.cpp ;

run to_string_test.cpp ;

run cmp_vv_test.cpp ;
run cross_test.cpp ;
run div_eq_vs_test.cpp ;
run div_vs_test.cpp ;
run dot_vv_test.cpp ;
run eq_vv_test.cpp ;
run mag_v_test.cpp ;
run mag_sqr_v_test.cpp ;
run minus_v_test.cpp ;
run minus_eq_vv_test.cpp ;
run minus_vv_test.cpp ;
run mul_eq_vs_test.cpp ;
run mul_vs_test.cpp ;
run mul_sv_test.cpp ;
run mul_vm_test.cpp ;
run normalize_v_test.cpp ;
run plus_eq_vv_test.cpp ;
run plus_vv_test.cpp ;
run scalar_cast_v_test.cpp ;
run vec_index_test.cpp ;
run vec_register_test.cpp ;

run cmp_mm_test.cpp ;
run determinant_test.cpp ;
run div_eq_ms_test.cpp ;
run div_ms_test.cpp ;
run eq_mm_test.cpp ;
run minus_m_test.cpp ;
run minus_eq_mm_test.cpp ;
run minus_mm_test.cpp ;
run mul_eq_mm_test.cpp ;
run mul_eq_ms_test.cpp ;
run mul_mm_test.cpp ;
run mul_ms_test.cpp ;
run mul_sm_test.cpp ;
run mul_mv_test.cpp ;
run inverse_m_test.cpp ;
run plus_eq_mm_test.cpp ;
run plus_mm_test.cpp ;
run scalar_cast_m_test.cpp ;
run mat_index_test.cpp ;

run cmp_qq_test.cpp ;
run conjugate_test.cpp ;
run normalize_q_test.cpp ;
run div_eq_qs_test.cpp ;
run div_qs_test.cpp ;
run dot_qq_test.cpp ;
run eq_qq_test.cpp ;
run inverse_q_test.cpp ;
run mag_q_test.cpp ;
run mag_sqr_q_test.cpp ;
run minus_q_test.cpp ;
run minus_eq_qq_test.cpp ;
run minus_qq_test.cpp ;
run mul_eq_qs_test.cpp ;
run mul_qs_test.cpp ;
run mul_qv_test.cpp ;
run mul_qq_test.cpp ;
run mul_eq_qq_test.cpp ;
run plus_eq_qq_test.cpp ;
run plus_qq_test.cpp ;
run scalar_cast_q_test.cpp ;
run slerp_test.cpp ;

run convert_to_test.cpp ;

run rot_mat_test.cpp ;
run rot_quat_test.cpp ;
run rotx_mat_test.cpp ;
run rotx_quat_test.cpp ;
run roty_mat_test.cpp ;
run roty_quat_test.cpp ;
run rotz_mat_test.cpp ;
run rotz_quat_test.cpp ;

run zero_vec_test.cpp ;
run zero_mat_test.cpp ;
run zero_quat_test.cpp ;
run identity_mat_test.cpp ;
run identity_quat_test.cpp ;

run col_test.cpp ;
run col_mat_test.cpp ;
run neg_col_test.cpp ;
run neg_row_test.cpp ;
run del_col_test.cpp ;
run del_row_test.cpp ;
run del_row_col_test.cpp ;
run swap_cols_test.cpp ;
run swap_rows_test.cpp ;
run diag_test.cpp ;
run diag_mat_test.cpp ;
run row_test.cpp ;
run row_mat_test.cpp ;
run transpose_test.cpp ;
run translation_test.cpp ;
run translation_mat_test.cpp ;

compile-fail swizzle_const_fail.cpp ;
run swizzle2_test2.cpp ;
run swizzle2_test3.cpp ;
run swizzle2_test4.cpp ;
run swizzle2_test.cpp ;
run swizzle3_test2.cpp ;
run swizzle3_test3.cpp ;
run swizzle3_test4.cpp ;
run swizzle3_test.cpp ;
run swizzle4_test2.cpp ;
run swizzle4_test3.cpp ;
run swizzle4_test4.cpp ;
run swizzle4_test.cpp ;

compile deduce_scalar_test.cpp ;
run deduce_scalar_mq_test.cpp ;
run deduce_scalar_mv_test.cpp ;
compile-fail deduce_scalar_fail.cpp ;

compile deduce_mat_test.cpp ;
compile deduce_quat_test.cpp ;
compile deduce_vec_test.cpp ;

run interop_test.cpp ;

run transform_test.cpp ;

run projection_test.cpp ;

compile scalar_traits_test.cpp ;
