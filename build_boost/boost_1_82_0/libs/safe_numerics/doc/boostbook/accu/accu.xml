<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE article PUBLIC "-//OASIS//DTD DocBook XML V4.5//EN"
"http://www.oasis-open.org/docbook/xml/4.5/docbookx.dtd">
<article>
  <articleinfo>
    <title>Correct Integer Operations With Minimal Runtime Penalties</title>

    <author>
      <firstname>Robert</firstname>

      <surname><PERSON><PERSON></surname>

      <affiliation>
        <orgname><PERSON> Software Development</orgname>
      </affiliation>
    </author>

    <pubdate>22 December 2016</pubdate>
  </articleinfo>

  <xi:include href="../safe_introduction.xml" xpointer="element(/1)"
              xmlns:xi="http://www.w3.org/2001/XInclude"/>

  <xi:include href="../eliminate_runtime_penalty.xml" xpointer="element(/1)"
              xmlns:xi="http://www.w3.org/2001/XInclude"/>

  <xi:include href="../notes.xml" xpointer="element(/1)"
              xmlns:xi="http://www.w3.org/2001/XInclude"/>

  <section id="safe_numerics.library_implementation">
    <title>Library Internals</title>

    <para>This library should compile and run correctly on any conforming
    C++14 compiler.</para>

    <para>The Safe Numerics library is implemented in terms of some more
    fundamental software components described here. It is not necessary to
    know about these components to use the library. This information has been
    included to help those who want to understand how the library works so
    they can extend it, correct bugs in it, or understand it's limitations.
    These components are also interesting in their own right. For all these
    reasons, they are described here. In general terms, the library works in
    the following manner:</para>

    <itemizedlist>
      <listitem>
        <para>All unary/binary expressions where one of the operands is a
        "safe" type are Overloaded. These overloads are declared and defined
        in the header file "safe_integer.hpp". SFINAE - "Substitution Failure
        Is Not An Error and <code>std::enable_if</code> are key features of
        C++ used to define these overloads in a correct manner.</para>
      </listitem>

      <listitem>
        <para>Each overloaded operation implements the following at compile
        time:<itemizedlist>
            <listitem>
              <para>Retrieve range of values for each operand of type T from
              <code>std::numeric_limits&lt;T&gt;::min()</code> and
              <code>std::numeric_limits&lt;T&gt;::max()</code>.</para>
            </listitem>

            <listitem>
              <para>Given the ranges of the operands, determine the range of
              the result of the operation using interval arithmetic. This is
              implemented in the "interval.hpp" header file using constexpr
              facility of C++14.</para>
            </listitem>

            <listitem>
              <para>if the range of the result type includes the range of the
              result of the operation, no run time checking of the result is
              necessary. So the operation reduces to the original built-in
              C/C++ operation.</para>
            </listitem>

            <listitem>
              <para>Otherwise, the operation is implemented as a "checked
              integer operation" at run time. This operation returns a variant
              which will contain either a correct result or an exception enum
              indicating why a correct result could not be obtained. The
              variant object is implemented in the header file
              "checked_result.hpp" and the checked operations are implemented
              in "checked.hpp".</para>
            </listitem>

            <listitem>
              <para>if a valid result has been obtained, it is passed to the
              caller.</para>
            </listitem>

            <listitem>
              <para>Otherwise, an exception is invoked.</para>
            </listitem>
          </itemizedlist></para>
      </listitem>
    </itemizedlist>
  </section>

  <xi:include href="../faq.xml" xpointer="element(/1)"
              xmlns:xi="http://www.w3.org/2001/XInclude"/>

  <section id="safe_numerics.pending_issues">
    <title>Current Status</title>

    <para>The library is currently in the <ulink
    url="http://www.boost.org/community/review_schedule.html">Boost Review
    Queue</ulink>. The proposal submission can be found in the <ulink
    url="http://blincubator.com/bi_library/safe-numerics/?gform_post_id=426">Boost
    Library Incubator</ulink></para>

    <itemizedlist>
      <listitem>
        <para>The library is currently limited to integers.</para>
      </listitem>

      <listitem>
        <para>Although care was taking to make the library portable, it's
        likely that at least some parts of the implementation - particularly
        <code>checked</code> arithmetic - depend upon two's complement
        representation of integers. Hence the library is probably not
        currently portable to other architectures.</para>
      </listitem>

      <listitem>
        <para>Currently the library permits a <code>safe&lt;int&gt;</code>
        value to be uninitialized. This supports the goal of "drop-in"
        replacement of C++/C built-in types with safe counter parts. On the
        other hand, this breaks the "always valid" guarantee.</para>
      </listitem>

      <listitem>
        <para>The library is not quite a "drop-in" replacement for all
        built-in integer types. In particular, C/C++ implements implicit
        conversions and promotions between certain integer types which are not
        captured by the operation overloads used to implement the library. In
        practice these case are few and can be addressed with minor changes to
        the user program to avoid these silent implicit conversions.</para>
      </listitem>
    </itemizedlist>
  </section>

  <xi:include href="../acknowledgements.xml" xpointer="element(/1)"
              xmlns:xi="http://www.w3.org/2001/XInclude"/>

  <xi:include href="../bibliography.xml" xpointer="element(/1)"
              xmlns:xi="http://www.w3.org/2001/XInclude"/>
</article>
