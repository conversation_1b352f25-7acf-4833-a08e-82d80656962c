# Generated by `boostdep --cmake multi_index`
# Copyright 2020 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.5...3.16)

project(boost_multi_index VERSION "${BOOST_SUPERPROJECT_VERSION}" LANGUAGES CXX)

add_library(boost_multi_index INTERFACE)
add_library(Boost::multi_index ALIAS boost_multi_index)

target_include_directories(boost_multi_index INTERFACE include)

target_link_libraries(boost_multi_index
  INTERFACE
    Boost::assert
    Boost::bind
    Boost::config
    Boost::container_hash
    Boost::core
    Boost::integer
    Boost::iterator
    Boost::move
    Boost::mpl
    Boost::preprocessor
    Boost::smart_ptr
    Boost::static_assert
    Boost::throw_exception
    Boost::tuple
    Boost::type_traits
    Boost::utility
)

if(BUILD_TESTING AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")

  add_subdirectory(test)

endif()

