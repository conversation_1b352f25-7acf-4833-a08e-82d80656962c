[{"key": "bind", "name": "Bind", "authors": ["<PERSON>"], "description": "boost::bind is a generalization of the standard functions std::bind1st and std::bind2nd. It supports arbitrary function objects, functions, function pointers, and member function pointers, and is able to bind any argument to a specific value or route input arguments into arbitrary positions.", "std": ["tr1"], "category": ["Function-objects"], "maintainers": ["<PERSON> <pdimov -at- gmail.com>"], "cxxstd": "03"}, {"key": "bind/mem_fn", "name": "Member Function", "authors": ["<PERSON>"], "description": "Generalized binders for function/object/pointers and member functions.", "documentation": "mem_fn.html", "std": ["tr1"], "category": ["Function-objects"], "cxxstd": "03"}]