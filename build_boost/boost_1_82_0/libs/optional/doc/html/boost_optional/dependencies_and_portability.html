<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Dependencies and Portability</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Boost.Optional">
<link rel="up" href="../index.html" title="Boost.Optional">
<link rel="prev" href="reference/header__boost_optional_hpp_.html" title="Header &lt;boost/optional.hpp&gt;">
<link rel="next" href="dependencies_and_portability/emplace_operations_in_older_compilers.html" title="Emplace operations in older compilers">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="reference/header__boost_optional_hpp_.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="dependencies_and_portability/emplace_operations_in_older_compilers.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_optional.dependencies_and_portability"></a><a class="link" href="dependencies_and_portability.html" title="Dependencies and Portability">Dependencies
    and Portability</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="dependencies_and_portability.html#boost_optional.dependencies_and_portability.dependencies">Dependencies</a></span></dt>
<dt><span class="section"><a href="dependencies_and_portability/emplace_operations_in_older_compilers.html">Emplace
      operations in older compilers</a></span></dt>
<dt><span class="section"><a href="dependencies_and_portability/optional_reference_binding.html">Optional
      Reference Binding</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_optional.dependencies_and_portability.dependencies"></a><a class="link" href="dependencies_and_portability.html#boost_optional.dependencies_and_portability.dependencies" title="Dependencies">Dependencies</a>
</h3></div></div></div>
<p>
        The implementation uses the following other Boost modules:
      </p>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
            assert
          </li>
<li class="listitem">
            config
          </li>
<li class="listitem">
            core
          </li>
<li class="listitem">
            detail
          </li>
<li class="listitem">
            move
          </li>
<li class="listitem">
            mpl
          </li>
<li class="listitem">
            static_assert
          </li>
<li class="listitem">
            throw_exception
          </li>
<li class="listitem">
            type_traits
          </li>
<li class="listitem">
            utility
          </li>
</ol></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2003-2007 Fernando Luis Cacciola Carballal<br>Copyright © 2014-2022 Andrzej
      Krzemieński<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="reference/header__boost_optional_hpp_.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="dependencies_and_portability/emplace_operations_in_older_compilers.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
