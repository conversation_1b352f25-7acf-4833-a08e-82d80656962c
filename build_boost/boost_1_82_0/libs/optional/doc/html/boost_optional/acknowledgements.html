<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Acknowledgements</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Boost.Optional">
<link rel="up" href="../index.html" title="Boost.Optional">
<link rel="prev" href="relnotes.html" title="Release Notes">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="relnotes.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_optional.acknowledgements"></a><a class="link" href="acknowledgements.html" title="Acknowledgements">Acknowledgements</a>
</h2></div></div></div>
<h4>
<a name="boost_optional.acknowledgements.h0"></a>
      <span class="phrase"><a name="boost_optional.acknowledgements.pre_formal_review"></a></span><a class="link" href="acknowledgements.html#boost_optional.acknowledgements.pre_formal_review">Pre-formal
      review</a>
    </h4>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          Peter Dimov suggested the name 'optional', and was the first to point out
          the need for aligned storage.
        </li>
<li class="listitem">
          Douglas Gregor developed 'type_with_alignment', and later Eric Friedman
          coded 'aligned_storage', which are the core of the optional class implementation.
        </li>
<li class="listitem">
          Andrei Alexandrescu and Brian Parker also worked with aligned storage techniques
          and their work influenced the current implementation.
        </li>
<li class="listitem">
          Gennadiy Rozental made extensive and important comments which shaped the
          design.
        </li>
<li class="listitem">
          Vesa Karvonen and Douglas Gregor made quite useful comparisons between
          optional, variant and any; and made other relevant comments.
        </li>
<li class="listitem">
          Douglas Gregor and Peter Dimov commented on comparisons and evaluation
          in boolean contexts.
        </li>
<li class="listitem">
          Eric Friedman helped understand the issues involved with aligned storage,
          move/copy operations and exception safety.
        </li>
<li class="listitem">
          Many others have participated with useful comments: Aleksey Gurotov, Kevlin
          Henney, David Abrahams, and others I can't recall.
        </li>
</ul></div>
<h4>
<a name="boost_optional.acknowledgements.h1"></a>
      <span class="phrase"><a name="boost_optional.acknowledgements.post_formal_review"></a></span><a class="link" href="acknowledgements.html#boost_optional.acknowledgements.post_formal_review">Post-formal
      review</a>
    </h4>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          William Kempf carefully considered the originally proposed interface and
          suggested the new interface which is currently used. He also started and
          fueled the discussion about the analogy optional&lt;&gt;/smart pointer
          and about relational operators.
        </li>
<li class="listitem">
          Peter Dimov, Joel de Guzman, David Abrahams, Tanton Gibbs and Ian Hanson
          focused on the relational semantics of optional (originally undefined);
          concluding with the fact that the pointer-like interface doesn't make it
          a pointer so it shall have deep relational operators.
        </li>
<li class="listitem">
          Augustus Saunders also explored the different relational semantics between
          optional&lt;&gt; and a pointer and developed the OptionalPointee concept
          as an aid against potential conflicts on generic code.
        </li>
<li class="listitem">
          Joel de Guzman noticed that optional&lt;&gt; can be seen as an API on top
          of variant&lt;T,nil_t&gt;.
        </li>
<li class="listitem">
          Dave Gomboc explained the meaning and usage of the Haskell analog to optional&lt;&gt;:
          the Maybe type constructor (analogy originally pointed out by David Sankel).
        </li>
<li class="listitem">
          Other comments were posted by Vincent Finn, Anthony Williams, Ed Brey,
          Rob Stewart, and others.
        </li>
<li class="listitem">
          Joel de Guzman made the case for the support of references and helped with
          the proper semantics.
        </li>
<li class="listitem">
          Mat Marcus shown the virtues of a value-oriented interface, influencing
          the current design, and contributed the idea of "none".
        </li>
<li class="listitem">
          Vladimir Batov's design of Boost.Convert library motivated the development
          of value accessors for <code class="computeroutput"><span class="identifier">optional</span></code>:
          functions <code class="computeroutput"><span class="identifier">value</span></code>, <code class="computeroutput"><span class="identifier">value_or</span></code>, <code class="computeroutput"><span class="identifier">value_or_eval</span></code>.
        </li>
</ul></div>
</div>
<div class="copyright-footer">Copyright © 2003-2007 Fernando Luis Cacciola Carballal<br>Copyright © 2014-2022 Andrzej
      Krzemieński<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="relnotes.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
</body>
</html>
