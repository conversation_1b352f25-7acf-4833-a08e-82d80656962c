<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<title>Optional references</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
<link rel="home" href="../index.html" title="Chapter&#160;1.&#160;Boost.Optional">
<link rel="up" href="../index.html" title="Chapter&#160;1.&#160;Boost.Optional">
<link rel="prev" href="detailed_semantics.html" title="Detailed Semantics">
<link rel="next" href="rebinding_semantics_for_assignment_of_optional_references.html" title="Rebinding semantics for assignment of optional references">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="detailed_semantics.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="rebinding_semantics_for_assignment_of_optional_references.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_optional.optional_references"></a><a class="link" href="optional_references.html" title="Optional references">Optional references</a>
</h2></div></div></div>
<p>
      This library allows the template parameter <code class="computeroutput"><span class="identifier">T</span></code>
      to be of reference type: <code class="computeroutput"><span class="identifier">T</span><span class="special">&amp;</span></code>, and to some extent, <code class="computeroutput"><span class="identifier">T</span>
      <span class="keyword">const</span><span class="special">&amp;</span></code>.
    </p>
<p>
      However, since references are not real objects some restrictions apply and
      some operations are not available in this case:
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          Converting constructors
        </li>
<li class="listitem">
          Converting assignment
        </li>
<li class="listitem">
          InPlace construction
        </li>
<li class="listitem">
          InPlace assignment
        </li>
<li class="listitem">
          Value-access via pointer
        </li>
</ul></div>
<p>
      Also, even though <code class="computeroutput"><span class="identifier">optional</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&amp;&gt;</span></code>
      treats it wrapped pseudo-object much as a real value, a true real reference
      is stored so aliasing will ocurr:
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          Copies of <code class="computeroutput"><span class="identifier">optional</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&amp;&gt;</span></code>
          will copy the references but all these references will nonetheless refer
          to the same object.
        </li>
<li class="listitem">
          Value-access will actually provide access to the referenced object rather
          than the reference itself.
        </li>
</ul></div>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top"><p>
        On compilers that do not conform to Standard C++ rules of reference binding,
        operations on optional references might give adverse results: rather than
        binding a reference to a designated object they may create an unexpected
        temporary and bind to it. For more details see <a class="link" href="dependencies_and_portability/optional_reference_binding.html" title="Optional Reference Binding">Dependencies
        and Portability section</a>.
      </p></td></tr>
</table></div>
<h4>
<a name="boost_optional.optional_references.h0"></a>
      <span class="phrase"><a name="boost_optional.optional_references.rvalue_references"></a></span><a class="link" href="optional_references.html#boost_optional.optional_references.rvalue_references">Rvalue
      references</a>
    </h4>
<p>
      Rvalue references and lvalue references to const have the ability in C++ to
      extend the life time of a temporary they bind to. Optional references do not
      have this capability, therefore to avoid surprising effects it is not possible
      to initialize an optional references from a temporary. Optional rvalue references
      are disabled altogether. Also, the initialization and assignment of an optional
      reference to const from rvalue reference is disabled.
    </p>
<pre class="programlisting"><span class="keyword">const</span> <span class="keyword">int</span><span class="special">&amp;</span> <span class="identifier">i</span> <span class="special">=</span> <span class="number">1</span><span class="special">;</span>            <span class="comment">// legal</span>
<span class="identifier">optional</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="keyword">int</span><span class="special">&amp;&gt;</span> <span class="identifier">oi</span> <span class="special">=</span> <span class="number">1</span><span class="special">;</span> <span class="comment">// illegal</span>
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright &#169; 2003-2007 Fernando Luis Cacciola Carballal<br>Copyright &#169; 2014 Andrzej Krzemie&#324;ski<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="detailed_semantics.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="rebinding_semantics_for_assignment_of_optional_references.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
