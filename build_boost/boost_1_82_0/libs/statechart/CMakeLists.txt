# Generated by `boostdep --cmake statechart`
# Copyright 2020 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.5...3.16)

project(boost_statechart VERSION "${BOOST_SUPERPROJECT_VERSION}" LANGUAGES CXX)

add_library(boost_statechart INTERFACE)
add_library(Boost::statechart ALIAS boost_statechart)

target_include_directories(boost_statechart INTERFACE include)

target_link_libraries(boost_statechart
  INTERFACE
    Boost::assert
    Boost::bind
    Boost::config
    Boost::conversion
    Boost::core
    Boost::detail
    Boost::function
    Boost::mpl
    Boost::smart_ptr
    Boost::static_assert
    Boost::thread
    Boost::type_traits
)

if(BUILD_TESTING AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")

  add_subdirectory(test)

endif()

