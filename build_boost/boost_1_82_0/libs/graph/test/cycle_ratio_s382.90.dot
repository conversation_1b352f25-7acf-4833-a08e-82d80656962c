digraph G {
 edge [w2=1];
  n79 -> n5 [w1=1];
  n74 -> n6 [w1=1];
  n15 -> n7 [w1=1];
  n102 -> n7 [w1=1];
  n94 -> n8 [w1=1];
  n95 -> n8 [w1=1];
  n93 -> n8 [w1=1];
  n93 -> n9 [w1=1];
  n42 -> n9 [w1=1];
  n7 -> n10 [w1=1];
  n68 -> n10 [w1=1];
  n1 -> n11 [w1=1];
  n39 -> n11 [w1=1];
  n99 -> n12 [w1=1];
  n50 -> n12 [w1=1];
  n23 -> n13 [w1=1];
  n29 -> n13 [w1=1];
  n95 -> n14 [w1=1];
  n37 -> n14 [w1=1];
  n99 -> n15 [w1=1];
  n50 -> n15 [w1=0];
  n35 -> n16 [w1=1];
  n81 -> n16 [w1=1];
  n39 -> n17 [w1=1];
  n88 -> n17 [w1=1];
  n99 -> n18 [w1=1];
  n98 -> n18 [w1=1];
  n102 -> n18 [w1=1];
  n95 -> n19 [w1=1];
  n37 -> n19 [w1=1];
  n94 -> n20 [w1=1];
  n95 -> n20 [w1=1];
  n38 -> n20 [w1=0];
  n1 -> n21 [w1=0];
  n93 -> n21 [w1=1];
  n49 -> n21 [w1=1];
  n55 -> n21 [w1=1];
  n104 -> n22 [w1=1];
  n105 -> n22 [w1=1];
  n96 -> n22 [w1=1];
  n94 -> n23 [w1=1];
  n85 -> n23 [w1=1];
  n1 -> n24 [w1=1];
  n93 -> n24 [w1=1];
  n12 -> n25 [w1=0];
  n53 -> n25 [w1=1];
  n1 -> n26 [w1=1];
  n60 -> n26 [w1=1];
  n76 -> n26 [w1=1];
  n2 -> n27 [w1=1];
  n61 -> n28 [w1=1];
  n69 -> n28 [w1=1];
  n94 -> n29 [w1=1];
  n85 -> n29 [w1=1];
  n3 -> n30 [w1=1];
  n92 -> n30 [w1=1];
  n48 -> n31 [w1=1];
  n54 -> n31 [w1=1];
  n65 -> n32 [w1=1];
  n71 -> n32 [w1=1];
  n9 -> n33 [w1=0];
  n80 -> n33 [w1=1];
  n14 -> n34 [w1=0];
  n19 -> n34 [w1=1];
  n98 -> n35 [w1=1];
  n43 -> n35 [w1=1];
  n1 -> n36 [w1=1];
  n46 -> n36 [w1=1];
  n52 -> n36 [w1=1];
  n94 -> n37 [w1=1];
  n85 -> n37 [w1=1];
  n62 -> n38 [w1=1];
  n83 -> n38 [w1=1];
  n45 -> n39 [w1=0];
  n64 -> n39 [w1=1];
  n103 -> n40 [w1=1];
  n91 -> n40 [w1=1];
  n11 -> n41 [w1=1];
  n72 -> n41 [w1=1];
  n77 -> n41 [w1=1];
  n86 -> n41 [w1=1];
  n94 -> n42 [w1=1];
  n95 -> n42 [w1=1];
  n85 -> n42 [w1=1];
  n99 -> n43 [w1=1];
  n102 -> n43 [w1=1];
  n50 -> n43 [w1=1];
  n1 -> n44 [w1=1];
  n59 -> n44 [w1=1];
  n95 -> n44 [w1=1];
  n93 -> n44 [w1=1];
  n94 -> n45 [w1=1];
  n44 -> n45 [w1=1];
  n94 -> n46 [w1=1];
  n95 -> n46 [w1=1];
  n93 -> n46 [w1=1];
  n39 -> n46 [w1=1];
  n17 -> n47 [w1=0];
  n21 -> n47 [w1=1];
  n105 -> n48 [w1=1];
  n96 -> n48 [w1=1];
  n59 -> n49 [w1=1];
  n94 -> n49 [w1=1];
  n95 -> n49 [w1=1];
  n92 -> n50 [w1=1];
  n57 -> n50 [w1=1];
  n30 -> n51 [w1=0];
  n56 -> n51 [w1=1];
  n26 -> n52 [w1=1];
  n39 -> n52 [w1=1];
  n93 -> n52 [w1=1];
  n99 -> n53 [w1=1];
  n50 -> n53 [w1=1];
  n105 -> n54 [w1=1];
  n96 -> n54 [w1=1];
  n59 -> n55 [w1=1];
  n26 -> n55 [w1=1];
  n3 -> n56 [w1=1];
  n92 -> n56 [w1=1];
  n103 -> n57 [w1=1];
  n22 -> n57 [w1=1];
  n39 -> n58 [w1=1];
  n90 -> n58 [w1=1];
  n1 -> n59 [w1=0];
  n32 -> n59 [w1=1];
  n8 -> n60 [w1=1];
  n26 -> n60 [w1=1];
  n85 -> n60 [w1=1];
  n104 -> n61 [w1=1];
  n70 -> n61 [w1=1];
  n1 -> n62 [w1=1];
  n26 -> n62 [w1=1];
  n93 -> n62 [w1=1];
  n1 -> n63 [w1=0];
  n26 -> n63 [w1=1];
  n94 -> n63 [w1=1];
  n95 -> n63 [w1=1];
  n75 -> n64 [w1=1];
  n82 -> n64 [w1=1];
  n4 -> n65 [w1=1];
  n59 -> n65 [w1=1];
  n98 -> n66 [w1=1];
  n39 -> n66 [w1=1];
  n94 -> n67 [w1=1];
  n87 -> n67 [w1=1];
  n102 -> n68 [w1=1];
  n15 -> n68 [w1=1];
  n104 -> n69 [w1=1];
  n70 -> n69 [w1=1];
  n105 -> n70 [w1=1];
  n96 -> n70 [w1=1];
  n4 -> n71 [w1=1];
  n59 -> n71 [w1=1];
  n1 -> n72 [w1=1];
  n26 -> n72 [w1=1];
  n94 -> n72 [w1=1];
  n39 -> n73 [w1=1];
  n101 -> n73 [w1=1];
  n73 -> n74 [w1=1];
  n78 -> n74 [w1=1];
  n1 -> n75 [w1=1];
  n39 -> n75 [w1=1];
  n26 -> n76 [w1=1];
  n106 -> n76 [w1=1];
  n1 -> n77 [w1=1];
  n94 -> n77 [w1=1];
  n95 -> n77 [w1=1];
  n98 -> n78 [w1=1];
  n101 -> n78 [w1=1];
  n58 -> n79 [w1=1];
  n66 -> n79 [w1=1];
  n93 -> n80 [w1=1];
  n42 -> n80 [w1=1];
  n98 -> n81 [w1=1];
  n43 -> n81 [w1=1];
  n59 -> n82 [w1=1];
  n26 -> n82 [w1=1];
  n93 -> n82 [w1=1];
  n89 -> n82 [w1=1];
  n1 -> n83 [w1=1];
  n59 -> n83 [w1=1];
  n26 -> n83 [w1=1];
  n93 -> n83 [w1=1];
  n97 -> n84 [w1=1];
  n100 -> n84 [w1=1];
  n18 -> n85 [w1=1];
  n50 -> n85 [w1=1];
  n97 -> n85 [w1=1];
  n1 -> n86 [w1=1];
  n93 -> n86 [w1=1];
  n1 -> n87 [w1=1];
  n95 -> n87 [w1=1];
  n93 -> n87 [w1=1];
  n39 -> n87 [w1=1];
  n59 -> n88 [w1=1];
  n26 -> n88 [w1=1];
  n94 -> n88 [w1=1];
  n95 -> n88 [w1=1];
  n94 -> n89 [w1=1];
  n95 -> n89 [w1=1];
  n24 -> n90 [w1=1];
  n63 -> n90 [w1=1];
  n104 -> n91 [w1=1];
  n105 -> n91 [w1=1];
  n96 -> n91 [w1=1];
  n1 -> n92 [w1=1];
  n51 -> n92 [w1=0];
  n1 -> n93 [w1=0];
  n33 -> n93 [w1=1];
  n60 -> n93 [w1=1];
  n1 -> n94 [w1=1];
  n13 -> n94 [w1=1];
  n60 -> n94 [w1=1];
  n1 -> n95 [w1=1];
  n34 -> n95 [w1=1];
  n60 -> n95 [w1=1];
  n1 -> n96 [w1=0];
  n57 -> n96 [w1=1];
  n96 -> n96 [w1=1];
  n1 -> n97 [w1=1];
  n84 -> n97 [w1=1];
  n85 -> n97 [w1=1];
  n1 -> n98 [w1=1];
  n16 -> n98 [w1=1];
  n85 -> n98 [w1=0];
  n1 -> n99 [w1=1];
  n25 -> n99 [w1=0];
  n85 -> n99 [w1=1];
  n99 -> n100 [w1=1];
  n98 -> n100 [w1=1];
  n102 -> n100 [w1=1];
  n50 -> n100 [w1=1];
  n20 -> n101 [w1=1];
  n64 -> n101 [w1=1];
  n1 -> n102 [w1=1];
  n10 -> n102 [w1=0];
  n85 -> n102 [w1=1];
  n1 -> n103 [w1=1];
  n40 -> n103 [w1=1];
  n57 -> n103 [w1=0];
  n1 -> n104 [w1=1];
  n28 -> n104 [w1=1];
  n57 -> n104 [w1=0];
  n1 -> n105 [w1=1];
  n31 -> n105 [w1=1];
  n57 -> n105 [w1=1];
  n94 -> n106 [w1=1];
  n95 -> n106 [w1=1];
  n93 -> n106 [w1=1];
  n85 -> n106 [w1=1];
  n47 -> n107 [w1=1];
  n36 -> n108 [w1=1];
  n5 -> n109 [w1=1];
  n41 -> n110 [w1=1];
  n6 -> n111 [w1=1];
  n67 -> n112 [w1=1];
}
