// Boost Graph Library LEDA examples regression test configuration file
//
// From the boost/status directory, run
// ./regression --tests ../libs/graph/example/sgb-regression.cfg -o graph-sgb-eg.html
//
// Please keep the entries ordered alphabetically by the test's file name.

compile libs/graph/example/girth.cpp
compile libs/graph/example/miles_span.cpp
compile libs/graph/example/topo-sort-with-sgb.cpp
compile libs/graph/example/roget_components.cpp
