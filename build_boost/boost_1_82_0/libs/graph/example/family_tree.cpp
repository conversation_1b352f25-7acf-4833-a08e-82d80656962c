//=======================================================================
// Copyright 2001 <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>,
//
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)
//=======================================================================
#include <boost/config.hpp>
#include <iostream>
#include <vector>
#include <string>
#include <boost/graph/adjacency_list.hpp>
#include <boost/tuple/tuple.hpp>
enum family
{
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>
};
int main()
{
    using namespace boost;
    const char* name[] = { "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>" };

    adjacency_list<> g(N);
    add_edge(<PERSON><PERSON>, <PERSON>, g);
    add_edge(<PERSON>ie, <PERSON>, g);
    add_edge(<PERSON>ie, <PERSON>, g);
    add_edge(Debbie, <PERSON>, g);
    add_edge(<PERSON>, <PERSON>, g);
    add_edge(<PERSON>, <PERSON>, g);

    graph_traits< adjacency_list<> >::vertex_iterator i, end;
    graph_traits< adjacency_list<> >::adjacency_iterator ai, a_end;
    property_map< adjacency_list<>, vertex_index_t >::type index_map
        = get(vertex_index, g);

    for (boost::tie(i, end) = vertices(g); i != end; ++i)
    {
        std::cout << name[get(index_map, *i)];
        boost::tie(ai, a_end) = adjacent_vertices(*i, g);
        if (ai == a_end)
            std::cout << " has no children";
        else
            std::cout << " is the parent of ";
        for (; ai != a_end; ++ai)
        {
            std::cout << name[get(index_map, *ai)];
            if (boost::next(ai) != a_end)
                std::cout << ", ";
        }
        std::cout << std::endl;
    }
    return EXIT_SUCCESS;
}
