<html>
<!--
     Copyright (c) 2002 Trustees of Indiana University

     Distributed under the Boost Software License, Version 1.0.
     (See accompanying file LICENSE_1_0.txt or copy at
     http://www.boost.org/LICENSE_1_0.txt)
  -->
<title>Boost Graph Library Users</title>
<body BGCOLOR="#ffffff" LINK="#0000ee" TEXT="#000000" VLINK="#551a8b"
        ALINK="#ff0000">
<img SRC="../../../boost.png"
     ALT="C++ Boost" width="277" height="86">
<br clear>

<h1>Boost Graph Library Users</h1>

<p>This is a list of people, projects, courses, companies, and other
languages that are using the Boost Graph Library in some way, shape,
or form.</p>

<p>If you are using the BGL and want to be listed here, please send
  mail to the Boost Users mailing list!</p>

<ul>
  <li><a href="http://www.cs.rpi.edu/~musser/gsd/">Generic Software Design Course at RPI</a></li>
  <li><a href="http://alps.comp-phys.org/">The ALPS quantum mechanics project</a></li>
  <li><a href="http://lgl.sourceforge.net">Large Graph Layout</a> at
  University of Texas</li>
   <li><a href="http://www.cs.concordia.ca/~gregb/home/<USER>">
     Bioinformatics Algorithms at Concordia University</a></li>
  <li><a href="https://web.archive.org/web/20130709183732/http://photon.poly.edu/~hbr/cs903/">Algorithm Course at Polytechnic University in Brooklyn</a></li>
  <li><a href="https://www.bioconductor.org/packages/devel/bioc/vignettes/RBGL/inst/doc/RBGL.pdf">
     BGL interface for language R.</a></li>
  <li><a href="http://www.cuj.com/documents/s=8470/cuj0307tan/">CUJ Article about Electronic Design Automation</a></li>
  <li><a href="http://www.rubydoc.info/github/monora/rgl">A BGL-inspired Ruby Graph Library</a></li>
  <li><a href="https://www.codeproject.com/Articles/5603/QuickGraph-A-100-C-graph-library-with-Graphviz-Sup">A BGL-inspired C# Graph Library</a></li>
  <li><a href="http://map1.squeakfoundation.org/sm/package/5729d80a-822b-4bc2-9420-ef7ecaea8553">A BGL-inspired Squeak (Smalltalk) Graph Library</a></li>
  <li><a href="https://web.archive.org/web/**************/http://www.datasim.nl:80/education/coursedetails.asp?coursecategory=CPP&coursecode=ADCPP">BGL course at DataSim</a></li>
  <li><a href="http://www.vrjuggler.org/">VR Juggler: Virtual Reality Tools</a></li>
  <li><a href="http://hyperworx.org">Hyperworx Platform Project</a></li>
  <li><a href="http://www.opencog.org/">OpenCog, an open source Artificial General Intelligence framework</a></li>
  <li><a href="https://www.pgRouting.org/">pgRouting extends the PostGIS/PostgreSQL geospatial database to provide geospatial routing functionality.</a></li>
</ul>

</body>
</html>
