<HTML>
<!--
     Copyright (c) <PERSON> 2000

     Distributed under the Boost Software License, Version 1.0.
     (See accompanying file LICENSE_1_0.txt or copy at
     http://www.boost.org/LICENSE_1_0.txt)
  -->
<Head>
<Title>Boost Graph Library: Dijkstra's Shortest Paths</Title>
<BODY BGCOLOR="#ffffff" LINK="#0000ee" TEXT="#000000" VLINK="#551a8b"
        ALINK="#ff0000">
<IMG SRC="../../../boost.png"
     ALT="C++ Boost" width="277" height="86">

<BR Clear>

<H1><A NAME="sec:dijkstra"></A><img src="figs/python.gif" alt="(Python)"/>
<TT>dijkstra_shortest_paths</TT>
</H1>

<P>
<PRE>
<i>// named parameter version</i>
template &lt;typename Graph, typename P, typename T, typename R&gt;
void
dijkstra_shortest_paths(Graph&amp; g,
  typename graph_traits&lt;Graph&gt;::vertex_descriptor s,
  const bgl_named_params&lt;P, T, R&gt;&amp; params);

<i>// non-named parameter version</i>
template &lt;typename Graph, typename <a href="DijkstraVisitor.html">DijkstraVisitor</a>,
	  typename PredecessorMap, typename DistanceMap,
	  typename WeightMap, typename VertexIndexMap, typename <a href="http://www.boost.org/sgi/stl/BinaryPredicate.html">CompareFunction</a>, typename <a href="http://www.boost.org/sgi/stl/BinaryFunction.html">CombineFunction</a>,
	  typename DistInf, typename DistZero, typename ColorMap = <i>default</i>&gt;
void dijkstra_shortest_paths
  (const Graph&amp; g,
   typename graph_traits&lt;Graph&gt;::vertex_descriptor s,
   PredecessorMap predecessor, DistanceMap distance, WeightMap weight,
   VertexIndexMap index_map,
   CompareFunction compare, CombineFunction combine, DistInf inf, DistZero zero,
   DijkstraVisitor vis, ColorMap color = <i>default</i>)

<i>// version that does not initialize the property maps (except for the default color map)</i>
template &lt;class Graph, class DijkstraVisitor,
          class PredecessorMap, class DistanceMap,
          class WeightMap, class IndexMap, class Compare, class Combine,
          class DistZero, class ColorMap&gt;
void
dijkstra_shortest_paths_no_init
  (const Graph&amp; g,
   typename graph_traits&lt;Graph&gt;::vertex_descriptor s,
   PredecessorMap predecessor, DistanceMap distance, WeightMap weight,
   IndexMap index_map,
   Compare compare, Combine combine, DistZero zero,
   DijkstraVisitor vis, ColorMap color = <i>default</i>);
</PRE>

<P>
This algorithm&nbsp;[<A HREF="bibliography.html#dijkstra59">10</A>,<A
HREF="bibliography.html#clr90">8</A>] solves the single-source
shortest-paths problem on a weighted, directed or undirected graph for
the case where all edge weights are nonnegative.  Use the Bellman-Ford
algorithm for the case when some edge weights are negative.  Use
breadth-first search instead of Dijkstra's algorithm when all edge
weights are equal to one.  For the definition of the shortest-path
problem see Section <A
HREF="graph_theory_review.html#sec:shortest-paths-algorithms">Shortest-Paths
Algorithms</A> for some background to the shortest-path problem.
</P>

<P>
There are two main options for obtaining output from the
<tt>dijkstra_shortest_paths()</tt> function. If you provide a
distance property map through the <tt>distance_map()</tt> parameter
then the shortest distance from the source vertex to every other
vertex in the graph will be recorded in the distance map. Also you can
record the shortest paths tree in a predecessor map: for each vertex
<i>u in V</i>, <i>p[u]</i> will be the predecessor of <i>u</i> in
the shortest paths tree (unless <i>p[u] = u</i>, in which case <i>u</i> is
either the source or a vertex unreachable from the source).  In
addition to these two options, the user can provide their own
custom-made visitor that takes actions during any of the
algorithm's event points.</P>

<P>
Dijkstra's algorithm finds all the shortest paths from the source
vertex to every other vertex by iteratively ``growing'' the set of
vertices <i>S</i> to which it knows the shortest path. At each step of
the algorithm, the next vertex added to <i>S</i> is determined by a
priority queue.  The queue contains the vertices in <i>V - S</i><a
href="#1">[1]</a> prioritized by their distance label, which is the
length of the shortest path seen so far for each vertex. The vertex
<i>u</i> at the top of the priority queue is then added to <i>S</i>,
and each of its out-edges is relaxed: if the distance to <i>u</i> plus
the weight of the out-edge <i>(u,v)</i> is less than the distance
label for <i>v</i> then the estimated distance for vertex <i>v</i> is
reduced.  The algorithm then loops back, processing the next vertex at
the top of the priority queue. The algorithm finishes when the
priority queue is empty.
</P>
<P>
The algorithm uses color markers (white, gray, and black) to keep
track of which set each vertex is in. Vertices colored black are in
<i>S</i>. Vertices colored white or gray are in <i>V-S</i>. White vertices have
not yet been discovered and gray vertices are in the priority queue.
By default, the algorithm will allocate an array to store a color
marker for each vertex in the graph. You can provide your own storage
and access for colors with the <tt>color_map()</tt> parameter.
</P>
<p>
The following is the pseudo-code for Dijkstra's single-source shortest
paths algorithm. <i>w</i> is the edge weight, <i>d</i> is the distance label,
and <i>p</i> is the predecessor of each vertex which is used to encode
the shortest paths tree. <i>Q</i> is a priority queue that supports the
DECREASE-KEY operation.  The visitor event points for the algorithm are
indicated by the labels on the right.
</p>

<table>
<tr>
<td valign="top">
<pre>
DIJKSTRA(<i>G</i>, <i>s</i>, <i>w</i>)
  <b>for</b> each vertex <i>u in V</i> <b>(This loop is not run in dijkstra_shortest_paths_no_init)</b>
    <i>d[u] := infinity</i>
    <i>p[u] := u</i>
    <i>color[u] :=</i> WHITE
  <b>end for</b>
  <i>color[s] := </i>GRAY
  <i>d[s] := 0</i>
  INSERT(<i>Q</i>, <i>s</i>)
  <b>while</b> (<i>Q != &Oslash;</i>)
    <i>u :=</i> EXTRACT-MIN(<i>Q</i>)
    <i>S := S U { u }</i>
    <b>for</b> each vertex <i>v in Adj[u]</i>
      <b>if</b> (<i>w(u,v) + d[u] < d[v]</i>)
        <i>d[v] := w(u,v) + d[u]</i>
        <i>p[v] := u</i>
        <b>if</b> (<i>color[v] =</i> WHITE)
          <i>color[v] :=</i> GRAY
          INSERT(<i>Q</i>, <i>v</i>)
        <b>else if</b> (<i>color[v] =</i> GRAY)
          DECREASE-KEY(<i>Q</i>, <i>v</i>)
      <b>else</b>
        <i>...</i>
    <b>end for</b>
    <i>color[u] :=</i> BLACK
  <b>end while</b>
  return (<i>d</i>, <i>p</i>)
</pre>
</td>
<td valign="top">
<pre>

initialize vertex <i>u</i>






discover vertex <i>s</i>

examine vertex <i>u</i>

examine edge <i>(u,v)</i>

edge <i>(u,v)</i> relaxed



discover vertex <i>v</i>



edge <i>(u,v)</i> not relaxed

finish vertex <i>u</i>
</pre>
</td>
</tr>
</table>

<h3>Where Defined</h3>

<a href="../../../boost/graph/dijkstra_shortest_paths.hpp"><tt>boost/graph/dijkstra_shortest_paths.hpp</tt></a>

<h3>Parameters</h3>

IN: <tt>const Graph&amp; g</tt>
<blockquote>
  The graph object on which the algorithm will be applied.
  The type <tt>Graph</tt> must be a model of
  <a href="./VertexListGraph.html">Vertex List Graph</a>
  and <a href="./IncidenceGraph.html">Incidence Graph</a>.<br>

  <b>Python</b>: The parameter is named <tt>graph</tt>.
</blockquote>

IN: <tt>vertex_descriptor s</tt>
<blockquote>
  The source vertex. All distance will be calculated from this vertex,
  and the shortest paths tree will be rooted at this vertex.<br>

  <b>Python</b>: The parameter is named <tt>root_vertex</tt>.
</blockquote>

<h3>Named Parameters</h3>

IN: <tt>weight_map(WeightMap w_map)</tt>
<blockquote>
  The weight or ``length'' of each edge in the graph. The weights
  must all be non-negative, and the algorithm will throw a
  <a href="./exception.html#negative_edge"><tt>negative_edge</tt></a>
  exception is one of the edges is negative.
  The type <tt>WeightMap</tt> must be a model of
  <a href="../../property_map/doc/ReadablePropertyMap.html">Readable Property Map</a>. The edge descriptor type of
  the graph needs to be usable as the key type for the weight
  map. The value type for this map must be
  the same as the value type of the distance map.<br>
  <b>Default:</b>  <tt>get(edge_weight, g)</tt><br>

  <b>Python</b>: Must be an <tt>edge_double_map</tt> for the graph.<br>
  <b>Python default</b>: <tt>graph.get_edge_double_map("weight")</tt>
</blockquote>

IN: <tt>vertex_index_map(VertexIndexMap i_map)</tt>
<blockquote>
  This maps each vertex to an integer in the range <tt>[0,
    num_vertices(g))</tt>. This is necessary for efficient updates of the
  heap data structure&nbsp;[<A
  HREF="bibliography.html#driscoll88">61</A>] when an edge is relaxed.
  The type
  <tt>VertexIndexMap</tt> must be a model of
  <a href="../../property_map/doc/ReadablePropertyMap.html">Readable Property Map</a>. The value type of the map must be an
  integer type. The vertex descriptor type of the graph needs to be
  usable as the key type of the map.<br>
  <b>Default:</b> <tt>get(vertex_index, g)</tt>.
    Note: if you use this default, make sure your graph has
    an internal <tt>vertex_index</tt> property. For example,
    <tt>adjacency_list</tt> with <tt>VertexList=listS</tt> does
    not have an internal <tt>vertex_index</tt> property.
   <br>

  <b>Python</b>: Unsupported parameter.
</blockquote>

OUT: <tt>predecessor_map(PredecessorMap p_map)</tt>
<blockquote>
  The predecessor map records the edges in the shortest path tree, the tree computed
  by the traversal of the graph. Upon completion of the algorithm, the edges
  <i>(p[u],u)</i> for all <i>u in V</i> are in the tree.  The shortest path
  from vertex <i>s</i> to each vertex <i>v</i> in the graph consists of the
  vertices <i>v</i>, <i>p[v]</i>, <i>p[p[v]]</i>, and so on until <i>s</i> is
  reached, in reverse order.  The
  tree is not guaranteed to be a minimum spanning tree. If <i>p[u] =
  u</i> then <i>u</i> is either the source vertex or a vertex that is
  not reachable from the source.  The <tt>PredecessorMap</tt> type
  must be a <a
  href="../../property_map/doc/ReadWritePropertyMap.html">Read/Write
  Property Map</a> whose key and value types are the same as the vertex
  descriptor type of the graph.<br>
  <b>Default:</b> <tt>dummy_property_map</tt><br>

  <b>Python</b>: Must be a <tt>vertex_vertex_map</tt> for the graph.<br>
</blockquote>

UTIL/OUT: <tt>distance_map(DistanceMap d_map)</tt>
<blockquote>
  The shortest path weight from the source vertex <tt>s</tt> to each
  vertex in the graph <tt>g</tt> is recorded in this property map. The
  shortest path weight is the sum of the edge weights along the
  shortest path.  The type <tt>DistanceMap</tt> must be a model of <a
  href="../../property_map/doc/ReadWritePropertyMap.html">Read/Write
  Property Map</a>. The vertex descriptor type of the graph needs to
  be usable as the key type of the distance map.

  The value type of the distance map is the element type of a <a
  href="./Monoid.html">Monoid</a> formed with the <tt>combine</tt>
  function object and the <tt>zero</tt> object for the identity
  element. Also the distance value type must have a <a
  href="http://www.boost.org/sgi/stl/StrictWeakOrdering.html">
  StrictWeakOrdering</a> provided by the <tt>compare</tt> function
  object.<br>
  <b>Default:</b> <a
  href="../../property_map/doc/iterator_property_map.html">
  <tt>iterator_property_map</tt></a> created from a
  <tt>std::vector</tt> of the <tt>WeightMap</tt>'s value type of size
  <tt>num_vertices(g)</tt> and using the <tt>i_map</tt> for the index
  map.<br>

  <b>Python</b>: Must be a <tt>vertex_double_map</tt> for the graph.<br>
</blockquote>

IN: <tt>distance_compare(CompareFunction cmp)</tt>
<blockquote>
  This function is use to compare distances to determine which vertex
  is closer to the source vertex.  The <tt>CompareFunction</tt> type
  must be a model of <a
  href="http://www.boost.org/sgi/stl/BinaryPredicate.html">Binary
  Predicate</a> and have argument types that match the value type of
  the <tt>DistanceMap</tt> property map.<br>

  <b>Default:</b>
  <tt>std::less&lt;D&gt;</tt> with <tt>D=typename
  property_traits&lt;DistanceMap&gt;::value_type</tt><br>

  <b>Python</b>: Unsupported parameter.
</blockquote>

IN: <tt>distance_combine(CombineFunction cmb)</tt>
<blockquote>
  This function is used to combine distances to compute the distance
  of a path. The <tt>CombineFunction</tt> type must be a model of <a
  href="http://www.boost.org/sgi/stl/BinaryFunction.html">Binary
  Function</a>. The first argument type of the binary function must
  match the value type of the <tt>DistanceMap</tt> property map and
  the second argument type must match the value type of the
  <tt>WeightMap</tt> property map.  The result type must be the same
  type as the distance value type.<br>

  <b>Default:</b> <tt>std::plus&lt;D&gt;</tt> with
   <tt>D=typename property_traits&lt;DistanceMap&gt;::value_type</tt><br>

  <b>Python</b>: Unsupported parameter.
</blockquote>

IN: <tt>distance_inf(D inf)</tt>
<blockquote>
  The <tt>inf</tt> object must be the greatest value of any <tt>D</tt> object.
  That is, <tt>compare(d, inf) == true</tt> for any <tt>d != inf</tt>.
  The type <tt>D</tt> is the value type of the <tt>DistanceMap</tt>.  Edges
  are assumed to have a weight less than (using <tt>distance_compare</tt> for
  comparison) this value.<br>
  <b>Default:</b> <tt>std::numeric_limits&lt;D&gt;::max()</tt><br>

  <b>Python</b>: Unsupported parameter.
</blockquote>

IN: <tt>distance_zero(D zero)</tt>
<blockquote>
  The <tt>zero</tt> value must be the identity element for the
  <a href="./Monoid.html">Monoid</a> formed by the distance values
  and the <tt>combine</tt> function object.
  The type <tt>D</tt> is the value type of the <tt>DistanceMap</tt>.<br>
  <b>Default:</b> <tt>D()</tt>with
   <tt>D=typename property_traits&lt;DistanceMap&gt;::value_type</tt><br>

  <b>Python</b>: Unsupported parameter.
</blockquote>

UTIL/OUT: <tt>color_map(ColorMap c_map)</tt>
<blockquote>
  This is used during the execution of the algorithm to mark the
  vertices. The vertices start out white and become gray when they are
  inserted in the queue. They then turn black when they are removed
  from the queue. At the end of the algorithm, vertices reachable from
  the source vertex will have been colored black. All other vertices
  will still be white. The type <tt>ColorMap</tt> must be a model of
  <a href="../../property_map/doc/ReadWritePropertyMap.html">Read/Write
  Property Map</a>. A vertex descriptor must be usable as the key type
  of the map, and the value type of the map must be a model of
  <a href="./ColorValue.html">Color Value</a>.<br>
  <b>Default:</b> an <a
  href="../../property_map/doc/iterator_property_map.html">
  <tt>iterator_property_map</tt></a> created from a <tt>std::vector</tt>
  of <tt>default_color_type</tt> of size <tt>num_vertices(g)</tt> and
  using the <tt>i_map</tt> for the index map.<br>

  <b>Python</b>: The color map must be a <tt>vertex_color_map</tt> for
  the graph.
</blockquote>

OUT: <tt>visitor(DijkstraVisitor v)</tt>
<blockquote>
  Use this to specify actions that you would like to happen
  during certain event points within the algorithm.
  The type <tt>DijkstraVisitor</tt> must be a model of the
  <a href="./DijkstraVisitor.html">Dijkstra Visitor</a> concept.
 The visitor object is passed by value <a
  href="#2">[2]</a>.<br>
  <b>Default:</b> <tt>dijkstra_visitor&lt;null_visitor&gt;</tt><br>

  <b>Python</b>: The parameter should be an object that derives from
  the <a
  href="DijkstraVisitor.html#python"><tt>DijkstraVisitor</tt></a> type
  of the graph.
</blockquote>


<H3>Complexity</H3>

<P>
The time complexity is <i>O(V log V + E)</i>.


<h3>Visitor Event Points</h3>

<ul>
<li><b><tt>vis.initialize_vertex(u, g)</tt></b>
  is invoked on each vertex in the graph before the start of the
  algorithm.
<li><b><tt>vis.examine_vertex(u, g)</tt></b>
  is invoked on a vertex as it is removed from the priority queue
  and added to set <i>S</i>. At this point we know that <i>(p[u],u)</i>
  is a shortest-paths tree edge so
  <i>d[u] = delta(s,u) = d[p[u]] + w(p[u],u)</i>. Also, the distances
  of the examined vertices is monotonically increasing
  <i>d[u<sub>1</sub>] <= d[u<sub>2</sub>] <= d[u<sub>n</sub>]</i>.
<li><b><tt>vis.examine_edge(e, g)</tt></b>
  is invoked on each out-edge of a vertex immediately after it has
  been added to set <i>S</i>.
<li><b><tt>vis.edge_relaxed(e, g)</tt></b>
  is invoked on edge <i>(u,v)</i> if <i>d[u] + w(u,v) < d[v]</i>.
  The edge <i>(u,v)</i> that participated in the last
  relaxation for vertex <i>v</i> is an edge in the shortest paths tree.
<li><b><tt>vis.discover_vertex(v, g)</tt></b>
  is invoked on vertex <i>v</i> when the edge
  <i>(u,v)</i> is examined and <i>v</i> is WHITE. Since
  a vertex is colored GRAY when it is discovered,
  each reachable vertex is discovered exactly once. This
  is also when the vertex is inserted into the priority queue.
<li><b><tt>vis.edge_not_relaxed(e, g)</tt></b>
  is invoked if the edge is not relaxed (see above).
<li><b><tt>vis.finish_vertex(u, g)</tt></b>
   is invoked on a vertex after all of its out edges have
  been examined.
</ul>

<H3>Example</H3>

<P>
See <a href="../example/dijkstra-example.cpp">
<TT>example/dijkstra-example.cpp</TT></a> for an example of using Dijkstra's
algorithm.

<H3>See also</H3> <a href="dijkstra_shortest_paths_no_color_map.html">dijkstra_shortest_paths_no_color_map</a> for a version of Dijkstra's shortest path that does not use a color map.

<H3>Notes</H3>

<a name="1">[1]</a>
The algorithm used here saves a little space by not putting all <i>V -
S</i> vertices in the priority queue at once, but instead only those
vertices in <i>V - S</i> that are discovered and therefore have a
distance less than infinity.

<p><a name="2">[2]</a>
  Since the visitor parameter is passed by value, if your visitor
  contains state then any changes to the state during the algorithm
  will be made to a copy of the visitor object, not the visitor object
  passed in. Therefore you may want the visitor to hold this state by
  pointer or reference.

<br>
<HR>
<TABLE>
<TR valign=top>
<TD nowrap>Copyright &copy; 2000-2001</TD><TD>
<A HREF="http://www.boost.org/people/jeremy_siek.htm">Jeremy Siek</A>, Indiana University (<A HREF="mailto:<EMAIL>"><EMAIL></A>)
</TD></TR></TABLE>

</BODY>
</HTML>
