<HTML>
<!--
     Copyright (c) <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> 2000

     Distributed under the Boost Software License, Version 1.0.
     (See accompanying file LICENSE_1_0.txt or copy at
     http://www.boost.org/LICENSE_1_0.txt)
  -->
<Head>
<Title>Boost Graph Library: property_writer</Title>
<BODY BGCOLOR="#ffffff" LINK="#0000ee" TEXT="#000000" VLINK="#551a8b"
        ALINK="#ff0000">
<IMG SRC="../../../boost.png"
     ALT="C++ Boost" width="277" height="86">

<BR Clear>

<H1>
<pre>
property_writer&lt;PropertyMap, OutputIterator, EventTag&gt;
</pre>
</H1>

This is an <a href="./EventVisitor.html">EventVisitor</a> that can be
used to output the property of a vertex or edge at some event-point
within an algorithm.

<p>
<tt>property_writer</tt> can be used with graph algorithms by
wrapping it with the algorithm-specific adaptor, such as <a
href="./bfs_visitor.html"><tt>bfs_visitor</tt></a> and <a
href="./dfs_visitor.html"><tt>dfs_visitor</tt></a>. Also, this event
visitor can be combined with other event visitors using
<tt>std::pair</tt> to form an EventVisitorList.

<h3>Example</h3>

The following is an excerpt from <a
href="../example/dave.cpp"><tt>examples/dave.cpp</tt></a>.

<pre>
  std::ostream_iterator<int> cout_int(std::cout, " ");
  std::ostream_iterator<char> cout_char(std::cout, " ");

  boost::breadth_first_search
    (G, vertex(a, G), make_bfs_visitor(
     std::make_pair(write_property(name, cout_char, on_discover_vertex()),
     std::make_pair(write_property(distance.begin(), cout_int,
                                   on_discover_vertex()),
     std::make_pair(print_edge(name, std::cout, on_examine_edge()),
                    print_endl(std::cout, on_finish_vertex()
                    ))))));
</pre>

<h3>Model of</h3>

<a href="./EventVisitor.html">EventVisitor</a>


<H3>Where Defined</H3>

<P>
<a href="../../../boost/graph/visitors.hpp">
<TT>boost/graph/visitors.hpp</TT></a>

<H3>Template Parameters</H3>

<P>
<TABLE border>
<TR>
<th>Parameter</th><th>Description</th><th>Default</th>
</tr>

<TR><TD><TT>PropertyMap</TT></TD>
<TD>
A <a
href="../../property_map/doc/ReadablePropertyMap.html">ReadablePropertyMap</a>
where the <tt>key_type</tt> is the vertex descriptor type or edge
descriptor of the graph (depending on the kind of event tag) and
the <tt>value_type</tt> of the property is convertible
to the <tt>value_type</tt> of the <tt>OutputIterator</tt>.
</TD>
<TD>&nbsp;</TD>
</TR>

<TR><TD><TT>OutputIterator</TT></TD>
<TD>
The iterator type used to write out the property values, which must be
a model of <a
href="http://www.boost.org/sgi/stl/OutputIterator.html">OutputIterator</a>.
</TD>
<TD>&nbsp;</TD>
</TR>


<TR><TD><TT>EventTag</TT></TD>
<TD>
The tag to specify when the <tt>property_writer</tt> should be
applied during the graph algorithm.
</TD>
<TD>&nbsp;</TD>
</TR>

</table>

<H2>Associated Types</H2>

<table border>

<tr>
<th>Type</th><th>Description</th>
</tr>

<tr>
<td><tt>property_writer::event_filter</tt></td>
<td>
This will be the same type as the template parameter <tt>EventTag</tt>.
</td>
</tr>

</table>

<h3>Member Functions</h3>

<p>

<table border>
<tr>
<th>Member</th><th>Description</th>
</tr>

<tr>
<td><tt>
property_writer(PropertyMap pa, OutputIterator out);
</tt></td>
<td>
Construct a property writer object with the property map
<tt>pa</tt> and output iterator <tt>out</tt>.
</td>
</tr>

<tr>
<td><tt>
template &lt;class X, class Graph&gt;<br>
void operator()(X x, const Graph& g);
</tt></td>
<td>
This writes the property value for <tt>x</tt> to the output iterator.<br>
<tt>*out++ = get(pa, x);</tt>
</td>
</tr>

</table>

<h3>Non-Member Functions</h3>

<table border>
<tr>
<th>Function</th><th>Description</th>
</tr>

<tr><td><tt>
template &lt;class PropertyMap, class OutputIterator, class Tag&gt;<br>
property_writer&lt;PropertyMap, OutputIterator, Tag&gt;<br>
write_property(PropertyMap pa, OutputIterator out, Tag);
</tt></td><td>
A convenient way to create a <tt>property_writer</tt>.
</td></tr>

</table>

<h3>See Also</h3>

<a href="./visitor_concepts.html">Visitor concepts</a>
<p>
The following are other event visitors: <a
<a href="./distance_recorder.html"><tt>distance_recorder</tt></a>,
<a href="./predecessor_recorder.html"><tt>predecessor_recorder</tt></a>,
and <a href="./time_stamper.html"><tt>time_stamper</tt></a>.


<br>
<HR>
<TABLE>
<TR valign=top>
<TD nowrap>Copyright &copy; 2000-2001</TD><TD>
<A HREF="http://www.boost.org/people/jeremy_siek.htm">Jeremy Siek</A>,
Indiana University (<A
HREF="mailto:<EMAIL>"><EMAIL></A>)<br>
<A HREF="http://www.boost.org/people/liequan_lee.htm">Lie-Quan Lee</A>, Indiana University (<A HREF="mailto:<EMAIL>"><EMAIL></A>)<br>
<A HREF="https://homes.cs.washington.edu/~al75">Andrew Lumsdaine</A>,
Indiana University (<A
HREF="mailto:<EMAIL>"><EMAIL></A>)
</TD></TR></TABLE>

</BODY>
</HTML>
<!--  LocalWords:  PropertyMap OutputIterator EventTag EventVisitor bfs dfs EventVisitorList
 -->
<!--  LocalWords:  cpp num dtime ftime int WritablePropertyMap map
 -->
<!--  LocalWords:  const Siek Univ Quan Lumsdaine
 -->
