<HTML>
<!--
     Copyright (c) <PERSON> 2000

     Distributed under the Boost Software License, Version 1.0.
     (See accompanying file LICENSE_1_0.txt or copy at
     http://www.boost.org/LICENSE_1_0.txt)
  -->
<Head>
<Title>Boost Graph Library: Copy Graph</Title>
<BODY BGCOLOR="#ffffff" LINK="#0000ee" TEXT="#000000" VLINK="#551a8b"
        ALINK="#ff0000">
<IMG SRC="../../../boost.png"
     ALT="C++ Boost" width="277" height="86">

<BR Clear>

<H1><TT>copy_graph</TT></H1>

<PRE>
template &lt;class <a href="./VertexListGraph.html">VertexListGraph</a>, class <a href="./MutableGraph.html">MutableGraph</a>&gt;
void copy_graph(const VertexListGraph&amp; G, MutableGraph&amp; G_copy,
    const bgl_named_params&lt;P, T, R&gt;&amp; params = <i>all defaults</i>)
</PRE>

This function copies all of the vertices and edges from graph
<tt>G</tt> into <tt>G_copy</tt>. Also, it copies the vertex and edge
properties, either by using the <tt>vertex_all</tt> and
<tt>edge_all</tt> property maps, or by user-supplied copy functions.

<H3>Where Defined</H3>

<P>
<a href="../../../boost/graph/copy.hpp"><TT>boost/graph/copy.hpp</TT></a>

<P>

<H3>Parameters</H3>

IN: <tt>const VertexListGraph&amp; G</tt>
<blockquote>
A directed or undirected graph. The graph type must be a model of <a href="./VertexListGraph.html">Vertex List Graph</a>.
</blockquote>

OUT: <tt>MutableGraph&amp; G_copy</tt>
<blockquote>
The resulting copy of the graph.  The graph type must be a model of <a
href="./MutableGraph.html">Mutable Graph</a>.
</blockquote>

<h3>Named Parameters</h3>

IN: <tt>vertex_copy(VertexCopier vc)</tt>
<blockquote>
This is a <a href="http://www.boost.org/sgi/stl/BinaryFunction.html">Binary Function</a> that copies the properties of a vertex in the original graph
into the corresponding vertex in the copy.<br>

<b>Default:</b> <tt>vertex_copier&lt;VertexListGraph, MutableGraph&gt;</tt>
which uses the property tag <tt>vertex_all</tt> to access a property
map from the graph.
</blockquote>

IN: <tt>edge_copy(EdgeCopier ec)</tt>
<blockquote>
This is a <a href="http://www.boost.org/sgi/stl/BinaryFunction.html">Binary Function</a> that copies the properties of an edge in the original graph
into the corresponding edge in the copy.<br>

<b>Default:</b> <tt>edge_copier&lt;VertexListGraph, MutableGraph&gt;</tt>
which uses the property tag <tt>edge_all</tt> to access a property
map from the graph.
</blockquote>

IN: <tt>vertex_index_map(VertexIndexMap i_map)</tt>
<blockquote>
The vertex index map type must be a model of <a
href="../../property_map/doc/ReadablePropertyMap.html">Readable Property
Map</a> and must map the vertex descriptors of <tt>G</tt> to the
integers in the half-open range <tt>[0,num_vertices(G))</tt>.<br>

<b>Default:</b> <tt>get(vertex_index, G)</tt>.
Note: if you use this default, make sure your graph has
an internal <tt>vertex_index</tt> property. For example,
<tt>adjacency_list</tt> with <tt>VertexList=listS</tt> does
not have an internal <tt>vertex_index</tt> property.
</blockquote>


UTIL/OUT: <tt>orig_to_copy(Orig2CopyMap c)</tt>
<blockquote>
This maps vertices in the original graph to vertices in the copy.

<b>Default:</b> an <a
  href="../../property_map/doc/iterator_property_map.html">
  </tt>iterator_property_map</tt></a> created from a
  <tt>std::vector</tt> of the output graph's vertex descriptor type of size
  <tt>num_vertices(g)</tt> and using the <tt>i_map</tt> for the index
  map.
</blockquote>

<H3>Complexity</H3>

<P>
The time complexity is <i>O(V + E)</i>.



<br>
<HR>
<TABLE>
<TR valign=top>
<TD nowrap>Copyright &copy; 2000-2001</TD><TD>
<A HREF="http://www.boost.org/people/jeremy_siek.htm">Jeremy Siek</A>, Indiana University (<A HREF="mailto:<EMAIL>"><EMAIL></A>)
</TD></TR></TABLE>

</BODY>
</HTML>
