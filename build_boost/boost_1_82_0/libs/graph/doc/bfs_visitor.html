<HTML>
<!--
     Copyright (c) <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> 2000

     Distributed under the Boost Software License, Version 1.0.
     (See accompanying file LICENSE_1_0.txt or copy at
     http://www.boost.org/LICENSE_1_0.txt)
  -->
<Head>
<Title>Boost Graph Library: bfs_visitor</Title>
<BODY BGCOLOR="#ffffff" LINK="#0000ee" TEXT="#000000" VLINK="#551a8b"
        ALINK="#ff0000">
<IMG SRC="../../../boost.png"
     ALT="C++ Boost" width="277" height="86">

<BR Clear>

<H1>
<pre>
bfs_visitor&lt;EventVisitorList&gt;
</pre>
</H1>

This class is an adapter that converts a list of <a
href="./EventVisitor.html">EventVisitor</a>s (constructed using
<tt>std::pair</tt>) into a <a href="./BFSVisitor.html">BFSVisitor</a>.


<h3>Example</h3>

This is an excerpt from <a
href="../example/bfs.cpp"><tt>examples/bfs.cpp</tt></a> where three
event-visitors are combined to make a BFS visitor. The functions
<tt>boost::record_distances</tt>, <tt>boost::record_predecessors</tt>,
and <tt>copy_graph</tt> are all functions that create an event
visitor.

<pre>
  // Construct graph G and obtain the source vertex s ...

  boost::breadth_first_search(G, s,
   boost::make_bfs_visitor(
    std::make_pair(boost::record_distances(d, boost::on_tree_edge()),
    std::make_pair(boost::record_predecessors(p.begin(),
                                              boost::on_tree_edge()),
                   copy_graph(G_copy, boost::on_examine_edge())))) );
</pre>


<h3>Model of</h3>

<a href="./BFSVisitor.html">BFSVisitor</a>

<H3>Template Parameters</H3>

<P>
<TABLE border>
<TR>
<th>Parameter</th><th>Description</th><th>Default</th>
</tr>

<TR><TD><TT>EventVisitorList</TT></TD>
<TD>
A list of <a href="./EventVisitor.html">EventVisitor</a>'s created
with <tt>std::pair</tt>.
</TD>
<TD><a href="./null_visitor.html"><tt>null_visitor</tt></a></TD>
</TR>

</table>

<H3>Where Defined</H3>

<P>
<a href="../../../boost/graph/breadth_first_search.hpp">
<TT>boost/graph/breadth_first_search.hpp</TT></a>

<h3>Member Functions</h3>

This class implements all of the member functions required by <a
href="./BFSVisitor.html">BFSVisitor</a>. In each function the
appropriate event is dispatched to the <a
href="./EventVisitor.html">EventVisitor</a> in the EventVisitorList.

<h3>Non-Member Functions</h3>

<table border>
<tr>
<th>Function</th><th>Description</th>
</tr>

<tr><td><tt>
template &lt;class EventVisitorList&gt;<br>
bfs_visitor&lt;EventVisitorList&gt;<br>
make_bfs_visitor(EventVisitorList ev_list);
</tt></td><td>
Returns the event visitor list adapted to be a BFS visitor.
</td></tr>

</table>

<h3>See Also</h3>

<a href="./visitor_concepts.html">Visitor concepts</a>
<p>
The following are event visitors: <a
 href="./predecessor_recorder.html"><tt>predecessor_recorder</tt></a>,
<a href="./distance_recorder.html"><tt>distance_recorder</tt></a>,
<a href="./time_stamper.html"><tt>time_stamper</tt></a>,
and <a href="./property_writer.html"><tt>property_writer</tt></a>.


<br>
<HR>
<TABLE>
<TR valign=top>
<TD nowrap>Copyright &copy; 2000-2001</TD><TD>
<A HREF="http://www.boost.org/people/jeremy_siek.htm">Jeremy Siek</A>,
Indiana University (<A
HREF="mailto:<EMAIL>"><EMAIL></A>)<br>
<A HREF="http://www.boost.org/people/liequan_lee.htm">Lie-Quan Lee</A>, Indiana University (<A HREF="mailto:<EMAIL>"><EMAIL></A>)<br>
<A HREF="https://homes.cs.washington.edu/~al75">Andrew Lumsdaine</A>,
Indiana University (<A
HREF="mailto:<EMAIL>"><EMAIL></A>)
</TD></TR></TABLE>

</BODY>
</HTML>
