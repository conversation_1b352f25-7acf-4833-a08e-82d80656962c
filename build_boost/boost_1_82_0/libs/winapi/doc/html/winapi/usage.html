<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Using Boost.WinAPI</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.WinAPI">
<link rel="up" href="../index.html" title="Chapter 1. Boost.WinAPI">
<link rel="prev" href="config.html" title="Configuration">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="config.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="winapi.usage"></a><a class="link" href="usage.html" title="Using Boost.WinAPI">Using Boost.WinAPI</a>
</h2></div></div></div>
<p>
      In order to use Boost.WinAPI you have to include one or several headers from
      the <code class="computeroutput"><span class="identifier">boost</span><span class="special">/</span><span class="identifier">winapi</span></code> directory. Each header there defines
      a portion of Windows API, the name of the header should be self-explanatory.
      Each Boost.WinAPI header may declare a number of symbols like functions and
      types in the global namespace, mimicking the Windows SDK, and the corresponding
      set of symbols in the <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">winapi</span></code>
      namespace. User's code is supposed to use the symbols from the <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">winapi</span></code>
      namespace only.
    </p>
<p>
      Most of the functions in the <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">winapi</span></code>
      have the same name and meaning as the corresponding Windows API functions.
      Types and constants have a trailing underscore ('_') in their name to avoid
      clashes with macros that are defined in Windows SDK.
    </p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
        Boost.WinAPI does not define function-name macros that expand to the <code class="computeroutput"><span class="keyword">char</span></code> or <code class="computeroutput"><span class="keyword">wchar_t</span></code>
        based functions depending on the <code class="computeroutput"><span class="identifier">UNICODE</span></code>
        macro. Boost.WinAPI also does not support <code class="computeroutput"><span class="identifier">_TCHAR</span></code>
        and related string types. Users have to explicitly use the <code class="computeroutput"><span class="special">*</span><span class="identifier">A</span></code> or
        <code class="computeroutput"><span class="special">*</span><span class="identifier">W</span></code>
        functions with appropriate argument types. Note however that <code class="computeroutput"><span class="special">*</span><span class="identifier">A</span></code> functions
        may not be available if <code class="computeroutput"><span class="identifier">BOOST_NO_ANSI_APIS</span></code>
        is defined.
      </p></td></tr>
</table></div>
<p>
      For example, here is how one would create and destroy a semaphore with Boost.WinAPI:
    </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">limits</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">winapi</span><span class="special">/</span><span class="identifier">handles</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">winapi</span><span class="special">/</span><span class="identifier">semaphore</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="identifier">boost</span><span class="special">::</span><span class="identifier">winapi</span><span class="special">::</span><span class="identifier">HANDLE_</span> <span class="identifier">h</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">winapi</span><span class="special">::</span><span class="identifier">CreateSemaphoreExW</span><span class="special">(</span>
    <span class="identifier">NULL</span><span class="special">,</span>                                                        <span class="comment">// security attributes</span>
    <span class="number">0l</span><span class="special">,</span>                                                          <span class="comment">// initial count</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">winapi</span><span class="special">::</span><span class="identifier">LONG_</span> <span class="special">&gt;::</span><span class="identifier">max</span><span class="special">(),</span>          <span class="comment">// max count</span>
    <span class="identifier">L</span><span class="string">"Local\\MySemaphore"</span><span class="special">,</span>                                       <span class="comment">// semaphore name</span>
    <span class="number">0l</span><span class="special">,</span>                                                          <span class="comment">// flags</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">winapi</span><span class="special">::</span><span class="identifier">SEMAPHORE_ALL_ACCESS_</span>                         <span class="comment">// access mode</span>
<span class="special">);</span>

<span class="keyword">if</span> <span class="special">(</span><span class="identifier">h</span><span class="special">)</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">winapi</span><span class="special">::</span><span class="identifier">CloseHandle</span><span class="special">(</span><span class="identifier">h</span><span class="special">);</span>
</pre>
<p>
      Refer to <a href="https://msdn.microsoft.com/library" target="_top">MSDN</a> for documentation
      on the particular functions, types and constants.
    </p>
</div>
<div class="copyright-footer">Copyright © 2016-2018 Andrey Semashev<p>
        Distributed under the <a href="http://boost.org/LICENSE_1_0.txt" target="_top">Boost
        Software License, Version 1.0</a>.
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="config.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
</body>
</html>
