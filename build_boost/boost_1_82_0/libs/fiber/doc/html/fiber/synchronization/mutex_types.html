<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Mutex Types</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Fiber">
<link rel="up" href="../synchronization.html" title="Synchronization">
<link rel="prev" href="../synchronization.html" title="Synchronization">
<link rel="next" href="conditions.html" title="Condition Variables">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../synchronization.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../synchronization.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="conditions.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="fiber.synchronization.mutex_types"></a><a class="link" href="mutex_types.html" title="Mutex Types">Mutex Types</a>
</h3></div></div></div>
<p>
        </p>
<h5>
<a name="class_mutex_bridgehead"></a>
  <span class="phrase"><a name="class_mutex"></a></span>
  <a class="link" href="mutex_types.html#class_mutex">Class <code class="computeroutput">mutex</code></a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">fiber</span><span class="special">/</span><span class="identifier">mutex</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">fibers</span> <span class="special">{</span>

<span class="keyword">class</span> <span class="identifier">mutex</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="identifier">mutex</span><span class="special">();</span>
    <span class="special">~</span><span class="identifier">mutex</span><span class="special">();</span>

    <span class="identifier">mutex</span><span class="special">(</span> <span class="identifier">mutex</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
    <span class="identifier">mutex</span> <span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span> <span class="identifier">mutex</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>

    <span class="keyword">void</span> <span class="identifier">lock</span><span class="special">();</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock</span><span class="special">();</span>
    <span class="keyword">void</span> <span class="identifier">unlock</span><span class="special">();</span>
<span class="special">};</span>

<span class="special">}}</span>
</pre>
<p>
        <a class="link" href="mutex_types.html#class_mutex"><code class="computeroutput">mutex</code></a> provides an exclusive-ownership mutex. At most one fiber
        can own the lock on a given instance of <a class="link" href="mutex_types.html#class_mutex"><code class="computeroutput">mutex</code></a> at any time. Multiple
        concurrent calls to <code class="computeroutput"><span class="identifier">lock</span><span class="special">()</span></code>, <code class="computeroutput"><span class="identifier">try_lock</span><span class="special">()</span></code> and <code class="computeroutput"><span class="identifier">unlock</span><span class="special">()</span></code> shall be permitted.
      </p>
<p>
        Any fiber blocked in <code class="computeroutput"><span class="identifier">lock</span><span class="special">()</span></code> is suspended until the owning fiber releases
        the lock by calling <code class="computeroutput"><span class="identifier">unlock</span><span class="special">()</span></code>.
      </p>
<p>
        </p>
<h5>
<a name="mutex_lock_bridgehead"></a>
  <span class="phrase"><a name="mutex_lock"></a></span>
  <a class="link" href="mutex_types.html#mutex_lock">Member function <code class="computeroutput">lock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">lock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Precondition:</span></dt>
<dd><p>
              The calling fiber doesn't own the mutex.
            </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
              The current fiber blocks until ownership can be obtained.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>resource_deadlock_would_occur</strong></span>: if
              <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              already owns the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="mutex_try_lock_bridgehead"></a>
  <span class="phrase"><a name="mutex_try_lock"></a></span>
  <a class="link" href="mutex_types.html#mutex_try_lock">Member function <code class="computeroutput">try_lock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="identifier">try_lock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Precondition:</span></dt>
<dd><p>
              The calling fiber doesn't own the mutex.
            </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Attempt to obtain ownership for the current fiber without blocking.
            </p></dd>
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if ownership was
              obtained for the current fiber, <code class="computeroutput"><span class="keyword">false</span></code>
              otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>resource_deadlock_would_occur</strong></span>: if
              <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              already owns the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="mutex_unlock_bridgehead"></a>
  <span class="phrase"><a name="mutex_unlock"></a></span>
  <a class="link" href="mutex_types.html#mutex_unlock">Member function <code class="computeroutput">unlock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">unlock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Precondition:</span></dt>
<dd><p>
              The current fiber owns <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>.
            </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Releases a lock on <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
              by the current fiber.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>operation_not_permitted</strong></span>: if <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              does not own the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="class_timed_mutex_bridgehead"></a>
  <span class="phrase"><a name="class_timed_mutex"></a></span>
  <a class="link" href="mutex_types.html#class_timed_mutex">Class <code class="computeroutput">timed_mutex</code></a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">fiber</span><span class="special">/</span><span class="identifier">timed_mutex</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">fibers</span> <span class="special">{</span>

<span class="keyword">class</span> <span class="identifier">timed_mutex</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="identifier">timed_mutex</span><span class="special">();</span>
    <span class="special">~</span><span class="identifier">timed_mutex</span><span class="special">();</span>

    <span class="identifier">timed_mutex</span><span class="special">(</span> <span class="identifier">timed_mutex</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
    <span class="identifier">timed_mutex</span> <span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span> <span class="identifier">timed_mutex</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>

    <span class="keyword">void</span> <span class="identifier">lock</span><span class="special">();</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock</span><span class="special">();</span>
    <span class="keyword">void</span> <span class="identifier">unlock</span><span class="special">();</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Clock</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Duration</span> <span class="special">&gt;</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock_until</span><span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">time_point</span><span class="special">&lt;</span> <span class="identifier">Clock</span><span class="special">,</span> <span class="identifier">Duration</span> <span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">timeout_time</span><span class="special">);</span>
    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Period</span> <span class="special">&gt;</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock_for</span><span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span> <span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">timeout_duration</span><span class="special">);</span>
<span class="special">};</span>

<span class="special">}}</span>
</pre>
<p>
        <a class="link" href="mutex_types.html#class_timed_mutex"><code class="computeroutput">timed_mutex</code></a> provides an exclusive-ownership mutex. At most
        one fiber can own the lock on a given instance of <a class="link" href="mutex_types.html#class_timed_mutex"><code class="computeroutput">timed_mutex</code></a> at
        any time. Multiple concurrent calls to <code class="computeroutput"><span class="identifier">lock</span><span class="special">()</span></code>, <code class="computeroutput"><span class="identifier">try_lock</span><span class="special">()</span></code>, <code class="computeroutput"><span class="identifier">try_lock_until</span><span class="special">()</span></code>, <code class="computeroutput"><span class="identifier">try_lock_for</span><span class="special">()</span></code> and <code class="computeroutput"><span class="identifier">unlock</span><span class="special">()</span></code> shall be permitted.
      </p>
<p>
        </p>
<h5>
<a name="timed_mutex_lock_bridgehead"></a>
  <span class="phrase"><a name="timed_mutex_lock"></a></span>
  <a class="link" href="mutex_types.html#timed_mutex_lock">Member function
        <code class="computeroutput">lock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">lock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Precondition:</span></dt>
<dd><p>
              The calling fiber doesn't own the mutex.
            </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
              The current fiber blocks until ownership can be obtained.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>resource_deadlock_would_occur</strong></span>: if
              <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              already owns the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="timed_mutex_try_lock_bridgehead"></a>
  <span class="phrase"><a name="timed_mutex_try_lock"></a></span>
  <a class="link" href="mutex_types.html#timed_mutex_try_lock">Member
        function <code class="computeroutput">try_lock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="identifier">try_lock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Precondition:</span></dt>
<dd><p>
              The calling fiber doesn't own the mutex.
            </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Attempt to obtain ownership for the current fiber without blocking.
            </p></dd>
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if ownership was
              obtained for the current fiber, <code class="computeroutput"><span class="keyword">false</span></code>
              otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>resource_deadlock_would_occur</strong></span>: if
              <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              already owns the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="timed_mutex_unlock_bridgehead"></a>
  <span class="phrase"><a name="timed_mutex_unlock"></a></span>
  <a class="link" href="mutex_types.html#timed_mutex_unlock">Member function
        <code class="computeroutput">unlock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">unlock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Precondition:</span></dt>
<dd><p>
              The current fiber owns <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>.
            </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Releases a lock on <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
              by the current fiber.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>operation_not_permitted</strong></span>: if <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              does not own the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="timed_mutex_try_lock_until_bridgehead"></a>
  <span class="phrase"><a name="timed_mutex_try_lock_until"></a></span>
  <a class="link" href="mutex_types.html#timed_mutex_try_lock_until">Templated
        member function <code class="computeroutput">try_lock_until</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Clock</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Duration</span> <span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">try_lock_until</span><span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">time_point</span><span class="special">&lt;</span> <span class="identifier">Clock</span><span class="special">,</span> <span class="identifier">Duration</span> <span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">timeout_time</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Precondition:</span></dt>
<dd><p>
              The calling fiber doesn't own the mutex.
            </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Attempt to obtain ownership for the current fiber. Blocks until ownership
              can be obtained, or the specified time is reached. If the specified
              time has already passed, behaves as <a class="link" href="mutex_types.html#timed_mutex_try_lock"><code class="computeroutput">timed_mutex::try_lock()</code></a>.
            </p></dd>
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if ownership was
              obtained for the current fiber, <code class="computeroutput"><span class="keyword">false</span></code>
              otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>, timeout-related
              exceptions.
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>resource_deadlock_would_occur</strong></span>: if
              <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              already owns the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="timed_mutex_try_lock_for_bridgehead"></a>
  <span class="phrase"><a name="timed_mutex_try_lock_for"></a></span>
  <a class="link" href="mutex_types.html#timed_mutex_try_lock_for">Templated
        member function <code class="computeroutput">try_lock_for</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Period</span> <span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">try_lock_for</span><span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span> <span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">timeout_duration</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Precondition:</span></dt>
<dd><p>
              The calling fiber doesn't own the mutex.
            </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Attempt to obtain ownership for the current fiber. Blocks until ownership
              can be obtained, or the specified time is reached. If the specified
              time has already passed, behaves as <a class="link" href="mutex_types.html#timed_mutex_try_lock"><code class="computeroutput">timed_mutex::try_lock()</code></a>.
            </p></dd>
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if ownership was
              obtained for the current fiber, <code class="computeroutput"><span class="keyword">false</span></code>
              otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>, timeout-related
              exceptions.
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>resource_deadlock_would_occur</strong></span>: if
              <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              already owns the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="class_recursive_mutex_bridgehead"></a>
  <span class="phrase"><a name="class_recursive_mutex"></a></span>
  <a class="link" href="mutex_types.html#class_recursive_mutex">Class
        <code class="computeroutput">recursive_mutex</code></a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">fiber</span><span class="special">/</span><span class="identifier">recursive_mutex</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">fibers</span> <span class="special">{</span>

<span class="keyword">class</span> <span class="identifier">recursive_mutex</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="identifier">recursive_mutex</span><span class="special">();</span>
    <span class="special">~</span><span class="identifier">recursive_mutex</span><span class="special">();</span>

    <span class="identifier">recursive_mutex</span><span class="special">(</span> <span class="identifier">recursive_mutex</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
    <span class="identifier">recursive_mutex</span> <span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span> <span class="identifier">recursive_mutex</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>

    <span class="keyword">void</span> <span class="identifier">lock</span><span class="special">();</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">;</span>
    <span class="keyword">void</span> <span class="identifier">unlock</span><span class="special">();</span>
<span class="special">};</span>

<span class="special">}}</span>
</pre>
<p>
        <a class="link" href="mutex_types.html#class_recursive_mutex"><code class="computeroutput">recursive_mutex</code></a> provides an exclusive-ownership recursive
        mutex. At most one fiber can own the lock on a given instance of <a class="link" href="mutex_types.html#class_recursive_mutex"><code class="computeroutput">recursive_mutex</code></a> at
        any time. Multiple concurrent calls to <code class="computeroutput"><span class="identifier">lock</span><span class="special">()</span></code>, <code class="computeroutput"><span class="identifier">try_lock</span><span class="special">()</span></code> and <code class="computeroutput"><span class="identifier">unlock</span><span class="special">()</span></code> shall be permitted. A fiber that already
        has exclusive ownership of a given <a class="link" href="mutex_types.html#class_recursive_mutex"><code class="computeroutput">recursive_mutex</code></a> instance
        can call <code class="computeroutput"><span class="identifier">lock</span><span class="special">()</span></code>
        or <code class="computeroutput"><span class="identifier">try_lock</span><span class="special">()</span></code>
        to acquire an additional level of ownership of the mutex. <code class="computeroutput"><span class="identifier">unlock</span><span class="special">()</span></code> must be called once for each level of ownership
        acquired by a single fiber before ownership can be acquired by another fiber.
      </p>
<p>
        </p>
<h5>
<a name="recursive_mutex_lock_bridgehead"></a>
  <span class="phrase"><a name="recursive_mutex_lock"></a></span>
  <a class="link" href="mutex_types.html#recursive_mutex_lock">Member
        function <code class="computeroutput">lock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">lock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              The current fiber blocks until ownership can be obtained.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="recursive_mutex_try_lock_bridgehead"></a>
  <span class="phrase"><a name="recursive_mutex_try_lock"></a></span>
  <a class="link" href="mutex_types.html#recursive_mutex_try_lock">Member
        function <code class="computeroutput">try_lock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="identifier">try_lock</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Attempt to obtain ownership for the current fiber without blocking.
            </p></dd>
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if ownership was
              obtained for the current fiber, <code class="computeroutput"><span class="keyword">false</span></code>
              otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="recursive_mutex_unlock_bridgehead"></a>
  <span class="phrase"><a name="recursive_mutex_unlock"></a></span>
  <a class="link" href="mutex_types.html#recursive_mutex_unlock">Member
        function <code class="computeroutput">unlock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">unlock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Releases a lock on <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
              by the current fiber.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>operation_not_permitted</strong></span>: if <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              does not own the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="class_recursive_timed_mutex_bridgehead"></a>
  <span class="phrase"><a name="class_recursive_timed_mutex"></a></span>
  <a class="link" href="mutex_types.html#class_recursive_timed_mutex">Class
        <code class="computeroutput">recursive_timed_mutex</code></a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">fiber</span><span class="special">/</span><span class="identifier">recursive_timed_mutex</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">fibers</span> <span class="special">{</span>

<span class="keyword">class</span> <span class="identifier">recursive_timed_mutex</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="identifier">recursive_timed_mutex</span><span class="special">();</span>
    <span class="special">~</span><span class="identifier">recursive_timed_mutex</span><span class="special">();</span>

    <span class="identifier">recursive_timed_mutex</span><span class="special">(</span> <span class="identifier">recursive_timed_mutex</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
    <span class="identifier">recursive_timed_mutex</span> <span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span> <span class="identifier">recursive_timed_mutex</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>

    <span class="keyword">void</span> <span class="identifier">lock</span><span class="special">();</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">;</span>
    <span class="keyword">void</span> <span class="identifier">unlock</span><span class="special">();</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Clock</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Duration</span> <span class="special">&gt;</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock_until</span><span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">time_point</span><span class="special">&lt;</span> <span class="identifier">Clock</span><span class="special">,</span> <span class="identifier">Duration</span> <span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">timeout_time</span><span class="special">);</span>
    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Period</span> <span class="special">&gt;</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock_for</span><span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span> <span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">timeout_duration</span><span class="special">);</span>
<span class="special">};</span>

<span class="special">}}</span>
</pre>
<p>
        <a class="link" href="mutex_types.html#class_recursive_timed_mutex"><code class="computeroutput">recursive_timed_mutex</code></a> provides an exclusive-ownership
        recursive mutex. At most one fiber can own the lock on a given instance of
        <a class="link" href="mutex_types.html#class_recursive_timed_mutex"><code class="computeroutput">recursive_timed_mutex</code></a> at any time. Multiple concurrent
        calls to <code class="computeroutput"><span class="identifier">lock</span><span class="special">()</span></code>,
        <code class="computeroutput"><span class="identifier">try_lock</span><span class="special">()</span></code>,
        <code class="computeroutput"><span class="identifier">try_lock_for</span><span class="special">()</span></code>,
        <code class="computeroutput"><span class="identifier">try_lock_until</span><span class="special">()</span></code>
        and <code class="computeroutput"><span class="identifier">unlock</span><span class="special">()</span></code>
        shall be permitted. A fiber that already has exclusive ownership of a given
        <a class="link" href="mutex_types.html#class_recursive_timed_mutex"><code class="computeroutput">recursive_timed_mutex</code></a> instance can call <code class="computeroutput"><span class="identifier">lock</span><span class="special">()</span></code>,
        <code class="computeroutput"><span class="identifier">try_lock</span><span class="special">()</span></code>,
        <code class="computeroutput"><span class="identifier">try_lock_for</span><span class="special">()</span></code>
        or <code class="computeroutput"><span class="identifier">try_lock_until</span><span class="special">()</span></code>
        to acquire an additional level of ownership of the mutex. <code class="computeroutput"><span class="identifier">unlock</span><span class="special">()</span></code> must be called once for each level of ownership
        acquired by a single fiber before ownership can be acquired by another fiber.
      </p>
<p>
        </p>
<h5>
<a name="recursive_timed_mutex_lock_bridgehead"></a>
  <span class="phrase"><a name="recursive_timed_mutex_lock"></a></span>
  <a class="link" href="mutex_types.html#recursive_timed_mutex_lock">Member
        function <code class="computeroutput">lock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">lock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              The current fiber blocks until ownership can be obtained.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="recursive_timed_mutex_try_lock_bridgehead"></a>
  <span class="phrase"><a name="recursive_timed_mutex_try_lock"></a></span>
  <a class="link" href="mutex_types.html#recursive_timed_mutex_try_lock">Member
        function <code class="computeroutput">try_lock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="identifier">try_lock</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Attempt to obtain ownership for the current fiber without blocking.
            </p></dd>
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if ownership was
              obtained for the current fiber, <code class="computeroutput"><span class="keyword">false</span></code>
              otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="recursive_timed_mutex_unlock_bridgehead"></a>
  <span class="phrase"><a name="recursive_timed_mutex_unlock"></a></span>
  <a class="link" href="mutex_types.html#recursive_timed_mutex_unlock">Member
        function <code class="computeroutput">unlock</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">unlock</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Releases a lock on <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
              by the current fiber.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">lock_error</span></code>
            </p></dd>
<dt><span class="term">Error Conditions:</span></dt>
<dd><p>
              <span class="bold"><strong>operation_not_permitted</strong></span>: if <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">this_fiber</span><span class="special">::</span><span class="identifier">get_id</span><span class="special">()</span></code>
              does not own the mutex.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="recursive_timed_mutex_try_lock_until_bridgehead"></a>
  <span class="phrase"><a name="recursive_timed_mutex_try_lock_until"></a></span>
  <a class="link" href="mutex_types.html#recursive_timed_mutex_try_lock_until">Templated
        member function <code class="computeroutput">try_lock_until</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Clock</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Duration</span> <span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">try_lock_until</span><span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">time_point</span><span class="special">&lt;</span> <span class="identifier">Clock</span><span class="special">,</span> <span class="identifier">Duration</span> <span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">timeout_time</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Attempt to obtain ownership for the current fiber. Blocks until ownership
              can be obtained, or the specified time is reached. If the specified
              time has already passed, behaves as <a class="link" href="mutex_types.html#recursive_timed_mutex_try_lock"><code class="computeroutput">recursive_timed_mutex::try_lock()</code></a>.
            </p></dd>
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if ownership was
              obtained for the current fiber, <code class="computeroutput"><span class="keyword">false</span></code>
              otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Timeout-related exceptions.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="recursive_timed_mutex_try_lock_for_bridgehead"></a>
  <span class="phrase"><a name="recursive_timed_mutex_try_lock_for"></a></span>
  <a class="link" href="mutex_types.html#recursive_timed_mutex_try_lock_for">Templated
        member function <code class="computeroutput">try_lock_for</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Period</span> <span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">try_lock_for</span><span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span> <span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">timeout_duration</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Attempt to obtain ownership for the current fiber. Blocks until ownership
              can be obtained, or the specified time is reached. If the specified
              time has already passed, behaves as <a class="link" href="mutex_types.html#recursive_timed_mutex_try_lock"><code class="computeroutput">recursive_timed_mutex::try_lock()</code></a>.
            </p></dd>
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if ownership was
              obtained for the current fiber, <code class="computeroutput"><span class="keyword">false</span></code>
              otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Timeout-related exceptions.
            </p></dd>
</dl>
</div>
</div>
<div class="copyright-footer">Copyright © 2013 Oliver Kowalke<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../synchronization.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../synchronization.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="conditions.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
