<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Channels</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Fiber">
<link rel="up" href="../synchronization.html" title="Synchronization">
<link rel="prev" href="barriers.html" title="Barriers">
<link rel="next" href="channels/buffered_channel.html" title="Buffered Channel">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="barriers.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../synchronization.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="channels/buffered_channel.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="fiber.synchronization.channels"></a><a class="link" href="channels.html" title="Channels">Channels</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="channels/buffered_channel.html">Buffered
        Channel</a></span></dt>
<dt><span class="section"><a href="channels/unbuffered_channel.html">Unbuffered
        Channel</a></span></dt>
</dl></div>
<p>
        A channel is a model to communicate and synchronize <code class="computeroutput"><span class="identifier">Threads</span>
        <span class="identifier">of</span> <span class="identifier">Execution</span></code>
        <a href="#ftn.fiber.synchronization.channels.f0" class="footnote" name="fiber.synchronization.channels.f0"><sup class="footnote">[2]</sup></a> via message passing.
      </p>
<a name="class_channel_op_status"></a><h5>
<a name="fiber.synchronization.channels.h0"></a>
        <span class="phrase"><a name="fiber.synchronization.channels.enumeration__code__phrase_role__identifier__channel_op_status__phrase___code_"></a></span><a class="link" href="channels.html#fiber.synchronization.channels.enumeration__code__phrase_role__identifier__channel_op_status__phrase___code_">Enumeration
        <code class="computeroutput"><span class="identifier">channel_op_status</span></code></a>
      </h5>
<p>
        channel operations return the state of the channel.
      </p>
<pre class="programlisting"><span class="keyword">enum</span> <span class="keyword">class</span> <span class="identifier">channel_op_status</span> <span class="special">{</span>
    <span class="identifier">success</span><span class="special">,</span>
    <span class="identifier">empty</span><span class="special">,</span>
    <span class="identifier">full</span><span class="special">,</span>
    <span class="identifier">closed</span><span class="special">,</span>
    <span class="identifier">timeout</span>
<span class="special">};</span>
</pre>
<h5>
<a name="fiber.synchronization.channels.h1"></a>
        <span class="phrase"><a name="fiber.synchronization.channels._code__phrase_role__identifier__success__phrase___code_"></a></span><a class="link" href="channels.html#fiber.synchronization.channels._code__phrase_role__identifier__success__phrase___code_"><code class="computeroutput"><span class="identifier">success</span></code></a>
      </h5>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Operation was successful.
            </p></dd>
</dl>
</div>
<h5>
<a name="fiber.synchronization.channels.h2"></a>
        <span class="phrase"><a name="fiber.synchronization.channels._code__phrase_role__identifier__empty__phrase___code_"></a></span><a class="link" href="channels.html#fiber.synchronization.channels._code__phrase_role__identifier__empty__phrase___code_"><code class="computeroutput"><span class="identifier">empty</span></code></a>
      </h5>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              channel is empty, operation failed.
            </p></dd>
</dl>
</div>
<h5>
<a name="fiber.synchronization.channels.h3"></a>
        <span class="phrase"><a name="fiber.synchronization.channels._code__phrase_role__identifier__full__phrase___code_"></a></span><a class="link" href="channels.html#fiber.synchronization.channels._code__phrase_role__identifier__full__phrase___code_"><code class="computeroutput"><span class="identifier">full</span></code></a>
      </h5>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              channel is full, operation failed.
            </p></dd>
</dl>
</div>
<h5>
<a name="fiber.synchronization.channels.h4"></a>
        <span class="phrase"><a name="fiber.synchronization.channels._code__phrase_role__identifier__closed__phrase___code_"></a></span><a class="link" href="channels.html#fiber.synchronization.channels._code__phrase_role__identifier__closed__phrase___code_"><code class="computeroutput"><span class="identifier">closed</span></code></a>
      </h5>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              channel is closed, operation failed.
            </p></dd>
</dl>
</div>
<h5>
<a name="fiber.synchronization.channels.h5"></a>
        <span class="phrase"><a name="fiber.synchronization.channels._code__phrase_role__identifier__timeout__phrase___code_"></a></span><a class="link" href="channels.html#fiber.synchronization.channels._code__phrase_role__identifier__timeout__phrase___code_"><code class="computeroutput"><span class="identifier">timeout</span></code></a>
      </h5>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              The operation did not become ready before specified timeout elapsed.
            </p></dd>
</dl>
</div>
<div class="footnotes">
<br><hr style="width:100; text-align:left;margin-left: 0">
<div id="ftn.fiber.synchronization.channels.f0" class="footnote"><p><a href="#fiber.synchronization.channels.f0" class="para"><sup class="para">[2] </sup></a>
          The smallest ordered sequence of instructions that can be managed independently
          by a scheduler is called a <code class="computeroutput"><span class="identifier">Thread</span>
          <span class="identifier">of</span> <span class="identifier">Execution</span></code>.
        </p></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2013 Oliver Kowalke<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="barriers.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../synchronization.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="channels/buffered_channel.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
