<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Event-Driven Program</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Fiber">
<link rel="up" href="../integration.html" title="Sharing a Thread with Another Main Loop">
<link rel="prev" href="overview.html" title="Overview">
<link rel="next" href="embedded_main_loop.html" title="Embedded Main Loop">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overview.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../integration.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="embedded_main_loop.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="fiber.integration.event_driven_program"></a><a class="link" href="event_driven_program.html" title="Event-Driven Program">Event-Driven
      Program</a>
</h3></div></div></div>
<p>
        Consider a classic event-driven program, organized around a main loop that
        fetches and dispatches incoming I/O events. You are introducing <span class="bold"><strong>Boost.Fiber</strong></span> because certain asynchronous I/O sequences
        are logically sequential, and for those you want to write and maintain code
        that looks and acts sequential.
      </p>
<p>
        You are launching fibers on the application’s main thread because certain
        of their actions will affect its user interface, and the application’s UI
        framework permits UI operations only on the main thread. Or perhaps those
        fibers need access to main-thread data, and it would be too expensive in
        runtime (or development time) to robustly defend every such data item with
        thread synchronization primitives.
      </p>
<p>
        You must ensure that the application’s main loop <span class="emphasis"><em>itself</em></span>
        doesn’t monopolize the processor: that the fibers it launches will get the
        CPU cycles they need.
      </p>
<p>
        The solution is the same as for any fiber that might claim the CPU for an
        extended time: introduce calls to <a class="link" href="../fiber_mgmt/this_fiber.html#this_fiber_yield"><code class="computeroutput">this_fiber::yield()</code></a>. The
        most straightforward approach is to call <code class="computeroutput"><span class="identifier">yield</span><span class="special">()</span></code> on every iteration of your existing main
        loop. In effect, this unifies the application’s main loop with <span class="bold"><strong>Boost.Fiber</strong></span>’s
        internal main loop. <code class="computeroutput"><span class="identifier">yield</span><span class="special">()</span></code> allows the fiber manager to run any fibers
        that have become ready since the previous iteration of the application’s main
        loop. When these fibers have had a turn, control passes to the thread’s main
        fiber, which returns from <code class="computeroutput"><span class="identifier">yield</span><span class="special">()</span></code> and resumes the application’s main loop.
      </p>
</div>
<div class="copyright-footer">Copyright © 2013 Oliver Kowalke<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overview.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../integration.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="embedded_main_loop.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
