<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class fiber::id</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Fiber">
<link rel="up" href="../fiber_mgmt.html" title="Fiber management">
<link rel="prev" href="fiber.html" title="Class fiber">
<link rel="next" href="this_fiber.html" title="Namespace this_fiber">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="fiber.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../fiber_mgmt.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="this_fiber.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="fiber.fiber_mgmt.id"></a><a name="class_id"></a><a class="link" href="id.html" title="Class fiber::id">Class fiber::id</a>
</h3></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">fiber</span><span class="special">/</span><span class="identifier">fiber</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">fibers</span> <span class="special">{</span>

<span class="keyword">class</span> <span class="identifier">id</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">constexpr</span> <span class="identifier">id</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">;</span>

    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">charT</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">traitsT</span> <span class="special">&gt;</span>
    <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span>
    <span class="keyword">operator</span><span class="special">&lt;&lt;(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traitsT</span> <span class="special">&gt;</span> <span class="special">&amp;,</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;);</span>
<span class="special">};</span>

<span class="special">}}</span>
</pre>
<h5>
<a name="fiber.fiber_mgmt.id.h0"></a>
        <span class="phrase"><a name="fiber.fiber_mgmt.id.constructor"></a></span><a class="link" href="id.html#fiber.fiber_mgmt.id.constructor">Constructor</a>
      </h5>
<pre class="programlisting"><span class="keyword">constexpr</span> <span class="identifier">id</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
              Represents an instance of <span class="emphasis"><em>not-a-fiber</em></span>.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="id_operator_equal_bridgehead"></a>
  <span class="phrase"><a name="id_operator_equal"></a></span>
  <a class="link" href="id.html#id_operator_equal">Member function
        <code class="computeroutput">operator==</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
              and <code class="computeroutput"><span class="identifier">other</span></code> represent
              the same fiber, or both represent <span class="emphasis"><em>not-a-fiber</em></span>,
              <code class="computeroutput"><span class="keyword">false</span></code> otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="id_operator_not_equal_bridgehead"></a>
  <span class="phrase"><a name="id_operator_not_equal"></a></span>
  <a class="link" href="id.html#id_operator_not_equal">Member
        function <code class="computeroutput">operator!=</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput">! (other == * this)</code>
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="id_operator_less_bridgehead"></a>
  <span class="phrase"><a name="id_operator_less"></a></span>
  <a class="link" href="id.html#id_operator_less">Member function
        <code class="computeroutput">operator&lt;</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span> <span class="special">!=</span> <span class="identifier">other</span></code>
              is true and the implementation-defined total order of <code class="computeroutput"><span class="identifier">fiber</span><span class="special">::</span><span class="identifier">id</span></code> values places <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code> before <code class="computeroutput"><span class="identifier">other</span></code>,
              false otherwise.
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="id_operator_greater_bridgehead"></a>
  <span class="phrase"><a name="id_operator_greater"></a></span>
  <a class="link" href="id.html#id_operator_greater">Member
        function <code class="computeroutput">operator&gt;</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">other</span> <span class="special">&lt;</span>
              <span class="special">*</span> <span class="keyword">this</span></code>
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="id_operator_less_equal_bridgehead"></a>
  <span class="phrase"><a name="id_operator_less_equal"></a></span>
  <a class="link" href="id.html#id_operator_less_equal">Member
        function <code class="computeroutput">operator&lt;=</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="special">!</span> <span class="special">(</span><span class="identifier">other</span> <span class="special">&lt;</span>
              <span class="special">*</span> <span class="keyword">this</span><span class="special">)</span></code>
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing.
            </p></dd>
</dl>
</div>
<p>
        </p>
<h5>
<a name="id_operator_greater_equal_bridgehead"></a>
  <span class="phrase"><a name="id_operator_greater_equal"></a></span>
  <a class="link" href="id.html#id_operator_greater_equal">Member
        function <code class="computeroutput">operator&gt;=</code>()</a>
</h5>
<p>
      </p>
<pre class="programlisting"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="special">!</span> <span class="special">(*</span>
              <span class="keyword">this</span> <span class="special">&lt;</span>
              <span class="identifier">other</span><span class="special">)</span></code>
            </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
              Nothing.
            </p></dd>
</dl>
</div>
<h5>
<a name="fiber.fiber_mgmt.id.h1"></a>
        <span class="phrase"><a name="fiber.fiber_mgmt.id.operator_lt__lt_"></a></span><a class="link" href="id.html#fiber.fiber_mgmt.id.operator_lt__lt_">operator&lt;&lt;</a>
      </h5>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">charT</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">traitsT</span> <span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span>
<span class="keyword">operator</span><span class="special">&lt;&lt;(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> <span class="identifier">os</span><span class="special">,</span> <span class="identifier">id</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Efects:</span></dt>
<dd><p>
              Writes the representation of <code class="computeroutput"><span class="identifier">other</span></code>
              to stream <code class="computeroutput"><span class="identifier">os</span></code>. The representation
              is unspecified.
            </p></dd>
<dt><span class="term">Returns:</span></dt>
<dd><p>
              <code class="computeroutput"><span class="identifier">os</span></code>
            </p></dd>
</dl>
</div>
</div>
<div class="copyright-footer">Copyright © 2013 Oliver Kowalke<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="fiber.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../fiber_mgmt.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="this_fiber.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
