<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Overview</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Fiber">
<link rel="up" href="../callbacks.html" title="Integrating Fibers with Asynchronous Callbacks">
<link rel="prev" href="../callbacks.html" title="Integrating Fibers with Asynchronous Callbacks">
<link rel="next" href="return_errorcode.html" title="Return Errorcode">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../callbacks.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../callbacks.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="return_errorcode.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="fiber.callbacks.overview"></a><a class="link" href="overview.html" title="Overview">Overview</a>
</h3></div></div></div>
<p>
        One of the primary benefits of <span class="bold"><strong>Boost.Fiber</strong></span>
        is the ability to use asynchronous operations for efficiency, while at the
        same time structuring the calling code <span class="emphasis"><em>as if</em></span> the operations
        were synchronous. Asynchronous operations provide completion notification
        in a variety of ways, but most involve a callback function of some kind.
        This section discusses tactics for interfacing <span class="bold"><strong>Boost.Fiber</strong></span>
        with an arbitrary async operation.
      </p>
<p>
        For purposes of illustration, consider the following hypothetical API:
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">class</span> <span class="identifier">AsyncAPI</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="comment">// constructor acquires some resource that can be read and written</span>
    <span class="identifier">AsyncAPI</span><span class="special">();</span>

    <span class="comment">// callbacks accept an int error code; 0 == success</span>
    <span class="keyword">typedef</span> <span class="keyword">int</span> <span class="identifier">errorcode</span><span class="special">;</span>

    <span class="comment">// write callback only needs to indicate success or failure</span>
    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Fn</span> <span class="special">&gt;</span>
    <span class="keyword">void</span> <span class="identifier">init_write</span><span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">data</span><span class="special">,</span> <span class="identifier">Fn</span> <span class="special">&amp;&amp;</span> <span class="identifier">callback</span><span class="special">);</span>

    <span class="comment">// read callback needs to accept both errorcode and data</span>
    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Fn</span> <span class="special">&gt;</span>
    <span class="keyword">void</span> <span class="identifier">init_read</span><span class="special">(</span> <span class="identifier">Fn</span> <span class="special">&amp;&amp;</span> <span class="identifier">callback</span><span class="special">);</span>

    <span class="comment">// ... other operations ...</span>
<span class="special">};</span>
</pre>
<p>
      </p>
<p>
        The significant points about each of <code class="computeroutput"><span class="identifier">init_write</span><span class="special">()</span></code> and <code class="computeroutput"><span class="identifier">init_read</span><span class="special">()</span></code> are:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            The <code class="computeroutput"><span class="identifier">AsyncAPI</span></code> method only
            initiates the operation. It returns immediately, while the requested
            operation is still pending.
          </li>
<li class="listitem">
            The method accepts a callback. When the operation completes, the callback
            is called with relevant parameters (error code, data if applicable).
          </li>
</ul></div>
<p>
        We would like to wrap these asynchronous methods in functions that appear
        synchronous by blocking the calling fiber until the operation completes.
        This lets us use the wrapper function’s return value to deliver relevant data.
      </p>
<div class="tip"><table border="0" summary="Tip">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Tip]" src="../../../../../../doc/src/images/tip.png"></td>
<th align="left">Tip</th>
</tr>
<tr><td align="left" valign="top"><p>
          <a class="link" href="../synchronization/futures/promise.html#class_promise"><code class="computeroutput">promise&lt;&gt;</code></a> and <a class="link" href="../synchronization/futures/future.html#class_future"><code class="computeroutput">future&lt;&gt;</code></a> are your friends
          here.
        </p></td></tr>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2013 Oliver Kowalke<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../callbacks.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../callbacks.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="return_errorcode.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
