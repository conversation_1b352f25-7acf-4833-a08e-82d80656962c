[/
          Copyright <PERSON> 2013.
 Distributed under the Boost Software License, Version 1.0.
    (See accompanying file LICENSE_1_0.txt or copy at
          http://www.boost.org/LICENSE_1_0.txt
]

[#class_promise]
[section:promise Template `promise<>`]

A __promise__ provides a mechanism to store a value (or exception) that can
later be retrieved from the corresponding __future__ object. `promise<>` and
`future<>` communicate via their underlying [link shared_state shared state].

        #include <boost/fiber/future/promise.hpp>

        namespace boost {
        namespace fibers {

        template< typename R >
        class promise {
        public:
            promise();

            template< typename __Allocator__ >
            promise( __allocator_arg_t__, Allocator);

            promise( promise &&) noexcept;

            promise & operator=( promise &&) noexcept;

            promise( promise const&) = delete;

            promise & operator=( promise const&) = delete;

            ~promise();

            void swap( promise &) noexcept;

            future< R > get_future();

            void set_value( R const&);  // member only of generic promise template
            void set_value( R &&);      // member only of generic promise template
            void set_value( R &);       // member only of promise< R & > template
            void set_value();           // member only of promise< void > template

            void set_exception( std::exception_ptr p);
        };

        template< typename R >
        void swap( promise< R > &, promise< R > &) noexcept;

        }

[heading Default constructor]

        promise();

[variablelist
[[Effects:] [Creates a promise with an empty [link shared_state shared state].]]
[[Throws:] [Exceptions caused by memory allocation.]]
]

[heading Constructor]

        template< typename __Allocator__ >
        promise( __allocator_arg_t__, Allocator alloc);

[variablelist
[[Effects:] [Creates a promise with an empty [link shared_state shared state] by using `alloc`.]]
[[Throws:] [Exceptions caused by memory allocation.]]
[[See also:] [__allocator_arg_t__]]
]

[heading Move constructor]

        promise( promise && other) noexcept;

[variablelist
[[Effects:] [Creates a promise by moving the [link shared_state shared state] from `other`.]]
[[Postcondition:] [`other` contains no valid shared state.]]
[[Throws:] [Nothing.]]
]

[heading Destructor]

        ~promise();

[variablelist
[[Effects:] [Destroys `*this` and abandons the [link shared_state shared
state] if shared state is ready; otherwise stores __future_error__ with error
condition __broken_promise__ as if by [member_link promise..set_exception]:
the shared state is set ready.]]
]

[operator_heading promise..operator_assign..operator=]

        promise & operator=( promise && other) noexcept;

[variablelist
[[Effects:] [Transfers the ownership of [link shared_state shared state] to `*this`.]]
[[Postcondition:] [`other` contains no valid shared state.]]
[[Throws:] [Nothing.]]
]

[member_heading promise..swap]

        void swap( promise & other) noexcept;

[variablelist
[[Effects:] [Swaps the [link shared_state shared state] between other and `*this`.]]
[[Throws:] [Nothing.]]
]

[member_heading promise..get_future]

        future< R > get_future();

[variablelist
[[Returns:] [A __future__ with the same [link shared_state shared state].]]
[[Throws:] [__future_error__ with __already_retrieved__ or __no_state__.]]
]

[member_heading promise..set_value]

        void set_value( R const& value);  // member only of generic promise template
        void set_value( R && value);      // member only of generic promise template
        void set_value( R & value);       // member only of promise< R & > template
        void set_value();                 // member only of promise< void > template

[variablelist
[[Effects:] [Store the result in the [link shared_state shared state] and marks the state as ready.]]
[[Throws:] [__future_error__ with __already_satisfied__ or __no_state__.]]
]

[member_heading promise..set_exception]

        void set_exception( std::exception_ptr);

[variablelist
[[Effects:] [Store an exception pointer in the [link shared_state shared state] and marks the state as ready.]]
[[Throws:] [__future_error__ with __already_satisfied__ or __no_state__.]]
]

[function_heading_for swap..promise]

    template< typename R >
    void swap( promise< R > & l, promise< R > & r) noexcept;

[variablelist
[[Effects:] [Same as `l.swap( r)`.]]
]

[endsect]
