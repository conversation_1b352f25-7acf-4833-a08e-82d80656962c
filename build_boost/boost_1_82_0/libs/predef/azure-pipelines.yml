# Use, modification, and distribution are
# subject to the Boost Software License, Version 1.0. (See accompanying
# file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
#
# Copyright <PERSON> 2019-2021.

trigger:
  branches:
    include:
    - develop
    - master
    - feature/*
  paths:
    exclude:
    - .circleci/*
    - .cirrus.yml
    - .drone.star
    - .github/workflows/*
    - .semaphore/*
    - .travis.yml
    - appveyor.yml
pr:
  branches:
    include:
    - develop

variables:
  AZP: 1
  AZP_REPO_DIR: $(Build.Repository.LocalPath)
  AZP_BRANCH: $(Build.SourceBranch)
  AZP_BRANCH_NAME: $(Build.SourceBranchName)
  AZP_COMMIT: $(Build.SourceVersion)
  AZP_REPO: $(Build.Repository.Name)
  AZP_PULL_REQUEST: $(System.PullRequest.PullRequestNumber)

stages:

- stage: Test
  jobs:

  - job: 'Linux'
    strategy:
      matrix:
        GCC 11 (GNU): { CXX: g++-11, PACKAGES: g++-11, VM_IMAGE: 'ubuntu-20.04', TOOLSET: gcc, B2_ARGS: 'cxxstd=03,11,14,17,20 cxxstd-dialect=gnu' }
        GCC 11 (ISO): { CXX: g++-11, PACKAGES: g++-11, VM_IMAGE: 'ubuntu-20.04', TOOLSET: gcc, B2_ARGS: 'cxxstd=03,11,14,17,20 cxxstd-dialect=iso' }
        GCC 11: { CXX: g++-11, PACKAGES: g++-11, VM_IMAGE: 'ubuntu-20.04', TOOLSET: gcc }
        GCC 10: { CXX: g++-10, PACKAGES: g++-10, VM_IMAGE: 'ubuntu-20.04', TOOLSET: gcc }
        GCC 9: { CXX: g++-9, PACKAGES: g++-9, VM_IMAGE: 'ubuntu-18.04', TOOLSET: gcc }
        GCC 8: { CXX: g++-8, PACKAGES: g++-8, VM_IMAGE: 'ubuntu-18.04', TOOLSET: gcc }
        GCC 7: { CXX: g++-7, PACKAGES: g++-7, VM_IMAGE: 'ubuntu-18.04', TOOLSET: gcc }
        GCC 6: { CXX: g++-6, PACKAGES: g++-6, VM_IMAGE: 'ubuntu-18.04', TOOLSET: gcc }
        GCC 5: { CXX: g++-5, PACKAGES: g++-5, VM_IMAGE: 'ubuntu-18.04', TOOLSET: gcc }
        Clang 13: { CXX: clang++-13, PACKAGES: clang-13, LLVM_OS: focal, LLVM_VER: 13, VM_IMAGE: 'ubuntu-20.04', TOOLSET: clang }
        Clang 12: { CXX: clang++-12, PACKAGES: clang-12, LLVM_OS: focal, LLVM_VER: 12, VM_IMAGE: 'ubuntu-20.04', TOOLSET: clang }
        Clang 11: { CXX: clang++-11, PACKAGES: clang-11, LLVM_OS: focal, LLVM_VER: 11, VM_IMAGE: 'ubuntu-20.04', TOOLSET: clang }
        Clang 10: { CXX: clang++-10, PACKAGES: clang-10, LLVM_OS: bionic, LLVM_VER: 10, VM_IMAGE: 'ubuntu-18.04', TOOLSET: clang }
        Clang 9: { CXX: clang++-9, PACKAGES: clang-9, LLVM_OS: bionic, LLVM_VER: 9, VM_IMAGE: 'ubuntu-18.04', TOOLSET: clang }
        Clang 8: { CXX: clang++-8, PACKAGES: clang-8, LLVM_OS: bionic, LLVM_VER: 8, VM_IMAGE: 'ubuntu-18.04', TOOLSET: clang }
        Clang 7: { CXX: clang++-7, PACKAGES: clang-7, LLVM_OS: bionic, LLVM_VER: 7, VM_IMAGE: 'ubuntu-18.04', TOOLSET: clang }
        Clang 6.0: { CXX: clang++-6.0, PACKAGES: clang-6.0, LLVM_OS: bionic, LLVM_VER: 6.0, VM_IMAGE: 'ubuntu-18.04', TOOLSET: clang }
        Clang 5.0: { CXX: clang++-5.0, PACKAGES: clang-5.0, LLVM_OS: bionic, LLVM_VER: 5.0, VM_IMAGE: 'ubuntu-18.04', TOOLSET: clang }
        Clang 4.0: { CXX: clang++-4.0, PACKAGES: clang-4.0, LLVM_OS: xenial, LLVM_VER: 4.0, VM_IMAGE: 'ubuntu-18.04', TOOLSET: clang }
    pool:
      vmImage: $(VM_IMAGE)
    steps:
    - bash: |
        set -e
        uname -a
        ./tools/ci/linux-cxx-install.sh
      displayName: 'Install CXX'
    - bash: |
        set -e
        pushd ${HOME}
        wget -nv https://github.com/bfgroup/b2/archive/release.tar.gz
        tar -zxf release.tar.gz
        cd b2-release
        CXX= ./bootstrap.sh
        sudo ./b2 install
        popd
      displayName: 'Install B2'
    - bash: |
        set -e
        CXX_PATH=`which ${CXX}`
        echo "using ${TOOLSET} : : ${CXX_PATH} ;" > ${HOME}/user-config.jam
        pushd test
        b2 --verbose-test -a toolset=${TOOLSET} ${B2_ARGS} predef-info
        b2 --debug-configuration -a toolset=${TOOLSET} ${B2_ARGS} predef predef-headers
        popd
      displayName: Test

  - job: 'macOS'
    strategy:
      matrix:
        Xcode 13.2.1: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_13.2.1.app, VM_IMAGE: 'macOS-11'}
        Xcode 13.1: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_13.1.app, VM_IMAGE: 'macOS-11'}
        Xcode 13.0: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_13.0.app, VM_IMAGE: 'macOS-11'}
        Xcode 12.4: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_12.4.app, VM_IMAGE: 'macOS-11'}
        Xcode 12.3: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_12.3.app, VM_IMAGE: 'macOS-10.15'}
        Xcode 12.2: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_12.2.app, VM_IMAGE: 'macOS-10.15'}
        Xcode 12.1.1: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_12.1.1.app, VM_IMAGE: 'macOS-10.15'}
        Xcode 12.0.1: {TOOLSET: clang,  CXX: clang++, XCODE_APP: /Applications/Xcode_12.0.1.app, VM_IMAGE: 'macOS-10.15'}
        Xcode 11.7: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_11.7.app, VM_IMAGE: 'macOS-10.15'}
        Xcode 11.6: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_11.6.app, VM_IMAGE: 'macOS-10.15'}
        Xcode 11.5: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_11.5.app, VM_IMAGE: 'macOS-10.15'}
        Xcode 11.4.1: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_11.4.1.app, VM_IMAGE: 'macOS-10.15'}
        Xcode 11.3.1: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_11.3.1.app, VM_IMAGE: 'macOS-10.15'}
        Xcode 11.2.1: {TOOLSET: clang, CXX: clang++, XCODE_APP: /Applications/Xcode_11.2.1.app, VM_IMAGE: 'macOS-10.15'}
    pool:
      vmImage: $(VM_IMAGE)
    steps:
    - bash: |
        set -e
        uname -a
        sudo xcode-select -switch ${XCODE_APP}
        which clang++
        clang++ --version
      displayName: 'Install CXX'
    - bash: |
        set -e
        pushd ${HOME}
        wget -nv https://github.com/bfgroup/b2/archive/release.tar.gz
        tar -zxf release.tar.gz
        cd b2-release
        CXX= ./bootstrap.sh
        sudo ./b2 install
        popd
      displayName: 'Install B2'
    - bash: |
        set -e
        CXX_PATH=`which ${CXX}`
        echo "using ${TOOLSET} : : ${CXX_PATH} ;" > ${HOME}/user-config.jam
        pushd test
        b2 --verbose-test -a toolset=${TOOLSET} ${B2_ARGS} predef-info
        b2 --debug-configuration -a toolset=${TOOLSET} ${B2_ARGS} predef predef-headers
        popd
      displayName: Test

  - job: 'Windows'
    strategy:
      matrix:
        VS 2019:
          TOOLSET: msvc
          TOOLSET_VERSION: 14.2
          B2_ARGS: address-model=32,64 cxxstd=14,latest
          VM_IMAGE: 'windows-2019'
        VS 2019 (UWP DESKTOP):
          TOOLSET: msvc
          TOOLSET_VERSION: 14.2
          B2_ARGS: address-model=64 define=_WIN32_WINNT=0x0A00 define=WINAPI_FAMILY=WINAPI_FAMILY_DESKTOP_APP
          VM_IMAGE: 'windows-2019'
        VS 2019 (UWP PHONE):
          TOOLSET: msvc
          TOOLSET_VERSION: 14.2
          B2_ARGS: address-model=64 define=_WIN32_WINNT=0x0A00 define=WINAPI_FAMILY=WINAPI_FAMILY_PHONE_APP
          VM_IMAGE: 'windows-2019'
        VS 2019 (UWP STORE):
          TOOLSET: msvc
          TOOLSET_VERSION: 14.2
          B2_ARGS: address-model=64 define=_WIN32_WINNT=0x0A00 define=WINAPI_FAMILY=WINAPI_FAMILY_PC_APP
          VM_IMAGE: 'windows-2019'
        VS 2019 (UWP SERVER):
          TOOLSET: msvc
          TOOLSET_VERSION: 14.2
          B2_ARGS: address-model=64 define=_WIN32_WINNT=0x0A00 define=WINAPI_FAMILY=WINAPI_FAMILY_SERVER
          VM_IMAGE: 'windows-2019'
        VS 2019 (UWP SYSTEM):
          TOOLSET: msvc
          TOOLSET_VERSION: 14.2
          B2_ARGS: address-model=64 define=_WIN32_WINNT=0x0A00 define=WINAPI_FAMILY=WINAPI_FAMILY_SYSTEM
          VM_IMAGE: 'windows-2019'
        VS 2022:
          TOOLSET: msvc
          TOOLSET_VERSION: 14.3
          VM_IMAGE: 'windows-2022'
        MinGW 8.1.0:
          TOOLSET: gcc
          VM_IMAGE: 'windows-2019'
        MinGW 8.1.0 (UWP DESKTOP):
          TOOLSET: gcc
          B2_ARGS: define=_WIN32_WINNT=0x0A00 define=WINAPI_FAMILY=WINAPI_FAMILY_DESKTOP_APP
          VM_IMAGE: 'windows-2019'
        MinGW 8.1.0 (UWP STORE):
          TOOLSET: gcc
          B2_ARGS: define=_WIN32_WINNT=0x0A00 define=WINAPI_FAMILY=WINAPI_FAMILY_PC_APP
          VM_IMAGE: 'windows-2019'
    pool:
      vmImage: $(VM_IMAGE)
    steps:
    - script: |
        cd %BUILD_SOURCESDIRECTORY%/..
        curl "https://github.com/bfgroup/b2/archive/release.zip" -L -o b2.zip
      displayName: Download B2
    - task: ExtractFiles@1
      inputs:
        archiveFilePatterns: $(Build.SourcesDirectory)/../b2.zip
        destinationFolder: $(Build.SourcesDirectory)/..
        cleanDestinationFolder: false
      displayName: Extract B2
    - script: |
        echo using %TOOLSET% ^: %TOOLSET_VERSION% ^; > %HOMEDRIVE%%HOMEPATH%/user-config.jam
        cd %BUILD_SOURCESDIRECTORY%/../b2-release
        cmd.exe /c bootstrap.bat
      displayName: Install B2
    - script: |
        set BOOST_BUILD_PATH=%BUILD_SOURCESDIRECTORY%/../b2-release
        cd %BUILD_SOURCESDIRECTORY%/test
        %BUILD_SOURCESDIRECTORY%/../b2-release/b2.exe --verbose-test -a toolset=%TOOLSET% %B2_ARGS% predef-info
        %BUILD_SOURCESDIRECTORY%/../b2-release/b2.exe --debug-configuration -a toolset=%TOOLSET% %B2_ARGS% predef predef-headers
      displayName: Test

- stage: WebsiteUpdate
  displayName: 'Website Update'
  condition: and(in(variables['Build.SourceBranch'], 'refs/heads/master', 'refs/heads/develop'), eq(variables['Build.Repository.Name'], 'grafikrobot/hash-predef'))
  jobs:

  - job: Documentation
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '2.x'
    - task: UseRubyVersion@0
    - bash: |
        gem install asciidoctor
        gem install rouge
      displayName: 'Install Doc Tools'
    - bash: |
        set -e
        pushd ${HOME}
        wget -nv https://github.com/bfgroup/b2/archive/release.tar.gz
        tar -zxf release.tar.gz
        cd b2-release
        CXX= ./bootstrap.sh
        sudo ./b2 install
        popd
      displayName: 'Install B2'
    - bash: |
        rm -rf ${WEB_DIR}
        git clone --verbose --branch gh-pages --depth 1 "https://${GH_TOKEN}github.com/grafikrobot/hash-predef.git" ${WEB_DIR} || exit 1
        pushd ${WEB_DIR}
        git rm --ignore-unmatch -r "${DOC_DIR}" || exit 1
        mkdir -p "${DOC_DIR}" || exit 1
        popd
      displayName: 'Clone Website'
      env:
        GH_TOKEN: $(GitHubToken)
        WEB_DIR: website
        DOC_DIR: manual/$(Build.SourceBranchName)
    - bash: |
        echo "using asciidoctor ;" > ${HOME}/user-config.jam
        pushd doc
        b2 --doc-dir=${WEB_DIR}/${DOC_DIR} html
        git config user.email "doc-bot"
        git config user.name "doc-bot"
        cd ${WEB_DIR}/${DOC_DIR}
        git add --verbose . || exit 1
        git commit -m "Update documentation."
        git push
        popd
      displayName: 'Build & Publish'
      env:
        GH_TOKEN: $(GitHubToken)
        WEB_DIR: website
        DOC_DIR: manual/$(Build.SourceBranchName)
