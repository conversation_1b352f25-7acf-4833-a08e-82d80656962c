# [Boost.Stacktrace](https://boost.org/libs/stacktrace)

Library for storing and printing backtraces.

Boost.Stacktrace is a part of the [Boost C++ Libraries](https://github.com/boostorg).


### Test results
@               | Build         | Tests coverage | More info
----------------|-------------- | -------------- |-----------
Develop branch:  | [![CI](https://github.com/boostorg/stacktrace/actions/workflows/ci.yml/badge.svg?branch=develop)](https://github.com/boostorg/stacktrace/actions/workflows/ci.yml) [![Build status](https://ci.appveyor.com/api/projects/status/l3aak4j8k39rx08t/branch/develop?svg=true)](https://ci.appveyor.com/project/apolukhin/stacktrace/branch/develop) | [![Coverage Status](https://coveralls.io/repos/github/boostorg/stacktrace/badge.svg?branch=develop)](https://coveralls.io/github/boostorg/stacktrace?branch=develop) | [details...](https://www.boost.org/development/tests/develop/developer/stacktrace.html)
Master branch:  | [![CI](https://github.com/boostorg/stacktrace/actions/workflows/ci.yml/badge.svg?branch=master)](https://github.com/boostorg/stacktrace/actions/workflows/ci.yml) [![Build status](https://ci.appveyor.com/api/projects/status/l3aak4j8k39rx08t/branch/master?svg=true)](https://ci.appveyor.com/project/apolukhin/stacktrace/branch/master) | [![Coverage Status](https://coveralls.io/repos/github/boostorg/stacktrace/badge.svg?branch=master)](https://coveralls.io/github/boostorg/stacktrace?branch=master) | [details...](https://www.boost.org/development/tests/master/developer/stacktrace.html)

[Latest developer documentation](https://www.boost.org/doc/libs/develop/doc/html/stacktrace.html)

### License
Distributed under the [Boost Software License, Version 1.0](https://boost.org/LICENSE_1_0.txt).
