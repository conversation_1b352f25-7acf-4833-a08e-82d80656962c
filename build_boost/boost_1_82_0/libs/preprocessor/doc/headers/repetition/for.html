<html>
<head>
	<title>repetition/for.hpp</title>
	<link rel="stylesheet" type="text/css" href="../../styles.css">
</head>
<body>
	<div style="margin-left:  0px;">
		The <b>repetition/for.hpp</b> header defines a generalized horizontal repetition construct.
	</div>
	<h4>Usage</h4>
		<div class="code">
			#include <b>&lt;boost/preprocessor/repetition/for.hpp&gt;</b>
		</div>
	<h4>Contents</h4>
		<ul>
			<li><a href="../../ref/for.html">BOOST_PP_FOR</a></li>
			<li><a href="../../ref/for_r.html">BOOST_PP_FOR_<i>r</i></a></li>
		</ul>
	<hr size="1">
	<div style="margin-left: 0px;">
		<i>� Copyright <a href="http://www.housemarque.com" target="_top"><PERSON><PERSON><PERSON></a> 2002</i>
		</br><i>� Copyright <PERSON> 2002</i>
	</div>
	<div style="margin-left: 0px;">
		<p><small>Distributed under the Boost Software License, Version 1.0. (See
		accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
		copy at <a href=
		"http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
	</div>
</body>
</html>
