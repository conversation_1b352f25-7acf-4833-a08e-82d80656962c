<html>
	<head>
		<title>seq/fold_left.hpp</title>
		<link rel="stylesheet" type="text/css" href="../../styles.css">
	</head>
	<body>
		<div style="margin-left:  0px;">
			The <b>seq/fold_left.hpp</b> header defines macros for folding (or 
			accumulating) a <i>seq</i> left-to-right.
		</div>
		<h4>
			Usage
		</h4>
		<div class="code">
			#include <b>&lt;boost/preprocessor/seq/fold_left.hpp&gt;</b>
		</div>
		<h4>
			Contents
		</h4>
		<ul>
			<li>
				<a href="../../ref/seq_fold_left.html">BOOST_PP_SEQ_FOLD_LEFT</a></li>
			<li>
				<a href="../../ref/seq_fold_left_s.html">BOOST_PP_SEQ_FOLD_LEFT_<i>s</i></a></li>
		</ul>
	<hr size="1">
	<div style="margin-left: 0px;">
		<i>� Copyright <a href="http://www.housemarque.com" target="_top">Housemarque Oy</a> 2002</i>
		</br><i>� Copyright Paul Mensonides 2002</i>
	</div>
	<div style="margin-left: 0px;">
		<p><small>Distributed under the Boost Software License, Version 1.0. (See
		accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
		copy at <a href=
		"http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
	</div>
	</body>
</html>
