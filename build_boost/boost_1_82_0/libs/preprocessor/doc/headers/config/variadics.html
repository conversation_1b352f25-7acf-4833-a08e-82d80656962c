<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>

    <meta http-equiv="content-type" content="text/html; charset=windows-1252">
    <title>config/config.hpp</title>
    <link rel="stylesheet" type="text/css" href="../../styles.css">
  </head>
  <body>
    <div style="margin-left: 0px;"> The <b>config/config.hpp</b>
      header defines internal configuration macros as well as a macro
      for determining whether the compiler's preprocessor conforms to
      the standard. The file is automatically included whenever any
      Boost PP macros are used.The end-user can manually include it
      in order to use the BOOST_PP_IS_STANDARD macro, but normally
      should never have to do so.</div>
    <h4> Usage </h4>
    <div class="code"> #include <b>&lt;boost/preprocessor/config/config.hpp&gt;</b>
    </div>
    <h4> Contents </h4>
    <ul>
      <li><a href="../../ref/is_standard.html">BOOST_PP_IS_STANDARD</a><br>
      </li>
    </ul>
    <hr size="1">
    <div style="margin-left: 0px;"><i>© Copyright Edward Diener
        2011,2014,2020</i> </div>
    <div style="margin-left: 0px;">
      <p><small>Distributed under the Boost Software License, Version
          1.0. (See accompanying file <a
            href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
          copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
    </div>
  </body>
</html>
