<html>
<head>
    <title>config/limits.hpp</title>
    <link rel="stylesheet" type="text/css" href="../../styles.css">
</head>
<body>
	<div style="margin-left: 0px;">
		The <b>config/limits.hpp</b> header defines various library limits.
	</div>
	<h4>
		Usage
	</h4>
	<div class="code">
		#include <b>&lt;boost/preprocessor/config/limits.hpp&gt;</b>
	</div>
	<h4>
		Contents
	</h4>
	<ul>
		<li><a href="../../ref/limit_dim.html">BOOST_PP_LIMIT_DIM</a></li>
		<li><a href="../../ref/limit_for.html">BOOST_PP_LIMIT_FOR</a></li>
		<li><a href="../../ref/limit_iteration.html">BOOST_PP_LIMIT_ITERATION</a></li>
		<li><a href="../../ref/limit_iteration_dim.html">BOOST_PP_LIMIT_ITERATION_DIM</a></li>
		<li><a href="../../ref/limit_mag.html">BOOST_PP_LIMIT_MAG</a></li>
		<li><a href="../../ref/limit_repeat.html">BOOST_PP_LIMIT_REPEAT</a></li>
		<li><a href="../../ref/limit_seq.html">BOOST_PP_LIMIT_SEQ</a></li>
		<li><a href="../../ref/limit_slot_count.html">BOOST_PP_LIMIT_SLOT_COUNT</a></li>
		<li><a href="../../ref/limit_slot_sig.html">BOOST_PP_LIMIT_SLOT_SIG</a></li>
		<li><a href="../../ref/limit_tuple.html">BOOST_PP_LIMIT_TUPLE</a></li>
		<li><a href="../../ref/limit_while.html">BOOST_PP_LIMIT_WHILE</a></li>
        <li><a href="../../ref/limit_variadic.html">BOOST_PP_LIMIT_VARIADIC</a></li>
	</ul>
	<hr size="1">
	<div style="margin-left: 0px;">
		<i>� Copyright <a href="http://www.housemarque.com" target="_top">Housemarque Oy</a> 2002</i>
		<br><i>� Copyright Paul Mensonides 2002</i>
	</div>
	<div style="margin-left: 0px;">
		<p><small>Distributed under the Boost Software License, Version 1.0. (See
		accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
		copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
	</div>
</body>
</html>
