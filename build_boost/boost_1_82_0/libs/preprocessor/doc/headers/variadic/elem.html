<html>
  <head>
    <title>variadic/elem.hpp
    </title>
    <link rel="stylesheet" type="text/css" href="../../styles.css">
  </head>
  <body>
  <div style="margin-left: 0px;">
    The <b>variadic/elem.hpp</b> header defines a variadic macro to extract an element from <i>variadic data</i>.
  </div>
  				<h4>Usage</h4>
  						<div class="code">
  									#include <b>&lt;boost/preprocessor/variadic/elem.hpp&gt;</b>
  											</div>
  												<h4>Contents</h4>
  														<ul>
			<li><a href="../../ref/variadic_elem.html">BOOST_PP_VARIADIC_ELEM</a> <a href="../../topics/variadic_macros.html#VNotation" target="_self">(v)</a><br>
</li>
		</ul>
	<hr size="1">
	<div style="margin-left: 0px;">
		<i></i><i>� Copyright <PERSON> 2011,2013</i>
	</div>
	<div style="margin-left: 0px;">
		<p><small>Distributed under the Boost Software License, Version 1.0. (See
		accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
		copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
	</div>
</body></html>
