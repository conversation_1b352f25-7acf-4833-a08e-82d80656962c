<html><head>
  <title>variadic/has_opt.hpp</title>
  <link rel="stylesheet" type="text/css" href="../../styles.css"></head><body>
   <div style="margin-left: 0px;">
   The <b>variadic/has_opt.hpp</b> header defines a macro that determines whether or not the __VA_OPT__ construct is supported.
   </div>
   <h4>Usage</h4>
   <div class="code">
   #include <b>&lt;boost/preprocessor/variadic/has_opt.hpp&gt;</b>
   </div>
   <h4>Contents</h4>
   <ul>
   <li><a href="../../ref/variadic_has_opt.html">BOOST_PP_VARIADIC_HAS_OPT</a> <br>
   </li>
   </ul>
   <hr size="1">
   <div style="margin-left: 0px;">
   <i></i><i>� Copyright <PERSON> 2019</i>
   </div>
   <div style="margin-left: 0px;">
   <p><small>Distributed under the Boost Software License, Version 1.0. (See
   accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
   copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
   </div>
   </body></html>