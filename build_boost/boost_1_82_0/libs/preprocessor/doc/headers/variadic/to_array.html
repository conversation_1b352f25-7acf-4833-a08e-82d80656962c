<html><head>
  <title>variadic/to_array.hpp</title>
    <link rel="stylesheet" type="text/css" href="../../styles.css"></head><body>
    <div style="margin-left: 0px;">
        The <b>variadic/to_array.hpp</b> header defines a variadic macro that converts <i>variadic data</i> to an <i>array</i>.
    </div>
    <h4>Usage</h4>
        <div class="code">
            #include <b>&lt;boost/preprocessor/variadic/to_array.hpp&gt;</b>
        </div>
    <h4>Contents</h4>
        <ul>
            <li><a href="../../ref/variadic_to_array.html">BOOST_PP_VARIADIC_TO_ARRAY</a> <a href="../../topics/variadic_macros.html#VNotation" target="_self">(v)</a><br>
</li>
        </ul>
    <hr size="1">
    <div style="margin-left: 0px;">
        <i></i><i>� Copyright <PERSON> 2011,2013</i>
    </div>
    <div style="margin-left: 0px;">
        <p><small>Distributed under the Boost Software License, Version 1.0. (See
        accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
        copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
    </div>
</body></html>