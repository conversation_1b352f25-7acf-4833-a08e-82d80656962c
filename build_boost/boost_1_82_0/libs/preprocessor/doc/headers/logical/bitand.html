<html>
<head>
	<title>logical/bitand.hpp</title>
	<link rel="stylesheet" type="text/css" href="../../styles.css">
</head>
<body>
	<div style="margin-left:  0px;">
		The <b>logical/bitand.hpp</b> header defines a macro that expands to the bitwise <i>AND</i> of its operands.
	</div>
	<h4>Usage</h4>
		<div class="code">
			#include <b>&lt;boost/preprocessor/logical/bitand.hpp&gt;</b>
		</div>
	<h4>Contents</h4>
		<ul>
			<li><a href="../../ref/bitand.html">BOOST_PP_BITAND</a></li>
		</ul>
	<hr size="1">
	<div style="margin-left: 0px;">
		<i>� Copyright <a href="http://www.housemarque.com" target="_top">Housemarque Oy</a> 2002</i>
		</br><i>� Copyright <PERSON> 2002</i>
	</div>
	<div style="margin-left: 0px;">
		<p><small>Distributed under the Boost Software License, Version 1.0. (See
		accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
		copy at <a href=
		"http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
	</div>
</body>
</html>
