<html>
  <head>
    <meta content="text/html; charset=windows-1252" http-equiv="content-type">
    <title>tuple/pop_front.hpp</title>
    <link rel="stylesheet" type="text/css" href="../../styles.css">
  </head>
  <body>
    <div style="margin-left:  0px;"> The <b>tuple/pop_front.hpp</b> header
      defines macros to pop an element from the beginning of an <i>tuple</i>.</div>
    <h4>Usage</h4>
    <div class="code"> #include <b>&lt;boost/preprocessor/tuple/pop_front.hpp&gt;</b>
    </div>
    <h4>Contents</h4>
    <ul>
      <li><a href="../../ref/tuple_pop_front.html">BOOST_PP_TUPLE_POP_FRONT</a>
        <a href="../../topics/variadic_macros.html#VNotation">(v)</a></li>
      <li><a href="../../ref/tuple_pop_front_z.html">BOOST_PP_TUPLE_POP_FRONT_Z</a>
        <a href="../../topics/variadic_macros.html#VNotation">(v)</a></li>
    </ul>
    <hr size="1">
    <div style="margin-left: 0px;"> <i>© Copyright Edward Diener 2013</i> </div>
    <div style="margin-left: 0px;">
      <p><small>Distributed under the Boost Software License, Version 1.0. (See
          accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a>
          or copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
    </div>
  </body>
</html>
