<html><head>
  <title>list/to_seq.hpp</title>
	<link rel="stylesheet" type="text/css" href="../../styles.css"></head><body>
	<div style="margin-left: 0px;">
		The <b>list/to_seq.hpp</b> header defines a macro to convert a <i>list</i> to a <i>seq</i>.
	</div>
	<h4>Usage</h4>
		<div class="code">
			#include <b>&lt;boost/preprocessor/list/to_seq.hpp&gt;</b>
		</div>
	<h4>Contents</h4>
		<ul>
			<li><a href="../../ref/list_to_seq.html">BOOST_PP_LIST_TO_SEQ</a></li>
			<li><a href="../../ref/list_to_seq.html">BOOST_PP_LIST_TO_SEQ_R</a></li>
		</ul>
	<hr size="1">
	<div style="margin-left: 0px;">
		<i></i><i>© Copyright <PERSON> 2011</i>
	</div>
	<div style="margin-left: 0px;">
		<p><small>Distributed under the Boost Software License, Version 1.0. (See
		accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
		copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
	</div>
</body></html>
