<html>
<head>
	<title>list/for_each_i.hpp</title>
	<link rel="stylesheet" type="text/css" href="../../styles.css">
</head>
<body>
	<div style="margin-left:  0px;">
		The <b>list/for_each_i.hpp</b> header defines macros to repeat a macro for each element in a <i>list</i>.
	</div>
	<h4>Usage</h4>
		<div class="code">
			#include <b>&lt;boost/preprocessor/list/for_each_i.hpp&gt;</b>
		</div>
	<h4>Contents</h4>
		<ul>
			<li><a href="../../ref/list_for_each_i.html">BOOST_PP_LIST_FOR_EACH_I</a></li>
			<li><a href="../../ref/list_for_each_i_r.html">BOOST_PP_LIST_FOR_EACH_I_R</a></li>
		</ul>
	<hr size="1">
	<div style="margin-left: 0px;">
		<i>� Copyright <a href="http://www.housemarque.com" target="_top">Housemarque Oy</a> 2002</i>
		</br><i>� Copyright <PERSON>sonides 2002</i>
	</div>
	<div style="margin-left: 0px;">
		<p><small>Distributed under the Boost Software License, Version 1.0. (See
		accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
		copy at <a href=
		"http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
	</div>
</body>
</html>
