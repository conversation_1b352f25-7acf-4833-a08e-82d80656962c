<html>
  <head>
    <title>facilities/overload.hpp</title>
	<link rel="stylesheet" type="text/css" href="../../styles.css">
  </head>
  <body>
	<div style="margin-left: 0px;">
		The <b>facilities/overload.hpp</b> header defines variadic macros that overload non-variadic macros to accept different numbers of parameters.
	</div>
	<h4>Usage</h4>
		<div class="code">
			#include <b>&lt;boost/preprocessor/facilities/overload.hpp&gt;</b>
		</div>
	<h4>Contents</h4>
		<ul>
			<li><a href="../../ref/overload.html">BOOST_PP_OVERLOAD</a> <a href="../../topics/variadic_macros.html#VNotation" target="_self">(v)</a></li>
		</ul>
	<hr size="1">
	<div style="margin-left: 0px;">
		<i></i><i>� Copyright <PERSON> 2011,2013</i>
	</div>
	<div style="margin-left: 0px;">
		<p><small>Distributed under the Boost Software License, Version 1.0. (See
		accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
		copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
	</div>
  </body>
</html>