<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="content-type" content="text/html;
      charset=windows-1252">
    <title>facilities/va_opt.hpp</title>
    <link rel="stylesheet" type="text/css" href="../../styles.css">
  </head>
  <body>
    <div style="margin-left: 0px;"> The <b>facilities/va_opt.hpp</b>
      header defines a variadic macro for the C++20 level that offers a
      more flexible alternative to the __VA_OPT__ construct. </div>
    <h4>Usage</h4>
    <div class="code"> #include <b>&lt;boost/preprocessor/facilities/va_opt.hpp&gt;</b>
    </div>
    <h4>Contents</h4>
    <ul>
      <li><a href="../../ref/va_opt.html">BOOST_PP_VA_OPT</a> <a
          href="../../topics/variadic_macros.html#VNotation"
          target="_self">(v)</a></li>
    </ul>
    <hr size="1">
    <div style="margin-left: 0px;"> <i>© Copyright Edward Diener 2019</i>
    </div>
    <div style="margin-left: 0px;">
      <p><small>Distributed under the Boost Software License, Version
          1.0. (See accompanying file <a
            href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
          copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
    </div>
  </body>
</html>
