<html>
  <head>
    <meta content="text/html; charset=windows-1252" http-equiv="content-type">
    <title>punctuation/remove_parens.hpp</title>
    <link rel="stylesheet" type="text/css" href="../../styles.css">
  </head>
  <body>
    <div style="margin-left:  0px;"> The <b>punctuation/remove_parens.hpp</b>
      header defines a macro that removes the beginning parenthesis from its
      input if it exists.</div>
    <h4>Usage</h4>
    <div class="code"> #include <b>&lt;boost/preprocessor/punctuation/remove_parens.hpp&gt;</b>
    </div>
    <h4>Contents</h4>
    <ul>
      <li><a href="../../ref/remove_parens.html">BOOST_PP_REMOVE_PARENS</a></li>
    </ul>
    <hr size="1">
    <div style="margin-left: 0px;"> <i>© Copyright <PERSON> 2014</i> </div>
    <div style="margin-left: 0px;">
      <p><small>Distributed under the Boost Software License, Version 1.0. (See
          accompanying file <a href="../../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a>
          or copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
    </div>
  </body>
</html>
