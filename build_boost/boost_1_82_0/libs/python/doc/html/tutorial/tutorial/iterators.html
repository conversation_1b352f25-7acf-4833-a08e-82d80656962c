<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Iterators</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Boost.Python Tutorial">
<link rel="up" href="../index.html" title="Boost.Python Tutorial">
<link rel="prev" href="embedding.html" title="Embedding">
<link rel="next" href="exception.html" title="Exception Translation">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="" width="" height="" src="../../images/boost.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="embedding.html"><img src="../../images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../images/home.png" alt="Home"></a><a accesskey="n" href="exception.html"><img src="../../images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="tutorial.iterators"></a><a class="link" href="iterators.html" title="Iterators">Iterators</a>
</h2></div></div></div>
<p>
      In C++, and STL in particular, we see iterators everywhere. Python also has
      iterators, but these are two very different beasts.
    </p>
<p>
      <span class="bold"><strong>C++ iterators:</strong></span>
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          C++ has 5 type categories (random-access, bidirectional, forward, input,
          output)
        </li>
<li class="listitem">
          There are 2 Operation categories: reposition, access
        </li>
<li class="listitem">
          A pair of iterators is needed to represent a (first/last) range.
        </li>
</ul></div>
<p>
      <span class="bold"><strong>Python Iterators:</strong></span>
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          1 category (forward)
        </li>
<li class="listitem">
          1 operation category (next())
        </li>
<li class="listitem">
          Raises StopIteration exception at end
        </li>
</ul></div>
<p>
      The typical Python iteration protocol: <code class="literal"><span class="bold"><strong>for y
      in x...</strong></span></code> is as follows:
    </p>
<pre class="programlisting"><span class="identifier">iter</span> <span class="special">=</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">__iter__</span><span class="special">()</span>         <span class="comment"># get iterator</span>
<span class="keyword">try</span><span class="special">:</span>
    <span class="keyword">while</span> <span class="number">1</span><span class="special">:</span>
    <span class="identifier">y</span> <span class="special">=</span> <span class="identifier">iter</span><span class="special">.</span><span class="identifier">next</span><span class="special">()</span>         <span class="comment"># get each item</span>
    <span class="special">...</span>                     <span class="comment"># process y</span>
<span class="keyword">except</span> <span class="identifier">StopIteration</span><span class="special">:</span> <span class="keyword">pass</span>  <span class="comment"># iterator exhausted</span>
</pre>
<p>
      Boost.Python provides some mechanisms to make C++ iterators play along nicely
      as Python iterators. What we need to do is to produce appropriate <code class="computeroutput"><span class="identifier">__iter__</span></code> function from C++ iterators that
      is compatible with the Python iteration protocol. For example:
    </p>
<pre class="programlisting"><span class="identifier">object</span> <span class="identifier">get_iterator</span> <span class="special">=</span> <span class="identifier">iterator</span><span class="special">&lt;</span><span class="identifier">vector</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;();</span>
<span class="identifier">object</span> <span class="identifier">iter</span> <span class="special">=</span> <span class="identifier">get_iterator</span><span class="special">(</span><span class="identifier">v</span><span class="special">);</span>
<span class="identifier">object</span> <span class="identifier">first</span> <span class="special">=</span> <span class="identifier">iter</span><span class="special">.</span><span class="identifier">next</span><span class="special">();</span>
</pre>
<p>
      Or for use in class_&lt;&gt;:
    </p>
<pre class="programlisting"><span class="special">.</span><span class="identifier">def</span><span class="special">(</span><span class="string">"__iter__"</span><span class="special">,</span> <span class="identifier">iterator</span><span class="special">&lt;</span><span class="identifier">vector</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;())</span>
</pre>
<p>
      <span class="bold"><strong>range</strong></span>
    </p>
<p>
      We can create a Python savvy iterator using the range function:
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          range(start, finish)
        </li>
<li class="listitem">
          range&lt;Policies,Target&gt;(start, finish)
        </li>
</ul></div>
<p>
      Here, start/finish may be one of:
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          member data pointers
        </li>
<li class="listitem">
          member function pointers
        </li>
<li class="listitem">
          adaptable function object (use Target parameter)
        </li>
</ul></div>
<p>
      <span class="bold"><strong>iterator</strong></span>
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
          iterator&lt;T, Policies&gt;()
        </li></ul></div>
<p>
      Given a container <code class="literal">T</code>, iterator is a shortcut that simply
      calls <code class="literal">range</code> with &amp;T::begin, &amp;T::end.
    </p>
<p>
      Let's put this into action... Here's an example from some hypothetical bogon
      Particle accelerator code:
    </p>
<pre class="programlisting"><span class="identifier">f</span> <span class="special">=</span> <span class="identifier">Field</span><span class="special">()</span>
<span class="keyword">for</span> <span class="identifier">x</span> <span class="keyword">in</span> <span class="identifier">f</span><span class="special">.</span><span class="identifier">pions</span><span class="special">:</span>
    <span class="identifier">smash</span><span class="special">(</span><span class="identifier">x</span><span class="special">)</span>
<span class="keyword">for</span> <span class="identifier">y</span> <span class="keyword">in</span> <span class="identifier">f</span><span class="special">.</span><span class="identifier">bogons</span><span class="special">:</span>
    <span class="identifier">count</span><span class="special">(</span><span class="identifier">y</span><span class="special">)</span>
</pre>
<p>
      Now, our C++ Wrapper:
    </p>
<pre class="programlisting"><span class="identifier">class_</span><span class="special">&lt;</span><span class="identifier">F</span><span class="special">&gt;(</span><span class="string">"Field"</span><span class="special">)</span>
    <span class="special">.</span><span class="identifier">property</span><span class="special">(</span><span class="string">"pions"</span><span class="special">,</span> <span class="identifier">range</span><span class="special">(&amp;</span><span class="identifier">F</span><span class="special">::</span><span class="identifier">p_begin</span><span class="special">,</span> <span class="special">&amp;</span><span class="identifier">F</span><span class="special">::</span><span class="identifier">p_end</span><span class="special">))</span>
    <span class="special">.</span><span class="identifier">property</span><span class="special">(</span><span class="string">"bogons"</span><span class="special">,</span> <span class="identifier">range</span><span class="special">(&amp;</span><span class="identifier">F</span><span class="special">::</span><span class="identifier">b_begin</span><span class="special">,</span> <span class="special">&amp;</span><span class="identifier">F</span><span class="special">::</span><span class="identifier">b_end</span><span class="special">));</span>
</pre>
<p>
      <span class="bold"><strong>stl_input_iterator</strong></span>
    </p>
<p>
      So far, we have seen how to expose C++ iterators and ranges to Python. Sometimes
      we wish to go the other way, though: we'd like to pass a Python sequence to
      an STL algorithm or use it to initialize an STL container. We need to make
      a Python iterator look like an STL iterator. For that, we use <code class="computeroutput"><span class="identifier">stl_input_iterator</span><span class="special">&lt;&gt;</span></code>.
      Consider how we might implement a function that exposes <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">list</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">assign</span><span class="special">()</span></code> to Python:
    </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">list_assign</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;&amp;</span> <span class="identifier">l</span><span class="special">,</span> <span class="identifier">object</span> <span class="identifier">o</span><span class="special">)</span> <span class="special">{</span>
    <span class="comment">// Turn a Python sequence into an STL input range</span>
    <span class="identifier">stl_input_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">begin</span><span class="special">(</span><span class="identifier">o</span><span class="special">),</span> <span class="identifier">end</span><span class="special">;</span>
    <span class="identifier">l</span><span class="special">.</span><span class="identifier">assign</span><span class="special">(</span><span class="identifier">begin</span><span class="special">,</span> <span class="identifier">end</span><span class="special">);</span>
<span class="special">}</span>

<span class="comment">// Part of the wrapper for list&lt;int&gt;</span>
<span class="identifier">class_</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">list</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;(</span><span class="string">"list_int"</span><span class="special">)</span>
    <span class="special">.</span><span class="identifier">def</span><span class="special">(</span><span class="string">"assign"</span><span class="special">,</span> <span class="special">&amp;</span><span class="identifier">list_assign</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;)</span>
    <span class="comment">// ...</span>
    <span class="special">;</span>
</pre>
<p>
      Now in Python, we can assign any integer sequence to <code class="computeroutput"><span class="identifier">list_int</span></code>
      objects:
    </p>
<pre class="programlisting"><span class="identifier">x</span> <span class="special">=</span> <span class="identifier">list_int</span><span class="special">();</span>
<span class="identifier">x</span><span class="special">.</span><span class="identifier">assign</span><span class="special">([</span><span class="number">1</span><span class="special">,</span><span class="number">2</span><span class="special">,</span><span class="number">3</span><span class="special">,</span><span class="number">4</span><span class="special">,</span><span class="number">5</span><span class="special">])</span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2002-2005 Joel
      de Guzman, David Abrahams<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="embedding.html"><img src="../../images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../images/home.png" alt="Home"></a><a accesskey="n" href="exception.html"><img src="../../images/next.png" alt="Next"></a>
</div>
</body>
</html>
