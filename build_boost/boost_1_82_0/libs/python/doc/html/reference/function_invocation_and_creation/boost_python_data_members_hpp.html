<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>boost/python/data_members.hpp</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Boost.Python Reference Manual">
<link rel="up" href="../function_invocation_and_creation.html" title="Chapter 4. Function Invocation and Creation">
<link rel="prev" href="boost_python_call_method_hpp.html" title="boost/python/call_method.hpp">
<link rel="next" href="boost_python_make_function_hpp.html" title="boost/python/make_function.hpp">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="" width="" height="" src="../../images/boost.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost_python_call_method_hpp.html"><img src="../../images/prev.png" alt="Prev"></a><a accesskey="u" href="../function_invocation_and_creation.html"><img src="../../images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../images/home.png" alt="Home"></a><a accesskey="n" href="boost_python_make_function_hpp.html"><img src="../../images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="function_invocation_and_creation.boost_python_data_members_hpp"></a><a class="link" href="boost_python_data_members_hpp.html" title="boost/python/data_members.hpp">boost/python/data_members.hpp</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="boost_python_data_members_hpp.html#function_invocation_and_creation.boost_python_data_members_hpp.introduction">Introduction</a></span></dt>
<dt><span class="section"><a href="boost_python_data_members_hpp.html#function_invocation_and_creation.boost_python_data_members_hpp.functions">Functions</a></span></dt>
<dt><span class="section"><a href="boost_python_data_members_hpp.html#function_invocation_and_creation.boost_python_data_members_hpp.example">Example</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="function_invocation_and_creation.boost_python_data_members_hpp.introduction"></a><a class="link" href="boost_python_data_members_hpp.html#function_invocation_and_creation.boost_python_data_members_hpp.introduction" title="Introduction">Introduction</a>
</h3></div></div></div>
<p>
          <code class="computeroutput"><span class="identifier">make_getter</span><span class="special">()</span></code>
          and <code class="computeroutput"><span class="identifier">make_setter</span><span class="special">()</span></code>
          are the functions used internally by <a class="link" href="../high_level_components.html#high_level_components.boost_python_class_hpp.class_template_class_t_bases_hel.class_template_class_modifier_fu" title="Class template class_ modifier functions"><code class="computeroutput"><span class="identifier">class_</span><span class="special">&lt;&gt;::</span><span class="identifier">def_readonly</span></code></a> and <a class="link" href="../high_level_components.html#high_level_components.boost_python_class_hpp.class_template_class_t_bases_hel.class_template_class_modifier_fu" title="Class template class_ modifier functions"><code class="computeroutput"><span class="identifier">class_</span><span class="special">&lt;&gt;::</span><span class="identifier">def_readwrite</span></code></a> to produce Python
          callable objects which wrap C++ data members.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="function_invocation_and_creation.boost_python_data_members_hpp.functions"></a><a class="link" href="boost_python_data_members_hpp.html#function_invocation_and_creation.boost_python_data_members_hpp.functions" title="Functions">Functions</a>
</h3></div></div></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">C</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_getter</span><span class="special">(</span><span class="identifier">D</span> <span class="identifier">C</span><span class="special">::*</span><span class="identifier">pm</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">C</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">D</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Policies</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_getter</span><span class="special">(</span><span class="identifier">D</span> <span class="identifier">C</span><span class="special">::*</span><span class="identifier">pm</span><span class="special">,</span> <span class="identifier">Policies</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">policies</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires</span></dt>
<dd><p>
                Policies is a model of <a class="link" href="../concepts.html#concepts.callpolicies" title="CallPolicies"><code class="computeroutput"><span class="identifier">CallPolicies</span></code></a>.
              </p></dd>
<dt><span class="term">Effects</span></dt>
<dd><p>
                Creates a Python callable object which accepts a single argument
                that can be converted from_python to C*, and returns the corresponding
                member D member of the C object, converted to_python. If policies
                is supplied, it will be applied to the function as described here.
                Otherwise, the library attempts to determine whether D is a user-defined
                class type, and if so uses return_internal_reference&lt;&gt; for
                Policies. Note that this test may inappropriately choose return_internal_reference&lt;&gt;
                in some cases when D is a smart pointer type. This is a known defect.
              </p></dd>
<dt><span class="term">Returns</span></dt>
<dd><p>
                An instance of object which holds the new Python callable object.
              </p></dd>
</dl>
</div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_getter</span><span class="special">(</span><span class="identifier">D</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">d</span><span class="special">);</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">D</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Policies</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_getter</span><span class="special">(</span><span class="identifier">D</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">d</span><span class="special">,</span> <span class="identifier">Policies</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">policies</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_getter</span><span class="special">(</span><span class="identifier">D</span> <span class="keyword">const</span><span class="special">*</span> <span class="identifier">p</span><span class="special">);</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">D</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Policies</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_getter</span><span class="special">(</span><span class="identifier">D</span> <span class="keyword">const</span><span class="special">*</span> <span class="identifier">p</span><span class="special">,</span> <span class="identifier">Policies</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">policies</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires</span></dt>
<dd><p>
                Policies is a model of CallPolicies.
              </p></dd>
<dt><span class="term">Effects</span></dt>
<dd><p>
                Creates a Python callable object which accepts no arguments and returns
                d or *p, converted to_python on demand. If policies is supplied,
                it will be applied to the function as described here. Otherwise,
                the library attempts to determine whether D is a user-defined class
                type, and if so uses reference_existing_object for Policies.
              </p></dd>
<dt><span class="term">Returns</span></dt>
<dd><p>
                An instance of object which holds the new Python callable object.
              </p></dd>
</dl>
</div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">C</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_setter</span><span class="special">(</span><span class="identifier">D</span> <span class="identifier">C</span><span class="special">::*</span><span class="identifier">pm</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">C</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">D</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Policies</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_setter</span><span class="special">(</span><span class="identifier">D</span> <span class="identifier">C</span><span class="special">::*</span><span class="identifier">pm</span><span class="special">,</span> <span class="identifier">Policies</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">policies</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires</span></dt>
<dd><p>
                Policies is a model of CallPolicies.
              </p></dd>
<dt><span class="term">Effects</span></dt>
<dd><p>
                Creates a Python callable object which, when called from Python,
                expects two arguments which can be converted from_python to C* and
                D const&amp;, respectively, and sets the corresponding D member of
                the C object. If policies is supplied, it will be applied to the
                function as described here.
              </p></dd>
<dt><span class="term">Returns</span></dt>
<dd><p>
                An instance of object which holds the new Python callable object.
              </p></dd>
</dl>
</div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_setter</span><span class="special">(</span><span class="identifier">D</span><span class="special">&amp;</span> <span class="identifier">d</span><span class="special">);</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">D</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Policies</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_setter</span><span class="special">(</span><span class="identifier">D</span><span class="special">&amp;</span> <span class="identifier">d</span><span class="special">,</span> <span class="identifier">Policies</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">policies</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_setter</span><span class="special">(</span><span class="identifier">D</span><span class="special">*</span> <span class="identifier">p</span><span class="special">);</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">D</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Policies</span><span class="special">&gt;</span>
<span class="identifier">object</span> <span class="identifier">make_setter</span><span class="special">(</span><span class="identifier">D</span><span class="special">*</span> <span class="identifier">p</span><span class="special">,</span> <span class="identifier">Policies</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">policies</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires</span></dt>
<dd><p>
                Policies is a model of CallPolicies.
              </p></dd>
<dt><span class="term">Effects</span></dt>
<dd><p>
                Creates a Python callable object which accepts one argument, which
                is converted from Python to D const&amp; and written into d or *p,
                respectively. If policies is supplied, it will be applied to the
                function as described here.
              </p></dd>
<dt><span class="term">Returns</span></dt>
<dd><p>
                An instance of object which holds the new Python callable object.
              </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="function_invocation_and_creation.boost_python_data_members_hpp.example"></a><a class="link" href="boost_python_data_members_hpp.html#function_invocation_and_creation.boost_python_data_members_hpp.example" title="Example">Example</a>
</h3></div></div></div>
<p>
          The code below uses make_getter and make_setter to expose a data member
          as functions:
        </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">python</span><span class="special">/</span><span class="identifier">data_members</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">python</span><span class="special">/</span><span class="identifier">module</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">python</span><span class="special">/</span><span class="keyword">class</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">struct</span> <span class="identifier">X</span>
<span class="special">{</span>
    <span class="identifier">X</span><span class="special">(</span><span class="keyword">int</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">:</span> <span class="identifier">y</span><span class="special">(</span><span class="identifier">x</span><span class="special">)</span> <span class="special">{}</span>
    <span class="keyword">int</span> <span class="identifier">y</span><span class="special">;</span>
<span class="special">};</span>

<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">python</span><span class="special">;</span>

<span class="identifier">BOOST_PYTHON_MODULE_INIT</span><span class="special">(</span><span class="identifier">data_members_example</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">class_</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;(</span><span class="string">"X"</span><span class="special">,</span> <span class="identifier">init</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;())</span>
       <span class="special">.</span><span class="identifier">def</span><span class="special">(</span><span class="string">"get"</span><span class="special">,</span> <span class="identifier">make_getter</span><span class="special">(&amp;</span><span class="identifier">X</span><span class="special">::</span><span class="identifier">y</span><span class="special">))</span>
       <span class="special">.</span><span class="identifier">def</span><span class="special">(</span><span class="string">"set"</span><span class="special">,</span> <span class="identifier">make_setter</span><span class="special">(&amp;</span><span class="identifier">X</span><span class="special">::</span><span class="identifier">y</span><span class="special">))</span>
       <span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
          It can be used this way in Python:
        </p>
<pre class="programlisting"><span class="special">&gt;&gt;&gt;</span> <span class="identifier">from</span> <span class="identifier">data_members_example</span> <span class="identifier">import</span> <span class="special">*</span>
<span class="special">&gt;&gt;&gt;</span> <span class="identifier">x</span> <span class="special">=</span> <span class="identifier">X</span><span class="special">(</span><span class="number">1</span><span class="special">)</span>
<span class="special">&gt;&gt;&gt;</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">get</span><span class="special">()</span>
<span class="number">1</span>
<span class="special">&gt;&gt;&gt;</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">set</span><span class="special">(</span><span class="number">2</span><span class="special">)</span>
<span class="special">&gt;&gt;&gt;</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">get</span><span class="special">()</span>
<span class="number">2</span>
</pre>
</div>
</div>
<div class="copyright-footer">Copyright © 2002-2005, 2015 David Abrahams, Stefan Seefeld<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost_python_call_method_hpp.html"><img src="../../images/prev.png" alt="Prev"></a><a accesskey="u" href="../function_invocation_and_creation.html"><img src="../../images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../images/home.png" alt="Home"></a><a accesskey="n" href="boost_python_make_function_hpp.html"><img src="../../images/next.png" alt="Next"></a>
</div>
</body>
</html>
