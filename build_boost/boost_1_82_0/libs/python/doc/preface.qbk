[preface Introduction
[quickbook 1.6]
]

[section Synopsis]

Welcome to version 2 of Boost.Python, a C++ library which enables seamless interoperability between C++ and the Python programming language. The new version has been rewritten from the ground up, with a more convenient and flexible interface, and many new capabilities, including support for:

    * References and Pointers
    * Globally Registered Type Coercions
    * Automatic Cross-Module Type Conversions
    * Efficient Function Overloading
    * C++ to Python Exception Translation
    * Default Arguments
    * Keyword Arguments
    * Manipulating Python objects in C++
    * Exporting C++ Iterators as Python Iterators
    * Documentation Strings

The development of these features was funded in part by grants to Boost Consulting from the Lawrence Livermore National Laboratories and by the Computational Crystallography Initiative at Lawrence Berkeley National Laboratories. 

[endsect]
[section Articles]

"Building Hybrid Systems With Boost Python", by <PERSON> and <PERSON><PERSON> (PDF)
[endsect]
