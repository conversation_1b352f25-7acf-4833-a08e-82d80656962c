# Generated by `boostdep --cmake poly_collection`
# Copyright 2020 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.5...3.16)

project(boost_poly_collection VERSION "${BOOST_SUPERPROJECT_VERSION}" LANGUAGES CXX)

add_library(boost_poly_collection INTERFACE)
add_library(Boost::poly_collection ALIAS boost_poly_collection)

target_include_directories(boost_poly_collection INTERFACE include)

target_link_libraries(boost_poly_collection
  INTERFACE
    Boost::assert
    Boost::config
    Boost::core
    Boost::iterator
    Boost::mp11
    Boost::mpl
    Boost::type_erasure
    Boost::type_traits
)

if(BUILD_TESTING AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")

  add_subdirectory(test)

endif()

