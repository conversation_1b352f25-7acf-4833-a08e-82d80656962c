[/
    Boost.Config

    Copyright (c) 2001 Beman <PERSON>wes
    Copyright (c) 2001 Vesa <PERSON>
    Copyright (c) 2001 <PERSON>

    Distributed under the Boost Software License, Version 1.0.
    (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt)
]


[section Configuring <PERSON>ost for Your Platform]


[section Using the default boost configuration]

<PERSON>ost comes already configured for most common compilers and platforms; you
should be able to use boost "as is". Since the compiler is configured
separately from the standard library, the default configuration should work
even if you replace the compiler's standard library with a third-party
standard library (like __STL_PORT__).

Using boost "as is" without trying to reconfigure is the recommended method
for using boost. You can, however, run the configure script if you want to,
and there are regression tests provided that allow you to test the current
boost configuration with your particular compiler setup.

Boost library users can request support for additional compilers or platforms
by visiting our __BOOST_TRACKER__ and submitting a support request.

[endsect]

[section The <boost/config.hpp> header]

Boost library implementations access configuration macros via

    #include ``__BOOST_CONFIG_HEADER__``

While Boost library users are not required to include that file directly, or
use those configuration macros, such use is acceptable. The configuration
macros are documented as to their purpose, usage, and limitations which makes
them usable by both Boost library and user code.

Boost [link config_info_macros informational] or [link config_helpers helper]
macros are designed for use by Boost users as well as for our own internal use.
Note however, that the [link config_features feature test] and
[link config_defects defect test] macros were designed for internal use by
Boost libraries, not user code, so they can change at any time (though no
gratuitous changes are made to them). Boost library problems resulting from
changes to the configuration macros are caught by the Boost regression tests,
so the Boost libraries are updated to account for those changes. By contrast,
Boost library user code can be adversely affected by changes to the macros
without warning. The best way to keep abreast of changes to the macros used in
user code is to monitor the discussions on the Boost developers list.

[endsect]

[#config_config_script]

[section Using the configure script]

[important
This configure script only sets up the Boost headers for use with a particular
compiler. It has no effect on Boost.Build, or how the libraries are built.
]

If you know that boost is incorrectly configured for your particular setup, and
you are on a UNIX like platform, then you may want to try and improve things by
running the boost configure script. From a shell command prompt you will need to
cd into ['<boost-root>]`/libs/config/` and type:

[: `sh ./configure` ]

you will see a list of the items being checked as the script works its way
through the regression tests. Note that the configure script only really
auto-detects your compiler if it's called g++, c++ or CC. If you are using
some other compiler you will need to set one or more of the following
environment variables:


[table
[[Variable][Description                                       ]]
[[CXX     ][The name of the compiler, for example `c++`.      ]]
[[CXXFLAGS][The compiler flags to use, for example `-O2`.     ]]
[[LDFLAGS ][The linker flags to use, for example `-L/mypath`. ]]
[[LIBS    ][Any libraries to link in, for example `-lpthread`.]]
]

For example to run the configure script with HP aCC, you might use something
like:

    export CXX="aCC"
    export CXXFLAGS="-Aa -DAportable -D__HPACC_THREAD_SAFE_RB_TREE \
       -DRWSTD_MULTI_THREAD -DRW_MULTI_THREAD -D_REENTRANT -D_THREAD_SAFE"
    export LDFLAGS="-DAportable"
    export LIBS="-lpthread" 
    sh ./configure

However you run the configure script, when it finishes you will find a
new header -`user.hpp`- located in the ['<boost-root>]`/libs/config/`
directory. [*Note that configure does not install this header into your
boost include path by default]. This header contains all the options
generated by the configure script, plus a header-section that contains
the user settable options from the default version of
__BOOST_CONFIG_USER_HEADER__ (located under __BOOST_CONFIG_DIR__).
There are two ways you can use this header:

* [*Option 1:] copy the header into __BOOST_CONFIG_DIR__ so that it replaces
the default user.hpp provided by boost. This option allows only one
configure-generated setup; boost developers should avoid this option,
as it incurs the danger of accidentally committing a configure-modified
__BOOST_CONFIG_USER_HEADER__ to the svn repository (something you will not
be thanked for!).

* [*Option 2:]  give the header a more memorable name, and place it somewhere
convenient; then, define the macro `BOOST_USER_CONFIG` to point to it. For
example create a new sub-directory __BOOST_CONFIG_DIR__ `user/`, and copy
the header there; for example as `multithread-gcc-config.hpp`. Then, when
compiling add the command line option:
`-DBOOST_USER_CONFIG="<boost/config/user/multithread-gcc-config.hpp>"`, and
boost will use the new configuration header. This option allows you to
generate more than one configuration header, and to keep them separate
from the boost source - so that updates to the source do not interfere
with your configuration.

[endsect]

[#config_user_settable]

[section User settable options]

There are some configuration-options that represent user choices, rather
than compiler defects or platform specific options. These are listed in
`<boost/config/user.hpp>` and at the start of a configure-generated `user.hpp`
header. You can define these on the command line, or by editing
`<boost/config/user.hpp>`, they are listed in the following table: 



[table

[[Macro         ][Description       ]]

[[`BOOST_USER_CONFIG`][
When defined, it should point to the name of the user configuration file
to include prior to any boost configuration files. When not defined,
defaults to [@../../../../boost/config/user.hpp `<boost/config/user.hpp>`].
]]
[[`BOOST_COMPILER_CONFIG`][
When defined, it should point to the name of the compiler configuration
file to use. Defining this cuts out the compiler selection logic, and
eliminates the dependency on the header containing that logic. For
example if you are using gcc, then you could define BOOST_COMPILER_CONFIG
to [@../../../../boost/config/compiler/gcc.hpp `<boost/config/compiler/gcc.hpp>`].
]]
[[`BOOST_STDLIB_CONFIG`][
When defined, it should point to the name of the standard library
configuration file to use. Defining this cuts out the standard library
selection logic, and eliminates the dependency on the header containing
that logic. For example if you are using STLport, then you could define
`BOOST_STDLIB_CONFIG` to
[@../../../../boost/config/stdlib/stlport.hpp `<boost/config/stdlib/stlport.hpp>`].
]]
[[`BOOST_PLATFORM_CONFIG`][
When defined, it should point to the name of the platform configuration
file to use. Defining this cuts out the platform selection logic, and
eliminates the dependency on the header containing that logic. For example
if you are compiling on linux, then you could define `BOOST_PLATFORM_CONFIG`
to [@../../../../boost/config/platform/linux.hpp `<boost/config/platform/linux.hpp>`].
]]
[[`BOOST_NO_COMPILER_CONFIG`][
When defined, no compiler configuration file is selected or included,
define when the compiler is fully conformant with the standard, or where
the user header (see `BOOST_USER_CONFIG`), has had any options necessary
added to it, for example by an autoconf generated configure script.
]]
[[`BOOST_NO_STDLIB_CONFIG` ][
When defined, no standard library configuration file is selected or included,
define when the standard library is fully conformant with the standard, or
where the user header (see `BOOST_USER_CONFIG`), has had any options necessary
added to it, for example by an autoconf generated configure script.
]]
[[`BOOST_NO_PLATFORM_CONFIG` ][
When defined, no platform configuration file is selected or included,
define when the platform is fully conformant with the standard (and has
no useful extra features), or where the user header (see
`BOOST_USER_CONFIG`), has had any options necessary added to it, for example
by an autoconf generated configure script.
]]
[[`BOOST_NO_CONFIG` ][
Equivalent to defining all of `BOOST_NO_COMPILER_CONFIG`,
`BOOST_NO_STDLIB_CONFIG` and `BOOST_NO_PLATFORM_CONFIG`.
]]
[[`BOOST_STRICT_CONFIG` ][
The normal behavior for compiler versions that are newer than the last
known version, is to assume that they have all the same defects as the
last known version. By setting this define, then compiler versions that
are newer than the last known version are assumed to be fully conforming
with the standard. This is probably most useful for boost developers or
testers, and for those who want to use boost to test beta compiler versions.
]]
[[`BOOST_ASSERT_CONFIG` ][
When this flag is set, if the config finds anything unknown, then it will
stop with a #error rather than continue. Boost regression testers should
set this define, as should anyone who wants to quickly check whether boost
is supported on their platform.
]]
[[`BOOST_DISABLE_THREADS` ][
When defined, disables threading support, even if the compiler in its
current translation mode supports multiple threads.
]]
[[`BOOST_DISABLE_WIN32` ][
When defined, disables the use of Win32 specific API's, even when these
are available. Also has the effect of setting `BOOST_DISABLE_THREADS` unless
`BOOST_HAS_PTHREADS` is set. This option may be set automatically by the
config system when it detects that the compiler is in "strict mode".
]]
[[`BOOST_DISABLE_ABI_HEADERS`][
Stops boost headers from including any prefix/suffix headers that normally
control things like struct packing and alignment.
]]
[[`BOOST_ABI_PREFIX`][
A prefix header to include in place of whatever boost.config would normally
select, any replacement should set up struct packing and alignment options
as required.
]]
[[`BOOST_ABI_SUFFIX` ][
A suffix header to include in place of whatever boost.config would normally
select, any replacement should undo the effects of the prefix header.
]]
[[`BOOST_ALL_DYN_LINK`][
Forces all libraries that have separate source, to be linked as dll's rather
than static libraries on Microsoft Windows (this macro is used to turn on
`__declspec(dllimport)` modifiers, so that the compiler knows which symbols
to look for in a dll rather than in a static library).
Note that there may be some libraries that can only be statically linked
(Boost.Test for example) and others which may only be dynamically linked
(Boost.Thread for example), in these cases this macro has no effect.
]]
[[`BOOST_`['WHATEVER]`_DYN_LINK`][
Forces library "whatever" to be linked as a dll rather than a static library
on Microsoft Windows: replace the ['WHATEVER] part of the macro name with the
name of the library that you want to dynamically link to, for example use
`BOOST_DATE_TIME_DYN_LINK` or `BOOST_REGEX_DYN_LINK` etc (this macro is used
to turn on `__declspec(dllimport)` modifiers, so that the compiler knows
which symbols to look for in a dll rather than in a static library).
Note that there may be some libraries that can only be statically linked
(Boost.Test for example) and others which may only be dynamically linked
(Boost.Thread for example), in these cases this macro is unsupported.
]]
[[`BOOST_ALL_NO_LIB`][
Tells the config system not to automatically select which libraries to link
against.
Normally if a compiler supports #pragma lib, then the correct library build
variant will be automatically selected and linked against, simply by the act
of including one of that library's headers. This macro turns that
feature off.
]]
[[`BOOST_`['WHATEVER]`_NO_LIB`][
Tells the config system not to automatically select which library to link
against for library "whatever", replace ['WHATEVER] in the macro name with the
name of the library; for example `BOOST_DATE_TIME_NO_LIB` or `BOOST_REGEX_NO_LIB`.
Normally if a compiler supports `#pragma lib`, then the correct library build
variant will be automatically selected and linked against, simply by the
act of including one of that library's headers.  This macro turns that
feature off.
]]
[[`BOOST_LIB_DIAGNOSTIC`][
Causes the auto-linking code to output diagnostic messages indicating the
name of the library that is selected for linking.
]]
[[`BOOST_LIB_BUILDID`][
If you built Boost using the `--buildid` option then set this macro to the same value
as you passed to bjam.  For example if you built using `bjam address-model=64 --buildid=amd64` 
then compile your code with `-DBOOST_LIB_BUILDID=amd64` to ensure the correct libraries
are selected at link time.
]]
[[`BOOST_LIB_TOOLSET`][
Overrides the name of the toolset part of the name of library being linked
to; note if defined this must be defined to a quoted string literal, for
example "abc".
]]
]

[endsect]

[section Advanced configuration usage]

By setting various macros on the compiler command line or by editing
__BOOST_CONFIG_USER_HEADER__, the boost configuration setup can be optimised
in a variety of ways.

Boost's configuration is structured so that the user-configuration is
included first (defaulting to __BOOST_CONFIG_USER_HEADER__ if `BOOST_USER_CONFIG`
is not defined). This sets up any user-defined policies, and gives the
user-configuration a chance to influence what happens next.

Next the compiler, standard library, and platform configuration files are
included. These are included via macros (`BOOST_COMPILER_CONFIG` etc,
[link config_user_settable see user settable macros]), and if the corresponding
macro is undefined then a separate header that detects which compiler/standard
library/platform is in use is included in order to set these. The config
can be told to ignore these headers altogether if the corresponding
`BOOST_NO_XXX` macro is set (for example `BOOST_NO_COMPILER_CONFIG` to
disable including any compiler configuration file -
[link config_user_settable see user settable macros]).

Finally the boost configuration header, includes __BOOST_CONFIG_SUFFIX_HEADER__;
this header contains any boiler plate configuration code - for example where one
boost macro being set implies that another must be set also.

The following usage examples represent just a few of the possibilities:

[section Example 1: creating our own frozen configuration]

Lets suppose that we're building boost with Visual C++ 6, and STLport 4.0. Lets
suppose also that we don't intend to update our compiler or standard library
any time soon. In order to avoid breaking dependencies when we update boost,
we may want to "freeze" our configuration headers, so that we only have to
rebuild our project if the boost code itself has changed, and not because the
boost config has been updated for more recent versions of Visual C++ or STLport.
We'll start by realising that the configuration files in use are:
[@../../../../boost/config/compiler/visualc.hpp `<boost/config/compiler/visualc.hpp>`]
for the compiler,
[@../../../../boost/config/stdlib/stlport.hpp `<boost/config/stdlib/stlport.hpp>`]
for the standard library, and
[@../../../../boost/config/platform/win32.hpp `<boost/config/platform/win32.hpp>`]
for the platform. Next we'll create our own private configuration directory:
`boost/config/mysetup/`, and copy the configuration files into there. Finally,
open up __BOOST_CONFIG_USER_HEADER__ and edit the following defines:

    #define BOOST_COMPILER_CONFIG "boost/config/mysetup/visualc.hpp"
    #define BOOST_STDLIB_CONFIG "boost/config/mysetup/stlport.hpp"
    #define BOOST_USER_CONFIG "boost/config/mysetup/win32.hpp"

Now when you use boost, its configuration header will go straight to our "frozen"
versions, and ignore the default versions, you will now be insulated from any
configuration changes when you update boost. This technique is also useful if
you want to modify some of the boost configuration files; for example if you are
working with a beta compiler release not yet supported by boost.

[endsect]

[section Example 2: skipping files that you don't need]

Lets suppose that you're using boost with a compiler that is fully conformant with
the standard; you're not interested in the fact that older versions of your compiler
may have had bugs, because you know that your current version does not need any
configuration macros setting. In a case like this, you can define
`BOOST_NO_COMPILER_CONFIG` either on the command line, or in __BOOST_CONFIG_USER_HEADER__,
and miss out the compiler configuration header altogether (actually you miss out
two headers, one which works out what the compiler is, and one that configures
boost for it). This has two consequences: the first is that less code has to be
compiled, and the second that you have removed a dependency on two boost headers.

[endsect]

[section Example 3: using configure script to freeze the boost configuration]

If you are working on a unix-like platform then you can use the configure script to
generate a "frozen" configuration based on your current compiler setup -
[link config_config_script see using the configure script for more details].

[endsect]

[endsect]

[section Testing the boost configuration]

The boost configuration library provides a full set of regression test programs
under the __BOOST_CONFIG_DIR__ `test/` sub-directory:


[table
[[File][Description]]
[[`config_info.cpp`][
Prints out a detailed description of your compiler/standard library/platform
setup, plus your current boost configuration. The information provided by this
program is useful in setting up the boost configuration files. If you report that
boost is incorrectly configured for your compiler/library/platform then please
include the output from this program when reporting the changes required.
]]
[[`config_test.cpp`][
A monolithic test program that includes most of the individual test cases.
This provides a quick check to see if boost is correctly configured for your
compiler/library/platform.
]]
[[`limits_test.cpp`][
Tests your standard library's `std::numeric_limits` implementation (or its boost
provided replacement if `BOOST_NO_LIMITS` is defined). This test file fails with
most versions of numeric_limits, mainly due to the way that some compilers
treat NAN's and infinity.
]]
[[`no_*pass.cpp`][
Individual compiler defect test files. Each of these should compile, if one
does not then the corresponding `BOOST_NO_XXX` macro needs to be defined - see
each test file for specific details.
]]
[[`no_*fail.cpp`][
Individual compiler defect test files. Each of these should not compile, if
one does then the corresponding `BOOST_NO_XXX` macro is defined when it need
not be - see each test file for specific details.
]]
[[`has_*pass.cpp`][
Individual feature test files. If one of these does not compile then the
corresponding `BOOST_HAS_XXX` macro is defined when it should not be - see
each test file for specific details.
]]
[[`has_*fail.cpp`][
Individual feature test files. If one of these does compile then the
corresponding `BOOST_HAS_XXX` macro can be safely defined - see each test
file for specific details.
]]
]

Although you can run the configuration regression tests as individual test
files, there are rather a lot of them, so there are a couple of shortcuts to
help you out:

Alternatively you can run the configure script like this:

[: `./configure --enable-test`]

in which case the script will test the current configuration rather than
creating a new one from scratch.

If you are reporting the results of these tests for a new
platform/library/compiler then please include a log of the full compiler output,
the output from `config_info.cpp`, and the pass/fail test results.


[endsect]

[endsect]

