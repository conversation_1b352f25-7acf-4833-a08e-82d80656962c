//  (C) Copyright <PERSON> 2021

//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/config for more information.

//  MACRO:         BOOST_NO_CXX03
//  TITLE:         C++03
//  DESCRIPTION:   C++03 non-conformance

#include <boost/config/assert_cxx03.hpp>

namespace boost_no_cxx03 {

int test()
{
  return 0;
}

}
