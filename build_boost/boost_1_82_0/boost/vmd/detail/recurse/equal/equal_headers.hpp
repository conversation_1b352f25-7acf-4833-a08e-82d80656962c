
//  (C) Copyright <PERSON> 2011-2015
//  Use, modification and distribution are subject to the Boost Software License,
//  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt).

#if !defined(BOOST_VMD_DETAIL_EQUAL_HEADERS_HPP)
#define BOOST_VMD_DETAIL_EQUAL_HEADERS_HPP

#include <boost/preprocessor/control/iif.hpp>
#include <boost/preprocessor/logical/bitor.hpp>
#include <boost/preprocessor/logical/bitand.hpp>
#include <boost/vmd/to_seq.hpp>
#include <boost/vmd/get_type.hpp>
#include <boost/vmd/identity.hpp>
#include <boost/vmd/is_empty.hpp>
#include <boost/vmd/detail/adjust_tuple_type.hpp>
#include <boost/vmd/detail/data_equal.hpp>
#include <boost/vmd/detail/equal_common.hpp>
#include <boost/vmd/detail/equal_type.hpp>
#include <boost/vmd/detail/modifiers.hpp>

#endif /* BOOST_VMD_DETAIL_EQUAL_HEADERS_HPP */
