/*==============================================================================
    Copyright (c) 2005-2010 <PERSON>
    Copyright (c) 2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
    
    
    
    
    
    
    
        template <
            typename Try
            
            
        >
        struct try_catch<
            Try
            
            
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
               
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0
        >
        struct try_catch<
            Try
            ,
            A0
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1
        >
        struct try_catch<
            Try
            ,
            A0 , A1
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9
            >
        {};
     
