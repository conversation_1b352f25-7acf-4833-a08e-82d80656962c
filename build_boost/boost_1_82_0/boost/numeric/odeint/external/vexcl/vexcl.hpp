/*
  [auto_generated]
  boost/numeric/odeint/external/vexcl/vexcl.hpp

  [begin_description]
  includes all headers required for using vexcl in odeint
  [end_description]

  Copyright 2013 <PERSON><PERSON>t
  Copyright 2013 <PERSON>

  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or
  copy at http://www.boost.org/LICENSE_1_0.txt)
*/


#ifndef BOOST_NUMERIC_ODEINT_EXTERNAL_VEXCL_VEXCL_HPP_DEFINED
#define BOOST_NUMERIC_ODEINT_EXTERNAL_VEXCL_VEXCL_HPP_DEFINED

#include <boost/numeric/odeint/external/vexcl/vexcl_algebra_dispatcher.hpp>
#include <boost/numeric/odeint/external/vexcl/vexcl_resize.hpp>
#include <boost/numeric/odeint/external/vexcl/vexcl_same_instance.hpp>
#include <boost/numeric/odeint/external/vexcl/vexcl_norm_inf.hpp>
#include <boost/numeric/odeint/external/vexcl/vexcl_abs.hpp>
#include <boost/numeric/odeint/external/vexcl/vexcl_copy.hpp>

#endif // BOOST_NUMERIC_ODEINT_EXTERNAL_VEXCL_VEXCL_HPP_DEFINED
