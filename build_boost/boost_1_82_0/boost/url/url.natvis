<?xml version="1.0" encoding="utf-8"?>
<AutoVisualizer xmlns="http://schemas.microsoft.com/vstudio/debugger/natvis/2010">

<!--
-->
  <Type Name="boost::urls::authority_view" Inheritable="true">
    <DisplayString>{ u_ }</DisplayString>
      <Expand>
          <ExpandedItem>u_</ExpandedItem>
      </Expand>
  </Type>

  <Type Name="boost::urls::ipv4_address">
    <DisplayString>{(addr_>>24)}.{(addr_>>16)%256}.{(addr_>>8)%256}.{addr_%256}</DisplayString>
  </Type>

  <Type Name="boost::urls::decode_view">
    <DisplayString Condition="dn_==n_">{p_,[n_]s}</DisplayString>
    <DisplayString Condition="dn_!=n_">{p_,[n_]s} ({dn_})</DisplayString>
  </Type>

  <Type Name="boost::urls::param">
    <DisplayString Condition="! has_value">{key}</DisplayString>
    <DisplayString Condition="has_value">{key}={value}</DisplayString>
  </Type>

  <Type Name="boost::urls::param_view">
    <DisplayString Condition="! has_value">{key}</DisplayString>
    <DisplayString Condition="has_value">{key}={value}</DisplayString>
  </Type>

  <Type Name="boost::urls::param_pct_view">
    <DisplayString Condition="! has_value">{key}</DisplayString>
    <DisplayString Condition="has_value">{key}={value}</DisplayString>
  </Type>

  <Type Name="boost::urls::pct_string_view">
    <DisplayString Condition="dn_ != s_.n_">{s_} ({dn_})</DisplayString>
    <DisplayString>{s_}</DisplayString>
  </Type>

  <Type Name="boost::urls::detail::url_impl">
    <DisplayString>{cs_,[offset_[id_end]]s}</DisplayString>
    <Expand>
      <Synthetic Name="scheme" Condition="scheme_!=scheme::none">
        <DisplayString>{cs_,[offset_[id_user]-1]s} {scheme_}</DisplayString>
      </Synthetic>
      <Synthetic Name="user" Condition="from_ != 2 &amp;&amp; offset_[id_pass]-offset_[id_user]>2">
        <DisplayString>{cs_+offset_[id_user]+2,[offset_[id_pass]-offset_[id_user]-2]s} ({decoded_[id_user]})</DisplayString>
      </Synthetic>
      <Synthetic Name="user" Condition="from_ == 2 &amp;&amp; offset_[id_pass]-offset_[id_user]>0">
        <DisplayString>{cs_+offset_[id_user],[offset_[id_pass]-offset_[id_user]]s} ({decoded_[id_user]})</DisplayString>
      </Synthetic>
      <Synthetic Name="pass" Condition="offset_[id_host]-offset_[id_pass]>=2">
        <DisplayString>{cs_+offset_[id_pass]+1,[offset_[id_host]-offset_[id_pass]-2]s} ({decoded_[id_pass]})</DisplayString>
      </Synthetic>
      <Synthetic Name="host" Condition="from_ == 2 || offset_[id_pass]-offset_[id_user]>=2">
        <DisplayString Condition="host_type_==host_type::none">{host_type_}</DisplayString>
        <DisplayString Condition="host_type_!=host_type::none">{cs_+offset_[id_host],[offset_[id_port]-offset_[id_host]]s}</DisplayString>
      </Synthetic>
      <Synthetic Name="port" Condition="offset_[id_path]-offset_[id_port]>=1">
        <DisplayString>{cs_+offset_[id_port]+1,[offset_[id_path]-offset_[id_port]-1]s} = {port_number_}</DisplayString>
      </Synthetic>
      <Synthetic Name="path" Condition="from_ != 2">
        <DisplayString>{cs_+offset_[id_path],[offset_[id_query]-offset_[id_path]]s}</DisplayString>
      </Synthetic>
      <Synthetic Name="query" Condition="offset_[id_frag]-offset_[id_query]>=1">
        <DisplayString>{cs_+offset_[id_query]+1,[offset_[id_frag]-offset_[id_query]-1]s}</DisplayString>
      </Synthetic>
      <Synthetic Name="frag" Condition="offset_[id_end]-offset_[id_frag]>=1">
        <DisplayString>{cs_+offset_[id_frag]+1,[offset_[id_end]-offset_[id_frag]-1]s})</DisplayString>
      </Synthetic>
    </Expand>
  </Type>

  <Type Name="boost::urls::url_view_base" Inheritable="true">
      <DisplayString>{*pi_}</DisplayString>
      <Expand>
        <ExpandedItem>*pi_</ExpandedItem>    
      </Expand>
  </Type>

  <Type Name="boost::urls::grammar::range&lt;*&gt;">
    <DisplayString>{s_} [{n_}]</DisplayString>
  </Type>

  <Type Name="boost::urls::grammar::recycled_ptr&lt;*&gt;">
    <DisplayString Condition="p_ == nullptr">nullptr</DisplayString>
    <DisplayString>{p_->t}</DisplayString>
    <Expand>
      <Synthetic Name="[refs]" Condition="p_ != nullptr">
        <DisplayString>{p_->refs}</DisplayString>
      </Synthetic>
    </Expand>
  </Type>

</AutoVisualizer>
