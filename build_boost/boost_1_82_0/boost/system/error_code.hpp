#ifndef BOOST_SYSTEM_ERROR_CODE_HPP_INCLUDED
#define BOOST_SYSTEM_ERROR_CODE_HPP_INCLUDED

//  Copyright <PERSON><PERSON> 2006, 2007
//  Copyright <PERSON><PERSON> 2007
//  Copyright Peter <PERSON> 2017, 2018
//
//  Distributed under the Boost Software License, Version 1.0. (See accompanying
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//
//  See library home page at http://www.boost.org/libs/system

#include <boost/system/detail/error_code.hpp>
#include <boost/system/error_category.hpp>
#include <boost/system/error_condition.hpp>
#include <boost/system/errc.hpp>
#include <boost/system/generic_category.hpp>
#include <boost/system/system_category.hpp>
#include <boost/system/detail/throws.hpp>

#endif // BOOST_SYSTEM_ERROR_CODE_HPP_INCLUDED
