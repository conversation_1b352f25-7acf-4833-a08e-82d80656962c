// -- control_structures.hpp -- Boost Lambda Library --------------------------
// Copyright (C) 1999, 2000 <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
//
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)
//
// For more information, see www.boost.org

#ifndef BOOST_LAMBDA_CONTROL_STRUCTURES_HPP
#define BOOST_LAMBDA_CONTROL_STRUCTURES_HPP


#include "boost/lambda/core.hpp"

// Arithmetic type promotion needed for if_then_else_return
#include "boost/lambda/detail/operator_actions.hpp"
#include "boost/lambda/detail/operator_return_type_traits.hpp"

#include "boost/lambda/if.hpp"
#include "boost/lambda/loops.hpp"

#endif
