// Copyright (C) 2005-2006 <PERSON> <doug.gregor -at- gmail.com>.

// Use, modification and distribution is subject to the Boost Software
// License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

// Message Passing Interface 1.1 -- Section 4. MPI Collectives

/** @file collectives_fwd.hpp
 *
 *  This header provides forward declarations for all of the
 *  collective operations contained in the header @c collectives.hpp.
 */
#ifndef BOOST_MPI_COLLECTIVES_FWD_HPP
#define BOOST_MPI_COLLECTIVES_FWD_HPP

/// INTERNAL ONLY
#define BOOST_MPI_COLLECTIVES_FORWARD_ONLY
#include <boost/mpi/collectives.hpp>
#undef BOOST_MPI_COLLECTIVES_FORWARD_ONLY

#endif // BOOST_MPI_COLLECTIVES_FWD_HPP

