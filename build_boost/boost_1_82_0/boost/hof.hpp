/*=============================================================================
    Copyright (c) 2016 Paul Fultz II
    boost/hof.hpp
    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/

#ifndef BOOST_HOF_GUARD_BOOST_HOF_HPP
#define BOOST_HOF_GUARD_BOOST_HOF_HPP

#include <boost/hof/alias.hpp>
#include <boost/hof/always.hpp>
#include <boost/hof/apply_eval.hpp>
#include <boost/hof/apply.hpp>
#include <boost/hof/arg.hpp>
#include <boost/hof/proj.hpp>
#include <boost/hof/capture.hpp>
#include <boost/hof/combine.hpp>
#include <boost/hof/compose.hpp>
#include <boost/hof/fold.hpp>
#include <boost/hof/first_of.hpp>
#include <boost/hof/construct.hpp>
#include <boost/hof/decay.hpp>
#include <boost/hof/decorate.hpp>
#include <boost/hof/eval.hpp>
#include <boost/hof/fix.hpp>
#include <boost/hof/flip.hpp>
#include <boost/hof/flow.hpp>
#include <boost/hof/function.hpp>
#include <boost/hof/identity.hpp>
#include <boost/hof/if.hpp>
#include <boost/hof/implicit.hpp>
#include <boost/hof/indirect.hpp>
#include <boost/hof/infix.hpp>
#include <boost/hof/is_invocable.hpp>
#include <boost/hof/lambda.hpp>
#include <boost/hof/lazy.hpp>
#include <boost/hof/lift.hpp>
#include <boost/hof/limit.hpp>
#include <boost/hof/match.hpp>
#include <boost/hof/mutable.hpp>
#include <boost/hof/pack.hpp>
#include <boost/hof/partial.hpp>
#include <boost/hof/pipable.hpp>
#include <boost/hof/placeholders.hpp>
#include <boost/hof/protect.hpp>
#include <boost/hof/repeat.hpp>
#include <boost/hof/repeat_while.hpp>
#include <boost/hof/result.hpp>
#include <boost/hof/returns.hpp>
#include <boost/hof/reveal.hpp>
#include <boost/hof/reverse_fold.hpp>
#include <boost/hof/rotate.hpp>
#include <boost/hof/static.hpp>
#include <boost/hof/tap.hpp>
#include <boost/hof/unpack.hpp>


namespace boost { namespace hof {

}} // namespace boost::hof

#endif
