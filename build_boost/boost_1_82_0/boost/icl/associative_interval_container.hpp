/*-----------------------------------------------------------------------------+    
Copyright (c) 2010-2010: <PERSON>
+------------------------------------------------------------------------------+
   Distributed under the Boost Software License, Version 1.0.
      (See accompanying file LICENCE.txt or copy at
           http://www.boost.org/LICENSE_1_0.txt)
+-----------------------------------------------------------------------------*/
#ifndef BOOST_ICL_ASSOCIATIVE_INTERVAL_CONTAINER_HPP_JOFA_101023
#define BOOST_ICL_ASSOCIATIVE_INTERVAL_CONTAINER_HPP_JOFA_101023

#include <boost/icl/impl_config.hpp>
#include <boost/icl/concept/comparable.hpp>
#include <boost/icl/concept/joinable.hpp>
#include <boost/icl/concept/container.hpp>
#include <boost/icl/concept/interval_associator_base.hpp>
#include <boost/icl/concept/interval_set.hpp>
#include <boost/icl/concept/interval_map.hpp>
#include <boost/icl/concept/interval_associator.hpp>
#include <boost/icl/iterator.hpp>

#endif


