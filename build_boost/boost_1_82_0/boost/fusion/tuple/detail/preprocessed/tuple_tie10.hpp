/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    This is an auto-generated file. Do not edit!
==============================================================================*/
namespace boost { namespace fusion
{
    template <typename T0>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0&>
    tie(T0 & arg0)
    {
        return tuple<T0&>(
            arg0);
    }
    template <typename T0 , typename T1>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0& , T1&>
    tie(T0 & arg0 , T1 & arg1)
    {
        return tuple<T0& , T1&>(
            arg0 , arg1);
    }
    template <typename T0 , typename T1 , typename T2>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0& , T1& , T2&>
    tie(T0 & arg0 , T1 & arg1 , T2 & arg2)
    {
        return tuple<T0& , T1& , T2&>(
            arg0 , arg1 , arg2);
    }
    template <typename T0 , typename T1 , typename T2 , typename T3>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0& , T1& , T2& , T3&>
    tie(T0 & arg0 , T1 & arg1 , T2 & arg2 , T3 & arg3)
    {
        return tuple<T0& , T1& , T2& , T3&>(
            arg0 , arg1 , arg2 , arg3);
    }
    template <typename T0 , typename T1 , typename T2 , typename T3 , typename T4>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0& , T1& , T2& , T3& , T4&>
    tie(T0 & arg0 , T1 & arg1 , T2 & arg2 , T3 & arg3 , T4 & arg4)
    {
        return tuple<T0& , T1& , T2& , T3& , T4&>(
            arg0 , arg1 , arg2 , arg3 , arg4);
    }
    template <typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0& , T1& , T2& , T3& , T4& , T5&>
    tie(T0 & arg0 , T1 & arg1 , T2 & arg2 , T3 & arg3 , T4 & arg4 , T5 & arg5)
    {
        return tuple<T0& , T1& , T2& , T3& , T4& , T5&>(
            arg0 , arg1 , arg2 , arg3 , arg4 , arg5);
    }
    template <typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0& , T1& , T2& , T3& , T4& , T5& , T6&>
    tie(T0 & arg0 , T1 & arg1 , T2 & arg2 , T3 & arg3 , T4 & arg4 , T5 & arg5 , T6 & arg6)
    {
        return tuple<T0& , T1& , T2& , T3& , T4& , T5& , T6&>(
            arg0 , arg1 , arg2 , arg3 , arg4 , arg5 , arg6);
    }
    template <typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0& , T1& , T2& , T3& , T4& , T5& , T6& , T7&>
    tie(T0 & arg0 , T1 & arg1 , T2 & arg2 , T3 & arg3 , T4 & arg4 , T5 & arg5 , T6 & arg6 , T7 & arg7)
    {
        return tuple<T0& , T1& , T2& , T3& , T4& , T5& , T6& , T7&>(
            arg0 , arg1 , arg2 , arg3 , arg4 , arg5 , arg6 , arg7);
    }
    template <typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0& , T1& , T2& , T3& , T4& , T5& , T6& , T7& , T8&>
    tie(T0 & arg0 , T1 & arg1 , T2 & arg2 , T3 & arg3 , T4 & arg4 , T5 & arg5 , T6 & arg6 , T7 & arg7 , T8 & arg8)
    {
        return tuple<T0& , T1& , T2& , T3& , T4& , T5& , T6& , T7& , T8&>(
            arg0 , arg1 , arg2 , arg3 , arg4 , arg5 , arg6 , arg7 , arg8);
    }
    template <typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9>
    BOOST_FUSION_GPU_ENABLED
    inline tuple<T0& , T1& , T2& , T3& , T4& , T5& , T6& , T7& , T8& , T9&>
    tie(T0 & arg0 , T1 & arg1 , T2 & arg2 , T3 & arg3 , T4 & arg4 , T5 & arg5 , T6 & arg6 , T7 & arg7 , T8 & arg8 , T9 & arg9)
    {
        return tuple<T0& , T1& , T2& , T3& , T4& , T5& , T6& , T7& , T8& , T9&>(
            arg0 , arg1 , arg2 , arg3 , arg4 , arg5 , arg6 , arg7 , arg8 , arg9);
    }
}}
